# Server Configuration
PORT=3002
NODE_ENV=development

# Database Configuration
DB_HOST=**************
DB_PORT=3306
DB_USERNAME=jobadmin
DB_PASSWORD=jobadmin
DB_NAME=job-cms

CORS_ORIGINS=http://localhost:3000,http://localhost:3001,https://job-dev.tekai.vn,https://cms-dev.tekai.vn,https://api-dev.tekai.vn

# JWT Configuration
JWT_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
JWT_EXPIRATION=30d

# Auth Encryption Configuration
AUTH_SECRET=TekaiJobPortalSecretKey2025

# AWS Configuration (if needed)
AWS_ACCESS_KEY_ID=your-dev-access-key
AWS_SECRET_ACCESS_KEY=your-dev-secret-key
AWS_REGION=ap-southeast-1
AWS_BUCKET_NAME=job-portal-dev

# Email Configuration (if needed)
# Zoho Mail SMTP Configuration
SMTP_HOST=smtp.zoho.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=Tekai!2025
SMTP_FROM=<EMAIL>
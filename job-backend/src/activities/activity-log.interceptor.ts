import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { ActivityLogService } from './activity-log.service';
import { LOG_ACTIVITY_KEY, LogActivityOptions } from './activity-log.decorator';
import { LogLevel } from './entities/activity-log.entity';

@Injectable()
export class ActivityLogInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    private readonly activityLogService: ActivityLogService
  ) {}

  intercept(context: ExecutionContext, next: Call<PERSON>and<PERSON>): Observable<any> {
    const logOptions = this.reflector.get<LogActivityOptions>(
      LOG_ACTIVITY_KEY,
      context.getHandler()
    );

    if (!logOptions) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    const logContext = {
      userId: user?.userId,
      userEmail: user?.email,
      ipAddress: request.ip,
      userAgent: request.headers['user-agent'],
      sessionId: request.sessionID,
      resourceType: logOptions.resourceType,
      resourceId: request.params?.id,
      metadata: {
        ...(logOptions.includeBody && { requestBody: request.body }),
        method: request.method,
        url: request.url
      }
    };

    return next.handle().pipe(
      tap(async (result) => {
        try {
          const metadata = {
            ...logContext.metadata,
            ...(logOptions.includeResult && { result })
          };

          await this.activityLogService.log(
            logOptions.actionType,
            logOptions.action,
            { ...logContext, metadata },
            LogLevel.INFO,
            `${logOptions.action} completed successfully`
          );
        } catch (error) {
          // Silent fail for logging
        }
      }),
      catchError(async (error) => {
        try {
          await this.activityLogService.logError(
            logOptions.action,
            error,
            logContext
          );
        } catch (logError) {
          // Silent fail for logging
        }
        throw error;
      })
    );
  }
}

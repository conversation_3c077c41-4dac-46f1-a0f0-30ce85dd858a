import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ActivityLogService } from './activity-log.service';
import { ActivityLogQueryDto } from './dto/activity-log.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('activity-logs')
@UseGuards(JwtAuthGuard)
export class ActivityLogController {
  constructor(private readonly activityLogService: ActivityLogService) {}

  @Get()
  async getActivityLogs(@Query() query: ActivityLogQueryDto) {
    return this.activityLogService.getActivityLogs(query);
  }

  @Get('user/:userId')
  async getUserActivityLogs(
    @Query('userId') userId: number,
    @Query('limit') limit: number = 50
  ) {
    return this.activityLogService.getUserActivityLogs(userId, limit);
  }

  @Get('resource/:resourceType/:resourceId')
  async getResourceActivityLogs(
    @Query('resourceType') resourceType: string,
    @Query('resourceId') resourceId: string
  ) {
    return this.activityLogService.getResourceActivityLogs(resourceType, resourceId);
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { ActivityLog } from '../entities/activity-log.entity';
import { ActivityLogQueryDto } from '../dto/activity-log.dto';
import { PaginatedResult } from '../../common/dto/pagination.dto';

@Injectable()
export class ActivityLogRepository {
  constructor(
    @InjectRepository(ActivityLog)
    private readonly repository: Repository<ActivityLog>
  ) {}

  async create(logData: Partial<ActivityLog>): Promise<ActivityLog> {
    const log = this.repository.create(logData);
    return this.repository.save(log);
  }

  async findWithFilters(query: ActivityLogQueryDto): Promise<PaginatedResult<ActivityLog>> {
    const queryBuilder = this.buildQueryWithFilters(query);
    
    const total = await queryBuilder.getCount();
    const page = query.page || 1;
    const limit = query.limit || 10;
    
    const data = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('activity_logs.created_at', 'DESC')
      .getMany();

    return new PaginatedResult(data, total, page, limit);
  }

  async findByUserId(userId: number, limit: number = 50): Promise<ActivityLog[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit
    });
  }

  async findByResourceId(resourceType: string, resourceId: string): Promise<ActivityLog[]> {
    return this.repository.find({
      where: { resourceType, resourceId },
      order: { createdAt: 'DESC' }
    });
  }

  async deleteOldLogs(days: number): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const result = await this.repository
      .createQueryBuilder()
      .delete()
      .where('created_at < :cutoffDate', { cutoffDate })
      .execute();
    
    return result.affected || 0;
  }

  private buildQueryWithFilters(query: ActivityLogQueryDto): SelectQueryBuilder<ActivityLog> {
    const queryBuilder = this.repository.createQueryBuilder('activity_logs');

    if (query.userId) {
      queryBuilder.andWhere('activity_logs.user_id = :userId', { userId: query.userId });
    }

    if (query.actionType) {
      queryBuilder.andWhere('activity_logs.action_type = :actionType', { actionType: query.actionType });
    }

    if (query.level) {
      queryBuilder.andWhere('activity_logs.level = :level', { level: query.level });
    }

    if (query.resourceType) {
      queryBuilder.andWhere('activity_logs.resource_type = :resourceType', { resourceType: query.resourceType });
    }

    if (query.dateFrom) {
      queryBuilder.andWhere('activity_logs.created_at >= :dateFrom', { dateFrom: query.dateFrom });
    }

    if (query.dateTo) {
      queryBuilder.andWhere('activity_logs.created_at <= :dateTo', { dateTo: query.dateTo });
    }

    return queryBuilder;
  }
}
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ActivityLogRepository } from './repositories/activity-log.repository';
import { CreateActivityLogDto, ActivityLogQueryDto } from './dto/activity-log.dto';
import { ActivityLog, ActionType, LogLevel } from './entities/activity-log.entity';

export interface LogContext {
  userId?: number;
  userEmail?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  resourceType?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class ActivityLogService {
  private readonly logger = new Logger(ActivityLogService.name);

  constructor(
    private readonly activityLogRepository: ActivityLogRepository
  ) {}

  async log(
    actionType: ActionType,
    action: string,
    context: LogContext = {},
    level: LogLevel = LogLevel.INFO,
    message?: string,
    details?: string
  ): Promise<void> {
    try {
      const logData: CreateActivityLogDto = {
        actionType,
        action,
        level,
        message,
        details,
        ...context
      };

      await this.activityLogRepository.create(logData);
    } catch (error) {
      this.logger.error(`Failed to create activity log: ${error.message}`, error.stack);
    }
  }

  async logUserAction(
    userId: number,
    userEmail: string,
    actionType: ActionType,
    action: string,
    context: Omit<LogContext, 'userId' | 'userEmail'> = {}
  ): Promise<void> {
    await this.log(actionType, action, { userId, userEmail, ...context });
  }

  async logResourceAction(
    resourceType: string,
    resourceId: string,
    actionType: ActionType,
    action: string,
    context: LogContext = {}
  ): Promise<void> {
    await this.log(actionType, action, { resourceType, resourceId, ...context });
  }

  async logError(
    action: string,
    error: Error,
    context: LogContext = {}
  ): Promise<void> {
    await this.log(
      ActionType.ACCESS,
      action,
      context,
      LogLevel.ERROR,
      error.message,
      error.stack
    );
  }

  async getActivityLogs(query: ActivityLogQueryDto) {
    return this.activityLogRepository.findWithFilters(query);
  }

  async getUserActivityLogs(userId: number, limit: number = 50): Promise<ActivityLog[]> {
    return this.activityLogRepository.findByUserId(userId, limit);
  }

  async getResourceActivityLogs(resourceType: string, resourceId: string): Promise<ActivityLog[]> {
    return this.activityLogRepository.findByResourceId(resourceType, resourceId);
  }

  // Cleanup old logs (runs daily at 2 AM)
  // @Cron(CronExpression.EVERY_DAY_AT_2AM)
  // async cleanupOldLogs(): Promise<void> {
  //   try {
  //     const retentionDays = 90; // Keep logs for 90 days
  //     const deletedCount = await this.activityLogRepository.deleteOldLogs(retentionDays);
  //     this.logger.log(`Cleaned up ${deletedCount} old activity logs`);
  //   } catch (error) {
  //     this.logger.error(`Failed to cleanup old logs: ${error.message}`, error.stack);
  //   }
  // }
}

import { SetMetadata } from '@nestjs/common';
import { ActionType } from './entities/activity-log.entity';

export const LOG_ACTIVITY_KEY = 'log_activity';

export interface LogActivityOptions {
  actionType: ActionType;
  action: string;
  resourceType?: string;
  includeBody?: boolean;
  includeResult?: boolean;
}

export const LogActivity = (options: LogActivityOptions) => 
  SetMetadata(LOG_ACTIVITY_KEY, options);

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum LogLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  DEBUG = 'debug',
}

export enum ActionType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  APPROVE = 'approve',
  REJECT = 'reject',
  LOGIN = 'login',
  ACCESS = 'access',
  EXPORT = 'export',
  IMPORT = 'import',
  COPY = 'copy',
}

@Entity('activity_logs')
@Index(['userId', 'createdAt'])
@Index(['actionType', 'createdAt'])
@Index(['level', 'createdAt'])
export class ActivityLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', nullable: true })
  userId?: number;

  @Column({ name: 'user_email', nullable: true })
  userEmail?: string;

  @Column({ name: 'action_type', type: 'enum', enum: ActionType })
  actionType: ActionType;

  @Column()
  action: string;

  @Column({ name: 'resource_type', nullable: true })
  resourceType?: string;

  @Column({ name: 'resource_id', nullable: true })
  resourceId?: string;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: 'ip_address', nullable: true })
  ipAddress?: string;

  @Column({ name: 'user_agent', nullable: true })
  userAgent?: string;

  @Column({ type: 'enum', enum: LogLevel, default: LogLevel.INFO })
  level: LogLevel;

  @Column({ nullable: true })
  message?: string;

  @Column({ type: 'text', nullable: true })
  details?: string;

  @Column({ name: 'session_id', nullable: true })
  sessionId?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}

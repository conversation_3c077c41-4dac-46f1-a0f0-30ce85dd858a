import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActivityLog } from './entities/activity-log.entity';
import { ActivityLogRepository } from './repositories/activity-log.repository';
import { ActivityLogService } from './activity-log.service';
import { ActivityLogController } from './activity-log.controller';
import { ActivityLogInterceptor } from './activity-log.interceptor';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([ActivityLog])],
  providers: [
    ActivityLogRepository,
    ActivityLogService,
    ActivityLogInterceptor
  ],
  controllers: [ActivityLogController],
  exports: [ActivityLogService, ActivityLogInterceptor]
})
export class ActivityLogModule {}

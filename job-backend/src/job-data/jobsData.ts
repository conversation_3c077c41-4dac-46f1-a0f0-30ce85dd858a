export interface Job {
  id: string;
  title: string;
  description: string;
  category: string;
  type: string;
  location: string;
  fullAddress: string;
  workingHours: string;
  salary: string;
  postedDays: number;
  featured: boolean;
  code: string;
  responsibilities: string[];
  technicalSkills: string[];
  softSkills: string[];
  benefits: string[];
}

export const jobsData: Job[] = [
  {
    id: "delivery-lead",
    title: "Delivery Lead / Senior Automation Test Engineer",
    description:
      "Tekai Vietnam is looking for a Senior Automation Test Engineer to lead our testing efforts and ensure the quality of our software solutions. You will be responsible for designing, implementing, and maintaining automated test frameworks, guiding junior testers, and collaborating with developers to improve software reliability.",
    category: "Quality Assurance",
    type: "Full-time",
    location: "Hanoi, Vietnam",
    fullAddress: "17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội",
    workingHours: "5 days per week (Monday to Friday, from 9:00 AM to 6:00 PM, with a 1.5-hour lunch break)",
    salary: "Negotiable",
    postedDays: 3,
    featured: true,
    code: "HR09",
    responsibilities: [
      "Design, develop, and maintain robust automation test frameworks for web, API, and mobile applications.",
      "Develop and execute automated test scripts using tools like Selenium, Cypress, Playwright, or Appium.",
      "Perform end-to-end, regression, performance, and security testing to validate software quality.",
      "Analyze test results, report defects, and work closely with developers to resolve issues.",
      "Define and implement test strategies, plans, and documentation for automation testing.",
      "Optimize automation test suites for better efficiency and coverage.",
      "Mentor junior testers and improve automation best practices within the team.",
      "Participate in requirement clarification, test planning, test scenario development, and test automation scripting.",
    ],
    technicalSkills: [
      "4+ years of experience in automation testing.",
      "Proficient in testing methodologies and writing comprehensive test cases.",
      "Strong proficiency in Selenium, Cypress, Playwright, or Appium.",
      "Experience with at least one programming language (e.g., Java, Python, JavaScript, C#).",
      "Strong understanding of API testing using Postman, RestAssured, or similar tools.",
      "Familiarity with test management tools (Jira, TestRail, Xray, etc.).",
      "Knowledge of performance testing tools like JMeter, k6, or Gatling is a plus.",
    ],
    softSkills: [
      "Strong problem-solving and analytical skills.",
      "Excellent communication skills in English (preferred TOEIC 750+ or IELTS 6.0+).",
      "Ability to lead test automation strategies and mentor junior team members.",
      "Proactive, self-motivated, and detail-oriented.",
    ],
    benefits: [
      "13th-month salary bonus, business performance-based bonus, and holiday bonuses",
      "Health insurance card",
      "Annual HR orientation program, professional skill development, and course subsidies",
      "Quarterly team-building activities and annual company trips",
      "Opportunities to travel abroad for training and experience",
      "Participation in an English club taught by native speakers",
    ],
  }
]

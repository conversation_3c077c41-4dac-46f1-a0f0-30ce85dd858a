import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { databaseConfig } from './config/database.config';
import { JobsModule } from './jobs/jobs.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { ContactModule } from './contact/contact.module';
import { PublicControllerModule } from './public-controller/public-controller.module';
import { BlogsModule } from './blogs/blogs.module';
import { OptionsModule } from './options/options.module';
import { ActivityLogModule } from './activities/activity-log.module';
import { CollaboratorPolicyModule } from './collaborator-policy/collaborator-policy.module';
import { CommonModule } from './common/common.module';
import { ContextInterceptor } from './common/interceptors/context.interceptor';

@Module({
  imports: [
    TypeOrmModule.forRoot(databaseConfig),
    CommonModule,
    JobsModule,
    AuthModule,
    UsersModule,
    ContactModule,
    PublicControllerModule,
    BlogsModule,
    OptionsModule,
    ActivityLogModule,
    CollaboratorPolicyModule
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ContextInterceptor,
    },
  ],
})
export class AppModule {}

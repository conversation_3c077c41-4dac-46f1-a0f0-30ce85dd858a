import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OptionsService } from './options.service';
import { OptionsController } from './options.controller';
import { Option } from './entities/option.entity';
import { PublicOptionsController } from '../public-controller/public-options.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Option])],
  controllers: [OptionsController, PublicOptionsController],
  providers: [OptionsService],
  exports: [OptionsService],
})
export class OptionsModule {}

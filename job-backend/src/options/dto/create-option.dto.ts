import { IsBoolean, IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsO<PERSON>al, IsString } from 'class-validator';

export class CreateOptionDto {
  @IsNotEmpty()
  @IsString()
  optionName: string;

  @IsNotEmpty()
  @IsString()
  optionTitle: string;

  @IsNotEmpty()
  @IsString()
  optionCode: string;

  @IsOptional()
  @IsNumber()
  index?: number;

  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @IsOptional()
  @IsString()
  createdBy?: string;

  @IsOptional()
  @IsString()
  updatedBy?: string; 
}

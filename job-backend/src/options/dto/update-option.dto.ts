import { IsBoolean, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class UpdateOptionDto {
  @IsOptional()
  @IsString()
  optionName?: string;

  @IsOptional()
  @IsString()
  optionTitle?: string;

  @IsOptional()
  @IsString()
  optionCode?: string;

  @IsOptional()
  @IsNumber()
  index?: number;

  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @IsOptional()
  @IsString()
  updatedBy?: string;
}

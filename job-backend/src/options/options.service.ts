import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Option } from './entities/option.entity';
import { CreateOptionDto } from './dto/create-option.dto';
import { UpdateOptionDto } from './dto/update-option.dto';
import { UpdateOptionIndexDto } from './dto/update-option-index.dto';
import { FindOptionDto } from './dto/find-option.dto';
import { PaginatedResult } from '../common/dto/pagination.dto';

@Injectable()
export class OptionsService {
  constructor(
    @InjectRepository(Option)
    private optionsRepository: Repository<Option>,
  ) {}

  async find(findOptionDto: FindOptionDto): Promise<PaginatedResult<Option>> {
    const { page = 1, limit = 10, search, optionNames, sortBy, sortOrder } = findOptionDto || {};

    const queryBuilder = this.optionsRepository.createQueryBuilder('option');

    if (optionNames) {
      const names = Array.isArray(optionNames) ? optionNames : [optionNames];
      if (names.length > 0) {
        queryBuilder.andWhere('option.optionName IN (:...names)', { names });
      }
    }

    if (search) {
      queryBuilder.andWhere(
        '(LOWER(option.optionTitle) LIKE :search OR LOWER(option.optionCode) LIKE :search)',
        { search: `%${search.toLowerCase()}%` }
      );
    }

    if (sortBy && sortOrder) {
      const allowedSortFields = {
        optionName: 'option.optionName',
        optionTitle: 'option.optionTitle',
        optionCode: 'option.optionCode',
        index: 'option.index',
        createdAt: 'option.createdAt',
      };
      const field = allowedSortFields[sortBy];
      if (field) {
        queryBuilder.orderBy(field, sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC');
      }
    } else {
      queryBuilder.orderBy('option.optionName', 'ASC').addOrderBy('option.index', 'ASC');
    }

    const totalCount = await queryBuilder.getCount();
    
    if (Number(limit) !== -1) {
      queryBuilder.limit(limit);
      if (Number(page) !== -1) {
        queryBuilder.offset((page - 1) * limit);
      }
    }

    const items = await queryBuilder.getMany();

    return new PaginatedResult(items, totalCount, page, limit);
  }

  async findOne(id: number): Promise<Option> {
    const option = await this.optionsRepository.findOne({ where: { id } });
    if (!option) {
      throw new NotFoundException(`Option with ID ${id} not found`);
    }
    return option;
  }

  async findByOptionName(optionName?: string): Promise<Option[]> {
    const whereCondition = optionName ? { optionName, active: true } : { active: true };
    const orderCondition = optionName ? { index: 'ASC' as const } : { optionName: 'ASC' as const, index: 'ASC' as const };

    return this.optionsRepository.find({
      where: whereCondition,
      order: orderCondition,
    });
  }

  async create(createOptionDto: CreateOptionDto, createdBy?: string): Promise<Option> {
    const option = this.optionsRepository.create({
      ...createOptionDto,
      createdBy
    });
    return this.optionsRepository.save(option);
  }

  async update(id: number, updateOptionDto: UpdateOptionDto, updatedBy?: string): Promise<Option> {
    const option = await this.findOne(id);
    const updatedOption = this.optionsRepository.merge(option, {
      ...updateOptionDto,
      updatedBy
    });
    return this.optionsRepository.save(updatedOption);
  }

  async remove(id: number, updatedBy?: string): Promise<{ message: string }> {
    const option = await this.findOne(id);
    option.active = false;
    if (updatedBy) {
      option.updatedBy = updatedBy;
    }
    await this.optionsRepository.save(option);
    return { message: `Option with ID ${id} has been deactivated` };
  }

  async batchUpdateIndexes(indexUpdates: UpdateOptionIndexDto[], updatedBy?: string): Promise<Option[]> {
    const optionIds = indexUpdates.map(update => update.id);
    const options = await this.optionsRepository.findBy({ id: In(optionIds) });

    if (options.length !== optionIds.length) {
      const foundIds = options.map(option => option.id);
      const missingIds = optionIds.filter(id => !foundIds.includes(id));
      throw new NotFoundException(`Some options were not found: ${missingIds.join(', ')}`);
    }

    const updatedOptions = options.map(option => {
      const update = indexUpdates.find(u => u.id === option.id);
      if (update) {
        option.index = update.index;
        if (updatedBy) {
          option.updatedBy = updatedBy;
        }
      }
      return option;
    });

    return this.optionsRepository.save(updatedOptions);
  }

  async seedJobOptions(createdBy: string = 'system'): Promise<{ message: string }> {
    const jobConstants = {
      JOB_TYPES: [
        { title: 'Full-time', code: 'FT', index: 1 },
        { title: 'Part-time', code: 'PT', index: 2 },
        { title: 'Contract', code: 'CT', index: 3 },
        { title: 'Internship', code: 'IN', index: 4 },
        { title: 'Remote', code: 'RM', index: 5 }
      ],
      JOB_CATEGORIES: [
        { title: 'Management', code: 'MGT', index: 1 },
        { title: 'Engineering', code: 'ENG', index: 2 },
        { title: 'Quality Assurance', code: 'QA', index: 3 },
        { title: 'Human Resources', code: 'HR', index: 4 },
        { title: 'Sales', code: 'SLS', index: 5 },
        { title: 'Design', code: 'DSG', index: 6 },
        { title: 'Marketing', code: 'MKT', index: 7 },
        { title: 'Customer Service', code: 'CS', index: 8 },
        { title: 'Other', code: 'OTH', index: 9 }
      ],
      JOB_LOCATIONS: [
        { title: 'Hanoi, Vietnam', code: 'HN-VN', index: 1 },
        { title: 'Ho Chi Minh, Vietnam', code: 'HCM-VN', index: 2 },
        { title: 'Hue, Vietnam', code: 'HUE-VN', index: 3 },
        { title: 'Danang, Vietnam', code: 'DN-VN', index: 4 },
        { title: 'Helsinki, Finland', code: 'HEL-FI', index: 5 },
        { title: 'Espoo, Finland', code: 'ESP-FI', index: 6 }
      ],
      JOB_SALARY_RANGES: [
        { title: 'Negotiable', code: 'NEG', index: 1 },
        { title: '6,000,000 - 12,000,000 VND', code: '6-12M', index: 2 },
        { title: '12,000,000 - 20,000,000 VND', code: '12-20M', index: 3 },
        { title: '15,000,000 - 25,000,000 VND', code: '15-25M', index: 4 },
        { title: '25,000,000 - 35,000,000 VND', code: '25-35M', index: 5 },
        { title: '30,000,000 - 40,000,000 VND', code: '30-40M', index: 6 },
        { title: '35,000,000 - 45,000,000 VND', code: '35-45M', index: 7 },
        { title: '35,000,000 - 50,000,000 VND', code: '35-50M', index: 8 },
        { title: 'Upto 30,000,000 VND', code: 'UT30M', index: 9 },
        { title: 'Upto 35,000,000 VND', code: 'UT35M', index: 10 },
        { title: 'Upto 40,000,000 VND', code: 'UT40M', index: 11 },
        { title: 'Upto 45,000,000 VND', code: 'UT45M', index: 12 },
        { title: 'Upto 50,000,000 VND', code: 'UT50M', index: 13 },
        { title: 'Upto 350,000 VND/Hour', code: '350KH', index: 14 },
        { title: 'Upto 300,000 VND/Hour', code: '300KH', index: 15 },
        { title: 'Upto 250,000 VND/Hour', code: '250KH', index: 16 },
        { title: '250k/post', code: '250K', index: 17 },
        { title: '200k/post', code: '200K', index: 18 },
        { title: '150k/post', code: '150K', index: 19 }
      ],
      JOB_WORKING_HOURS: [
        { title: '9:00 AM - 6:00 PM GMT+7', code: 'WH-0906-7', index: 1 },
        { title: '2:00 PM - 10:00 PM GMT+3', code: 'WH-1422-3', index: 2 },
        { title: 'Hybrid', code: 'HYB', index: 3 },
        { title: 'Remote - Fixed Hours', code: 'RM-FX', index: 4 },
        { title: 'Remote - Flexible Hours', code: 'RM-FL', index: 5 },
        { title: 'Part-time (Afternoon)', code: 'PT-AF', index: 6 }
      ]
    };

    // Delete existing options
    const optionNames = Object.keys(jobConstants);
    await this.optionsRepository.delete({ optionName: In(optionNames) });

    // Create new options
    const options: CreateOptionDto[] = [];

    for (const [optionName, values] of Object.entries(jobConstants)) {
      for (const item of values) {
        options.push({
          optionName,
          optionTitle: item.title,
          optionCode: item.code,
          index: item.index,
          active: true,
          createdBy,
          updatedBy: createdBy,
        });
      }
    }

    await this.optionsRepository.save(options);

    return { message: 'Job options seeded successfully' };
  }
}

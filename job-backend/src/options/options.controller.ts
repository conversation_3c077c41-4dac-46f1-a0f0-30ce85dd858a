import { Controller, Get, Post, Put, Delete, Body, Param, ParseIntPipe, Query, UseGuards, Request } from '@nestjs/common';
import { OptionsService } from './options.service';
import { CreateOptionDto } from './dto/create-option.dto';
import { UpdateOptionDto } from './dto/update-option.dto';
import { UpdateOptionIndexDto } from './dto/update-option-index.dto';
import { FindOptionDto } from './dto/find-option.dto';

@Controller('options')
export class OptionsController {
  constructor(private readonly optionsService: OptionsService) {}

  @Get()
  find(@Query() findOptionDto: FindOptionDto) {
    return this.optionsService.find(findOptionDto);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.optionsService.findOne(id);
  }

  @Post()
  create(@Request() req, @Body() createOptionDto: CreateOptionDto) {
    return this.optionsService.create(createOptionDto, req.user.email);
  }

  @Put(':id')
  update(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOptionDto: UpdateOptionDto,
  ) {
    return this.optionsService.update(id, updateOptionDto, req.user.email);
  }

  @Delete(':id')
  remove(@Request() req, @Param('id', ParseIntPipe) id: number) {
    return this.optionsService.remove(id, req.user.email);
  }

  @Post('seed')
  seedOptions(@Request() req) {
    return this.optionsService.seedJobOptions(req.user.email);
  }

  @Post('batch-update-indexes')
  batchUpdateIndexes(@Request() req: any, @Body() batchUpdateDto: UpdateOptionIndexDto[]) {
    return this.optionsService.batchUpdateIndexes(batchUpdateDto, req.user.email);
  }
}

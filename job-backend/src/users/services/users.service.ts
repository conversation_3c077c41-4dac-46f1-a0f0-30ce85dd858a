import { Injectable, NotFoundException, UnauthorizedException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { EmailService } from '../../email/email.service';
import { UserRole } from '../enums/user-role.enum';
import e from 'express';
import { FindUserDto } from '../dto/find-user.dto';
import { PaginatedResult } from '../../common/dto/pagination.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly emailService: EmailService,
  ) {}

  //create user
  async create(createUserDto: CreateUserDto, currentUserEmail?: string) {
    // Check if email already exists
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email }
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Handle empty birthday value
    const userDataToSave = {
      ...createUserDto,
      birthday: createUserDto.birthday || null,
      createdBy: currentUserEmail,
      updatedBy: currentUserEmail
    };

    const user = this.userRepository.create(userDataToSave);
    await this.userRepository.save(user);

    // Send welcome email
    //if user is admin, send welcome email to admin
    if (user.role === UserRole.ADMIN) {
      await this.emailService.sendWelcomeEmailAdmin(user);
    } else if (user.role === UserRole.EMPLOYER) {
      //if user is employee, send welcome email to employee 
      await this.emailService.sendWelcomeEmailEmployer(user);
    } else {
      await this.emailService.sendWelcomeEmail(user);
    }

    const { password, ...result } = user;
    return result;
  }
  

  async findAll(findUserDto: FindUserDto) : Promise<PaginatedResult<any>> {
    const { page = 1, limit = 10, search, role, sortBy, sortOrder } = findUserDto || {};
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    if (search) {
      queryBuilder.andWhere(
        '(LOWER(user.fullName) LIKE :search OR LOWER(user.email) LIKE :search OR LOWER(user.phone) LIKE :search)',
        { search: `%${search.toLowerCase()}%` }
      );
    }

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (sortBy && sortOrder) {
      const allowedSortFields = {
        createdAt: 'user.createdAt',
        fullName: 'user.fullName',
        email: 'user.email',
        role: 'user.role',
      };
      const field = allowedSortFields[sortBy];
      if (field) {
        queryBuilder.orderBy(field, sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC');
      }
    } else {
      queryBuilder.orderBy('user.createdAt', 'DESC');
    }

    const totalCount = await queryBuilder.getCount();

    queryBuilder.skip((page - 1) * limit).take(limit);

    const users = await queryBuilder.getMany();
    const result = users.map(({ password, ...rest }) => rest);

    return new PaginatedResult(result, totalCount, page, limit)
  }

  async findOne(id: number) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    const { password, ...result } = user;
    return result;
  }

  async findOneWithPassword(id: number) {
    const user = await this.userRepository.findOne({
      where: { id },
      select: ['id', 'email', 'password', 'fullName', 'birthday', 'phone', 'active', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']
    });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto, currentUserEmail?: string) {
    const user = await this.findOne(id);
    
    // Handle empty birthday value
    const dataToUpdate = {
      ...updateUserDto,
      birthday: updateUserDto.birthday || null,
      updatedBy: currentUserEmail
    };
    
    const updated = Object.assign(user, dataToUpdate);
    await this.userRepository.save(updated);
    const { password, ...result } = updated;
    return result;
  }

  async changePassword(id: number, changePasswordDto: ChangePasswordDto, currentUserEmail: string) {
    const user = await this.findOneWithPassword(id);
    
    
    user.password = changePasswordDto.newPassword;
    user.updatedBy = currentUserEmail;
    await this.userRepository.save(user);

    // Send password change notification email
    await this.emailService.sendPasswordChangeNotification(user);

    return { message: 'Password changed successfully' };
  }

  async remove(id: number, currentUserEmail?: string) {
    const user = await this.findOne(id);
    await this.userRepository.update(id, { 
      active: false,
      updatedBy: currentUserEmail
    });
    return { message: `User ${id} has been deactivated` };
  }
}
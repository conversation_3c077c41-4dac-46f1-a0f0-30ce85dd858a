import { IsString, IsEmail, IsDate, IsEnum, IsOptional } from 'class-validator';
import { UserRole } from '../enums/user-role.enum';

export class CreateUserDto {
  @IsString()
  fullName: string;

  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  phone: string;

  @IsOptional()
  @IsDate()
  birthday: Date;

  @IsOptional()
  @IsEnum(UserRole)
  role: UserRole;

  active: boolean;
}
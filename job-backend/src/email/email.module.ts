import { Module } from '@nestjs/common';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { join } from 'path';
import { EmailService } from './email.service';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const host = configService.get<string>('SMTP_HOST') || 'smtp.zoho.com';
        const port = configService.get<number>('SMTP_PORT') || 587;
        const user = configService.get<string>('SMTP_USER');
        const pass = configService.get<string>('SMTP_PASSWORD');
        const from = configService.get<string>('SMTP_FROM');
        console.log(user, pass, from);
        if (!user || !pass || !from) {
          throw new Error('SMTP configuration not properly configured');
        }

        return {
          transport: {
            host,
            port,
            secure: false,
            auth: {
              user,
              pass,
            },
            tls: {
              rejectUnauthorized: true,
              minVersion: 'TLSv1.2'
            }
          },
          defaults: {
            from,
          },
          template: {
            dir: join(__dirname, 'templates'),
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [EmailService],
  exports: [EmailService],
})
export class EmailModule {} 
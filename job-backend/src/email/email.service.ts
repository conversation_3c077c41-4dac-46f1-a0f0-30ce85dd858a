import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { User } from '../users/entities/user.entity';
import { Contact } from '../contact/entities/contact.entity';
import { InterviewProcess } from '../jobs/entities/interview-process.entity';
import { JobApplicationDetailDto } from '../jobs/dto/job-application-detail.dto';
import { formatDateTime } from '../utils/date.utils';

@Injectable()
export class EmailService {
  constructor(private readonly mailerService: MailerService) {}

  async sendWelcomeEmail(user: User): Promise<void> {
    await this.mailerService.sendMail({
      to: user.email,
      subject: 'Welcome to Job Portal!',
      template: './welcome',
      context: {
        name: user.fullName,
      },
    });
  }

  async sendWelcomeEmailEmployer(user: User): Promise<void> {
    await this.mailerService.sendMail({
      to: user.email,
      subject: 'Welcome to Job Portal CMS!',
      template: './welcome-employer',
      context: {
        name: user.fullName,
      },
    });
  }

  async sendWelcomeEmailAdmin(admin: User): Promise<void> {
    await this.mailerService.sendMail({
      to: admin.email,
      subject: 'Welcome to Job Portal Admin Panel',
      template: './welcome-admin',
      context: {
        name: admin.fullName,
        email: admin.email,
        password: admin.password,
      },
    });
  }

  async sendPasswordResetEmail(user: User, resetToken: string): Promise<void> {
    await this.mailerService.sendMail({
      to: user.email,
      subject: 'Password Reset Request',
      template: './password-reset',
      context: {
        name: user.fullName,
        resetToken,
      },
    });
  }

  async sendPasswordChangeNotification(user: User): Promise<void> {
    await this.mailerService.sendMail({
      to: user.email,
      subject: 'Password Changed Successfully',
      template: './password-changed',
      context: {
        name: user.fullName,
      },
    });
  }

  async sendTestEmail(email: string): Promise<void> {
    await this.mailerService.sendMail({
      to: email,
      subject: 'Test Email from Job Portal',
      template: './welcome',
      context: {
        name: 'Test User',
      },
    });
  }

  async sendJobApplicationNotification(application: JobApplicationDetailDto): Promise<void> {
    const cvDownloadUrl = `https://job-api.tekai.vn/v1/job-applications/${application.id}/download-cv`;

    // Send notification to recruiter
    await this.mailerService.sendMail({
      to: '<EMAIL>',
      subject: 'New Job Application Received - ' + application.jobTitle + ' - ' + application.name || 'Not specified',
      template: './job-application-notification',
      context: {
        jobTitle: application.jobTitle || 'Not specified',
        applicantName: application.name,
        applicantEmail: application.email,
        refPerson: application.refPerson,
        cvDownloadUrl: cvDownloadUrl,
      },
    });

    // Send confirmation to applicant
    await this.mailerService.sendMail({
      to: application.email,
      subject: 'We Have Received Your Application - Tekai Vietnam',
      template: './application-confirmation',
      context: {
        applicantName: application.name,
        jobTitle: application.jobTitle || 'Not specified',
      },
    });
  }

  async sendContactNotification(contact: Contact): Promise<void> {
    await this.mailerService.sendMail({
      to: '<EMAIL>',
      subject: 'New Contact Form Submission',
      template: './contact-notification',
      context: {
        fullname: contact.fullname,
        email: contact.email,
        phoneNumber: contact.phoneNumber,
        content: contact.content,
      },
    });
  }

  async sendInterviewScheduleEmail(
    application: JobApplicationDetailDto,
    interviewProcess: InterviewProcess,
    roundNumber: number
  ): Promise<void> {

    const isRound1 = roundNumber === 1;
    const roundData = {
      schedule: isRound1 ? interviewProcess.scheduleRound1 : interviewProcess.scheduleRound2,
      interviewer: isRound1 ? interviewProcess.interviewerRound1 : interviewProcess.interviewerRound2,
      content: isRound1 ? interviewProcess.contentRound1 : interviewProcess.contentRound2,
      template: isRound1 ? './interview-notification-round1' : './interview-notification-round2'
    };

    const scheduleDateTime = formatDateTime(roundData.schedule);
    const interviewerName = roundData.interviewer?.split('@')[0] || 'To be determined';
    const interviewerEmail = roundData.interviewer;
    const content = roundData.content || 'Details will be provided during the interview';

    // 1. Send email to the applicant
    await this.mailerService.sendMail({
      to: application.email,
      subject: `Interview Schedule - Round ${roundNumber} - Tekai Vietnam`,
      template: roundData.template,
      context: {
        applicantName: application.name,
        jobTitle: application.jobTitle || 'Not specified',
        scheduleDateTime: scheduleDateTime,
        interviewer: interviewerName,
        content: content,
      },
    });

    // 2. Send email to the interviewer if email is provided
    if (interviewerEmail) {
      await this.mailerService.sendMail({
        to: interviewerEmail,
        subject: `Interview Assignment - ${application.name} - Round ${roundNumber}`,
        template: './interview-notification-interviewer',
        context: {
          interviewerName: interviewerName,
          applicantName: application.name,
          jobTitle: application.jobTitle || 'Not specified',
          roundNumber: roundNumber,
          scheduleDateTime: scheduleDateTime,
          content: content,
        },
      });
    }
  }

  async sendRejectionEmail(application: JobApplicationDetailDto): Promise<void> {
    await this.mailerService.sendMail({
      to: application.email,
      subject: 'Tekai– Application Update',
      template: './applicant-rejection-notification',
      context: {
        applicantName: application.name,
        jobTitle: application.jobTitle || 'Not specified',
      },
    });
  }
}
import { Controller, Post, Body } from '@nestjs/common';
import { Public } from '../auth/decorators/public.decorator';
import { ContactService } from 'src/contact/contact.service';

@Controller('v1')
@Public()
export class ContactPublicController {
  constructor(private readonly contactService: ContactService) {}

  @Post('contacts')
  async submitContact(@Body() createContactDto: any) {
    return this.contactService.create(createContactDto);
  }
}

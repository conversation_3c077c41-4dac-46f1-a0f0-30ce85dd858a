import { Controller, Get, Query } from '@nestjs/common';
import { OptionsService } from '../options/options.service';
import { Public } from '../auth/decorators/public.decorator';

@Controller('v1/options')
@Public()
export class PublicOptionsController {
  constructor(private readonly optionsService: OptionsService) {}

  @Get()
  findByOptionName(@Query('optionName') optionName: string) {
    return this.optionsService.findByOptionName(optionName);
  }
}

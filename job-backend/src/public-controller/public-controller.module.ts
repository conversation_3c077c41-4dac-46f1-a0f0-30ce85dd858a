import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PublicJobsController } from './public-jobs.controller';
import { ContactPublicController } from './contact-public.controller';
import { JobsService } from '../jobs/services/jobs.service';
import { JobApplicationService } from '../jobs/services/job-application.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Job } from '../jobs/entities/job.entity';
import { JobApplication } from '../jobs/entities/job-application.entity';
import { ContactModule } from '../contact/contact.module';
import { EmailModule } from '../email/email.module';
import { PublicBlogsController } from './public-blogs.controller';
import { BlogsModule } from '../blogs/blogs.module';
import { CollaboratorPolicyModule } from '../collaborator-policy/collaborator-policy.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Job, JobApplication]),
    ContactModule,  // This will now provide ContactService
    EmailModule,
    BlogsModule,
    CollaboratorPolicyModule
  ],
  controllers: [PublicJobsController, ContactPublicController, PublicBlogsController],
  providers: [JobsService, JobApplicationService]
})
export class PublicControllerModule {}
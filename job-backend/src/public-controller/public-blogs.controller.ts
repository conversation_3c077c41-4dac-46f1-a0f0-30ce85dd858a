import {
  Controller,
  Get,
  Param,
} from '@nestjs/common';
import { BlogsService } from '../blogs/blogs.service';
import { Public } from '../auth/decorators/public.decorator';
import { CategoriesService } from '../blogs/categories.service';

@Controller('v1/blogs')
@Public()
export class PublicBlogsController {
  constructor(
    private readonly blogsService: BlogsService,
    private readonly categoriesService: CategoriesService,
  ) {}

  @Get()
  findAll() {
    return this.blogsService.findAll();
  }

  @Get('categories')
  findAllCategories() {
    return this.categoriesService.findAllActive();
  }

  @Get('by-category/:slug')
  findByCategorySlug(@Param('slug') slug: string) {
    return this.blogsService.findByCategorySlug(slug);
  }

  @Get(':slug')
  findBySlug(@Param('slug') slug: string) {
    return this.blogsService.findBySlug(slug);
  }
}
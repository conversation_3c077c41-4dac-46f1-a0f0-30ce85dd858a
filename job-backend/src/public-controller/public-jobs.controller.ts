import { Controller, Get, Post, Param, Body, UploadedFile, UseInterceptors, ParseIntPipe, NotFoundException, Res, PayloadTooLargeException } from '@nestjs/common';
import { Response } from 'express';
import { Public } from '../auth/decorators/public.decorator';
import { JobsService } from '../jobs/services/jobs.service';
import { JobApplicationService } from '../jobs/services/job-application.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as path from 'path';
import * as fs from 'fs';
import { unaccent } from '../utils/string.utils';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

@Controller('v1')
@Public() // Mark entire controller as public
export class PublicJobsController {
  constructor(
    private readonly jobsService: JobsService,
    private readonly jobApplicationService: JobApplicationService,
  ) {}

  @Get('jobs')
  findAll() {
    return this.jobsService.userFindAll();
  }

  @Get('jobs/:id')
  findOne(@Param('id') id: string) {
    return this.jobsService.findByIdOrSlug(id);
  }

  @Post('jobs/:id/increment-view')
  async incrementView(@Param('id') id: string) {
    return await this.jobsService.incrementTotalViews(id);
  }

  @Post('job-applications')
  @UseInterceptors(
    FileInterceptor('cv', {
      storage: diskStorage({
        destination: './uploads/cv',
        filename: (req, file, callback) => {
          const applicantName = req.body.name || 'unnamed';
          const timestamp = Math.floor(Date.now() / 1000);
          const filename = `${unaccent(applicantName)}_${timestamp}${extname(file.originalname)}`;
          callback(null, filename);
        },
      }),
      limits: {
        fileSize: MAX_FILE_SIZE
      },
      fileFilter: (req, file, callback) => {
        if (file.size > MAX_FILE_SIZE) {
          return callback(new PayloadTooLargeException('File size exceeds 10MB limit'), false);
        }
        callback(null, true);
      }
    }),
  )
  async create(
    @Body() createJobApplicationDto: any,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const cvFilePath = file ? file.path : null;
    return this.jobApplicationService.create({
      ...createJobApplicationDto,
      cvFile: cvFilePath,
    }, true);
  }

  @Get('/job-applications/:id/download-cv')
  async downloadCv(
    @Param('id', ParseIntPipe) id: number,
    @Res() res: Response
  ) {
    const cvPath = await this.jobApplicationService.getCvFilePath(id);
    
    if (!fs.existsSync(cvPath)) {
      throw new NotFoundException('CV file not found on server');
    }

    const filename = path.basename(cvPath);
    return res.download(cvPath, filename);
  }
}
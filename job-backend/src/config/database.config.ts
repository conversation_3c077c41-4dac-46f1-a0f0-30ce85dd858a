import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as dotenv from 'dotenv';

dotenv.config();

export const showDatabaseEnvironment = () => {
  console.log('Database Environment Configuration:');
  console.log('----------------------------------');
  console.log(`Host: ${process.env.DB_HOST || 'localhost'}`);
  console.log(`Port: ${process.env.DB_PORT || '3306'}`);
  console.log(`Database: ${process.env.DB_NAME || 'job-cms'}`);
  console.log(`Username: ${process.env.DB_USERNAME || 'root'}`);
  console.log(`Password: ${process.env.DB_PASSWORD}`);
  console.log('----------------------------------');
};

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'job-cms',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: true, // Enable auto-synchronization
  logging: true, // Enable logging to see SQL queries
  autoLoadEntities: true, // Automatically load entities
};
import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { NestExpressApplication } from '@nestjs/platform-express';
import { json, urlencoded } from 'express';
import { ConfigModule } from '@nestjs/config';
import { showDatabaseEnvironment } from './config/database.config';
import { corsConfig } from './config/cors.config';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  // Load environment variables first
  await ConfigModule.forRoot({
    isGlobal: true,
    envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
  });

  console.log('Starting server with NODE_ENV:', process.env.NODE_ENV);
  showDatabaseEnvironment();
  
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Configure request size limits
  app.use(json({ limit: '10mb' }));
  app.use(urlencoded({ extended: true, limit: '10mb' }));

  // Enable CORS with configuration from environment
  app.enableCors(corsConfig);

  // Enable global validation pipe with transformation
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
  }));

  // Apply JWT guard globally
  app.useGlobalGuards(new JwtAuthGuard(app.get(Reflector)));
  console.log(
    'Starting server on port 3002 with NODE_ENV:',
    process.env.NODE_ENV,
  );
  await app.listen(3002);
}
bootstrap();

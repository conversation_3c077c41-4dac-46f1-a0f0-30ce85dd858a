import {
  Injectable,
  UnauthorizedException,
  OnModuleInit,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { LoginDto, DecryptedLoginDto } from '../dto/login.dto';
import { RegisterDto } from '../dto/register.dto';
import { EmailService } from '../../email/email.service';
import { UserRole } from 'src/users/enums/user-role.enum';
import { decrypt } from '../../utils/crypto.utils';
import { ActivityLogService } from '../../activities/activity-log.service';
import { ActionType } from '../../activities/entities/activity-log.entity';

@Injectable()
export class AuthService implements OnModuleInit {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly emailService: EmailService,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async onModuleInit() {
    await this.createDefaultAdmin();
  }

  private async createDefaultAdmin() {
    try {
      const adminEmail = '<EMAIL>';
      const existingAdmin = await this.userRepository.findOne({
        where: { email: adminEmail },
      });

      console.log("existingAdmin", existingAdmin);
      // if (existingAdmin) {
      //   existingAdmin.password = '123456';
      //   await this.userRepository.save(existingAdmin);
      // }
      // return;
      if (!existingAdmin) {
        const admin = new User();
        admin.email = adminEmail;
        //make password strong with 8 characters  
        admin.password = Math.random().toString(36).slice(-8);
        admin.fullName = 'System Admin';
        admin.phone = '0000000000';
        admin.birthday = new Date('1990-01-01');
        admin.active = true;
        admin.role = UserRole.ADMIN;
        await this.userRepository.save(admin);
        //send email to admin
        await this.emailService.sendWelcomeEmailAdmin(admin);
        console.log('Default admin user created successfully');
      } else {
        console.log('Admin user already exists');
      }
    } catch (error) {
      console.error('Error creating default admin:', error);
    }
  }

  async register(registerDto: RegisterDto) {
    const existingUser = await this.userRepository.findOne({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new UnauthorizedException('Email already exists');
    }

    const user = this.userRepository.create(registerDto);
    await this.userRepository.save(user);

    // Send welcome email
    await this.emailService.sendWelcomeEmail(user);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = user;
    return result;
  }

  async login(loginDto: LoginDto) {
    try {
      if (!loginDto.credentials) {
        throw new BadRequestException('Credentials are required');
      }

      const decryptedCredentials = decrypt(loginDto.credentials) as DecryptedLoginDto;
      if (!decryptedCredentials || !decryptedCredentials.email || !decryptedCredentials.password) {
        throw new BadRequestException('Invalid encrypted credentials format');
      }

      const user = await this.userRepository.findOne({
        where: { email: decryptedCredentials.email },
      });

      if (!user || !(await user.validatePassword(decryptedCredentials.password))) {
        throw new UnauthorizedException('Invalid credentials');
      }

      const payload = { sub: user.id, email: user.email, role: user.role };
      const accessToken = this.jwtService.sign(payload);

      await this.activityLogService.logUserAction(
        user.id,
        user.email,
        ActionType.LOGIN,
        'User logged in'
      );

      return {
        access_token: accessToken,
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
        },
      };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }
      console.error('Login error:', error);
      throw new UnauthorizedException('Invalid credentials');
    }
  }
}

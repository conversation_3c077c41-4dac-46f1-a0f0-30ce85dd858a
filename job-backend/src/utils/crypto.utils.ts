import * as CryptoJS from 'crypto-js';

// Get the secret key from environment variables
const SECRET_KEY = process.env.AUTH_SECRET || 'TekaiJobPortalSecretKey2025';

export const encrypt = (data: string | object): string => {
  try {
    const dataString = typeof data === 'object' ? JSON.stringify(data) : data;
    return CryptoJS.AES.encrypt(dataString, SECRET_KEY).toString();
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

export const decrypt = (encryptedData: string): string | object => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    try {
      return JSON.parse(decryptedString);
    } catch {
      return decryptedString;
    }
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};

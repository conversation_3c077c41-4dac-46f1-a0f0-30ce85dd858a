import { IsString, <PERSON><PERSON>umber, IsEnum, IsOptional, IsA<PERSON>y, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateCommissionReportDto {
  @IsString()
  employerEmail: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsNumber()
  @Min(1)
  @Max(12)
  month: number;

  @IsNumber()
  @Min(2020)
  year: number;

  @IsNumber()
  totalAmount: number;

  @IsArray()
  @IsNumber({}, { each: true })
  commissionIds: number[];

  @IsOptional()
  @IsString()
  notes?: string;
}

import { IsString, <PERSON>N<PERSON>ber, IsEnum, IsOptional, IsDate } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { CommissionPhase } from '../enums/commission-phase.enum';
import { CommissionStatus } from '../enums/commission-status.enum';
import { PaymentStatus } from '../enums/payment-status.enum';
import { EmploymentType } from '../enums/employment-type.enum';

export class CreateEmployerCommissionDto {
  @IsString()
  employerEmail: string;

  @IsNumber()
  jobApplicationId: number;

  @IsEnum(EmploymentType)
  employmentType: EmploymentType;

  @IsOptional()
  @IsEnum(Level)
  candidateLevel: Level;

  @IsOptional()
  @IsEnum(EnglishSkill)
  englishSkill: EnglishSkill;

  @IsEnum(CommissionPhase)
  commissionPhase: CommissionPhase;

  @IsNumber()
  amount: number;

  @IsOptional()
  @IsEnum(CommissionStatus)
  status?: CommissionStatus;

  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : null)
  @Type(() => Date)
  @IsDate()
  paidDate?: Date;

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : null)
  @Type(() => Date)
  @IsDate()
  onboardingDate?: Date;

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : null)
  @Type(() => Date)
  @IsDate()
  officialDate?: Date;

  @IsOptional()
  @IsString()
  approvedBy?: string;

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : null)
  @Type(() => Date)
  @IsDate()
  approvedAt?: Date;

  @IsOptional()
  @IsString()
  notes?: string;
}

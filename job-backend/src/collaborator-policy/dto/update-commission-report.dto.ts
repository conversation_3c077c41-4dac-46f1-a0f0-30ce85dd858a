import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsEnum, IsString, IsDate } from 'class-validator';
import { CreateCommissionReportDto } from './create-commission-report.dto';
import { ReportStatus } from '../enums/report-status.enum';
import { PaymentStatus } from '../enums/payment-status.enum';
import { Transform, Type } from 'class-transformer';

export class UpdateCommissionReportDto extends PartialType(CreateCommissionReportDto) {
  @IsOptional()
  @IsEnum(ReportStatus)
  status?: ReportStatus;

  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @IsOptional()
  @Transform(({ value }) => value ? new Date(value) : null)
  @Type(() => Date)
  @IsDate()
  paidDate?: Date;

  @IsOptional()
  @IsString()
  notes?: string;
}

import { IsEnum, IsN<PERSON>ber, IsBoolean, IsOptional } from 'class-validator';
import { EmploymentType } from '../enums/employment-type.enum';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { PaymentPhase } from '../enums/payment-phase.enum';

export class CreatePaymentRateDto {
  @IsEnum(EmploymentType)
  employmentType: EmploymentType;

  @IsOptional()
  @IsEnum(Level)
  level?: Level;

  @IsOptional()
  @IsEnum(EnglishSkill)
  englishSkill?: EnglishSkill;

  @IsEnum(PaymentPhase)
  paymentPhase: PaymentPhase;

  @IsNumber()
  amount: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

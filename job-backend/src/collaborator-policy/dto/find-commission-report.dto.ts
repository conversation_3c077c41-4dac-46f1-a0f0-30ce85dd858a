import { <PERSON><PERSON><PERSON>al, <PERSON><PERSON>num, Is<PERSON>tring, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { BaseFilterDto } from '../../common/dto/base-filter.dto';
import { ReportStatus } from '../enums/report-status.enum';

export class FindCommissionReportDto extends BaseFilterDto {
  @IsOptional()
  @IsString()
  employerEmail?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(12)
  month?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(2020)
  year?: number;

  @IsOptional()
  @IsEnum(ReportStatus)
  status?: ReportStatus;
}

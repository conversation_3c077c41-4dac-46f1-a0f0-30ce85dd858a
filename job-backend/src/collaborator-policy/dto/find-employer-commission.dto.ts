import { IsOptional, IsEnum, IsString } from 'class-validator';
import { BaseFilterDto } from '../../common/dto/base-filter.dto';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { CommissionPhase } from '../enums/commission-phase.enum';
import { CommissionStatus } from '../enums/commission-status.enum';
import { EmploymentType } from '../enums/employment-type.enum';
import { PaymentStatus } from '../enums/payment-status.enum';

export class FindEmployerCommissionDto extends BaseFilterDto {
  @IsOptional()
  @IsString()
  employerEmail?: string;

  @IsOptional()
  @IsEnum(EmploymentType)
  employmentType?: EmploymentType;

  @IsOptional()
  @IsEnum(Level)
  candidateLevel?: Level;

  @IsOptional()
  @IsEnum(EnglishSkill)
  englishSkill?: EnglishSkill;

  @IsOptional()
  @IsEnum(CommissionPhase)
  commissionPhase?: CommissionPhase;

  @IsOptional()
  @IsEnum(CommissionStatus)
  status?: CommissionStatus;

  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;
}

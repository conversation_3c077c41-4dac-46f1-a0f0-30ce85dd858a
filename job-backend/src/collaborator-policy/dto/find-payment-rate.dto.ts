import { IsOptional, IsEnum, IsString } from 'class-validator';
import { BaseFilterDto } from '../../common/dto/base-filter.dto';
import { EmploymentType } from '../enums/employment-type.enum';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { PaymentPhase } from '../enums/payment-phase.enum';

export class FindPaymentRateDto extends BaseFilterDto {
  @IsOptional()
  @IsEnum(EmploymentType)
  employmentType?: EmploymentType;

  @IsOptional()
  @IsEnum(Level)
  level?: Level;

  @IsOptional()
  @IsEnum(EnglishSkill)
  englishSkill?: EnglishSkill;

  @IsOptional()
  @IsEnum(PaymentPhase)
  paymentPhase?: PaymentPhase;

  @IsOptional()
  @IsString()
  isActive?: string;
}

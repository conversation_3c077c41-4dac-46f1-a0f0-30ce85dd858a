import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentRate } from './entities/payment-rate.entity';
import { EmployerCommission } from './entities/employer-commission.entity';
import { CommissionReport } from './entities/commission-report.entity';
import { JobApplication } from '../jobs/entities/job-application.entity';
import { PaymentRateService } from './services/payment-rate.service';
import { EmployerCommissionService } from './services/employer-commission.service';
import { CommissionReportService } from './services/commission-report.service';
import { EmployerCommissionController } from './controllers/employer-commission.controller';
import { PaymentRateController } from './controllers/payment-rate.controller';
import { CommissionReportController } from './controllers/commission-report.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([PaymentRate, EmployerCommission, CommissionReport, JobApplication]),
  ],
  controllers: [PaymentRateController, EmployerCommissionController, CommissionReportController],
  providers: [PaymentRateService, EmployerCommissionService, CommissionReportService],
  exports: [PaymentRateService, EmployerCommissionService, CommissionReportService],
})
export class CollaboratorPolicyModule {}
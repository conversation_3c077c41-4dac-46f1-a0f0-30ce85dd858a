import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContextService } from '../../common/services/context.service';
import { EmployerCommission } from '../entities/employer-commission.entity';
import { PaymentRate } from '../entities/payment-rate.entity';
import { JobApplication } from '../../jobs/entities/job-application.entity';
import { CreateEmployerCommissionDto } from '../dto/create-employer-commission.dto';
import { UpdateEmployerCommissionDto } from '../dto/update-employer-commission.dto';
import { FindEmployerCommissionDto } from '../dto/find-employer-commission.dto';
import { ApproveCommissionDto } from '../dto/approve-commission.dto';
import { PaginatedResult } from '../../common/dto/pagination.dto';
import { CommissionPhase } from '../enums/commission-phase.enum';
import { CommissionStatus } from '../enums/commission-status.enum';
import { EmploymentType } from '../enums/employment-type.enum';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { PaymentPhase } from '../enums/payment-phase.enum';
import { ConflictException } from '@nestjs/common';
import { BaseAuditRepository } from '../../common/repositories/base-audit.repository';
import { AuditRepositoryFactory } from '../../common/services/audit-repository-factory.service';

@Injectable()
export class EmployerCommissionService {
  private readonly employerCommissionRepository: BaseAuditRepository<EmployerCommission>;
  constructor(
    private readonly auditFactory: AuditRepositoryFactory,
    @InjectRepository(PaymentRate)
    private readonly paymentRateRepository: Repository<PaymentRate>,
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    protected readonly context: ContextService,
  ) {
    this.employerCommissionRepository = this.auditFactory.createRepository(EmployerCommission);
  }

  async create(createEmployerCommissionDto: CreateEmployerCommissionDto): Promise<EmployerCommission> {
    const commission = this.employerCommissionRepository.create(createEmployerCommissionDto);
    return this.employerCommissionRepository.save(commission);
  }

  async findAll(filters?: FindEmployerCommissionDto): Promise<PaginatedResult<any>> {
    const queryBuilder = this.employerCommissionRepository
      .createQueryBuilder('commission')
      .andWhere('commission.report IS NULL')
      .leftJoin('job_application', 'jobApp', 'jobApp.id = commission.jobApplicationId')
      .select([
        'commission.*',
        'jobApp.name as applicantName',
        'jobApp.email as applicantEmail'
      ]);

    if (filters?.employerEmail) {
      queryBuilder.andWhere('commission.employerEmail = :employerEmail', {
        employerEmail: filters.employerEmail.trim()
      });
    }

    if (filters?.employmentType) {
      queryBuilder.andWhere('commission.employmentType = :employmentType', {
        employmentType: filters.employmentType
      });
    }

    if (filters?.candidateLevel) {
      queryBuilder.andWhere('commission.candidateLevel = :candidateLevel', {
        candidateLevel: filters.candidateLevel
      });
    }

    if (filters?.englishSkill) {
      queryBuilder.andWhere('commission.englishSkill = :englishSkill', {
        englishSkill: filters.englishSkill
      });
    }

    if (filters?.commissionPhase) {
      queryBuilder.andWhere('commission.commissionPhase = :commissionPhase', {
        commissionPhase: filters.commissionPhase
      });
    }

    if (filters?.status) {
      queryBuilder.andWhere('commission.status = :status', { status: filters.status });
    }

    if (filters?.paymentStatus) {
      queryBuilder.andWhere('commission.paymentStatus = :paymentStatus', { paymentStatus: filters.paymentStatus });
    }

    if (filters?.startDate) {
      queryBuilder.andWhere('commission.createdAt >= :startDate', { startDate: filters.startDate });
    }

    if (filters?.endDate) {
      queryBuilder.andWhere('commission.createdAt <= :endDate', { endDate: filters.endDate });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(commission.employerEmail LIKE :search OR commission.notes LIKE :search OR jobApp.name LIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    // Sorting
    const sortBy = filters?.sortBy || 'createdAt';
    const sortOrder = filters?.sortOrder || 'DESC';
    queryBuilder.orderBy(`commission.${sortBy}`, sortOrder);

    const totalCount = await queryBuilder.getCount();

    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    if (limit !== -1) {
      queryBuilder.limit(limit);
      if (page !== -1) {
        queryBuilder.offset((page - 1) * limit);
      }
    }

    const results = await queryBuilder.getRawMany();

    return new PaginatedResult(results, totalCount, page, limit);
  }

  async findOne(id: number): Promise<any> {
    const result = await this.employerCommissionRepository
      .createQueryBuilder('commission')
      .leftJoin('job_application', 'jobApp', 'jobApp.id = commission.jobApplicationId')
      .andWhere('commission.id = :id', { id })
      .select([
        'commission.*',
        'jobApp.name as applicantName',
        'jobApp.email as applicantEmail'
      ])
      .getRawOne();

    if (!result) {
      throw new NotFoundException(`Employer commission with ID "${id}" not found`);
    }
    return result;
  }

  async update(id: number, updateEmployerCommissionDto: UpdateEmployerCommissionDto): Promise<EmployerCommission> {
    const commission = await this.findOne(id);

    const updatedCommission = {
      ...commission,
      ...updateEmployerCommissionDto,
      ...(updateEmployerCommissionDto.status === CommissionStatus.APPROVED && {
        approvedBy: this.context.user?.email,
        approvedAt: new Date(),
      }),
    };

    return this.employerCommissionRepository.save(updatedCommission);
  }

  async remove(id: number): Promise<void> {
    const result = await this.employerCommissionRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Employer commission with ID "${id}" not found`);
    }
  }

  async autoCreateCommission(
    jobApplicationId: number,
    employerEmail: string,
    candidateLevel: Level,
    englishSkill: EnglishSkill,
    commissionPhase: CommissionPhase,
    employmentType: EmploymentType
  ): Promise<EmployerCommission | null> {
    try {
      const existingCommission = await this.employerCommissionRepository.findOne({
        where: {
          jobApplicationId,
          commissionPhase,
        },
      });

      if (existingCommission) {
        return existingCommission;
      }

      let paymentPhase: PaymentPhase;
      switch (commissionPhase) {
        case CommissionPhase.INTERVIEW_PASS:
          paymentPhase = PaymentPhase.INTERVIEW_PASS;
          break;
        case CommissionPhase.ONBOARDING:
          paymentPhase = PaymentPhase.ONBOARDING;
          break;
        case CommissionPhase.OFFICIAL:
          paymentPhase = PaymentPhase.OFFICIAL;
          break;
        default:
          return null;
      }

      // Find matching payment rate with mapped employment type
      const whereConditions = {
        employmentType,
        paymentPhase,
        isActive: true,
        ...(employmentType === EmploymentType.FULLTIME && {
          level: candidateLevel,
          englishSkill: englishSkill,
        }),
      };

      const paymentRate = await this.paymentRateRepository.findOne({
        where: whereConditions,
      });


      if (!paymentRate) return null;

      // Create commission with found payment rate
      const commissionDto: CreateEmployerCommissionDto = {
        employerEmail,
        jobApplicationId,
        employmentType,
        candidateLevel,
        englishSkill,
        commissionPhase,
        amount: paymentRate.amount,
        status: CommissionStatus.PENDING,
      };

      return this.create(commissionDto);
    } catch (error) {
      console.error('Error auto-creating commission:', error);
      return null;
    }
  }

  async copyToOfficial(id: number): Promise<EmployerCommission> {
    const originalCommission = await this.employerCommissionRepository.findOne({
      where: { id }
    });

    if (!originalCommission) {
      throw new NotFoundException(`Employer commission with ID "${id}" not found`);
    }

    const existingOfficialCommission = await this.employerCommissionRepository.findOne({
      where: {
        jobApplicationId: originalCommission.jobApplicationId,
        commissionPhase: CommissionPhase.OFFICIAL,
      },
    });

    if (existingOfficialCommission) {
      throw new ConflictException('OFFICIAL commission already exists for this job application');
    }

    let officialDate: Date | undefined;
    if (originalCommission.officialDate) {
      officialDate = originalCommission.officialDate;
    } else if (originalCommission.onboardingDate) {
      // Add 2 months to onboarding date
      officialDate = new Date(originalCommission.onboardingDate);
      officialDate.setMonth(officialDate.getMonth() + 2);
    }

    let paymentPhase = PaymentPhase.OFFICIAL;
    const whereConditions = {
      employmentType: originalCommission.employmentType,
      paymentPhase,
      isActive: true,
      ...(originalCommission.employmentType === EmploymentType.FULLTIME && {
        level: originalCommission.candidateLevel,
        englishSkill: originalCommission.englishSkill,
      }),
    };

    const paymentRate = await this.paymentRateRepository.findOne({
      where: whereConditions,
    });

    if (!paymentRate) {
      throw new Error('No payment rate found for OFFICIAL phase');
    }

    const officialCommissionDto: CreateEmployerCommissionDto = {
      employerEmail: originalCommission.employerEmail,
      jobApplicationId: originalCommission.jobApplicationId,
      employmentType: originalCommission.employmentType,
      candidateLevel: originalCommission.candidateLevel,
      englishSkill: originalCommission.englishSkill,
      commissionPhase: CommissionPhase.OFFICIAL,
      amount: paymentRate.amount,
      status: CommissionStatus.PENDING,
      onboardingDate: originalCommission.onboardingDate,
      officialDate: officialDate || undefined,
    };

    return this.create(officialCommissionDto);
  }

  async approve(id: number, approveDto: ApproveCommissionDto): Promise<EmployerCommission> {
    const commission = await this.employerCommissionRepository.findOne({
      where: { id }
    });

    if (!commission) {
      throw new NotFoundException(`Employer commission with ID "${id}" not found`);
    }

    if (commission.status === CommissionStatus.APPROVED) {
      throw new ConflictException('Commission is already approved');
    }

    commission.status = CommissionStatus.APPROVED;
    commission.approvedBy = this.context.user?.email || '';
    commission.approvedAt = new Date();

    if (approveDto.notes) {
      commission.notes = approveDto.notes;
    }

    return this.employerCommissionRepository.save(commission);
  }
}

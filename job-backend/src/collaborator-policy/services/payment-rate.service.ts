import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentRate } from '../entities/payment-rate.entity';
import { CreatePaymentRateDto } from '../dto/create-payment-rate.dto';
import { UpdatePaymentRateDto } from '../dto/update-payment-rate.dto';
import { FindPaymentRateDto } from '../dto/find-payment-rate.dto';
import { PaginatedResult } from '../../common/dto/pagination.dto';
import { EmploymentType } from '../enums/employment-type.enum';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { PaymentPhase } from '../enums/payment-phase.enum';
import { CommissionPhase } from '../enums/commission-phase.enum';

@Injectable()
export class PaymentRateService {
  constructor(
    @InjectRepository(PaymentRate)
    private readonly paymentRateRepository: Repository<PaymentRate>,
  ) {}

  async create(createPaymentRateDto: CreatePaymentRateDto, currentUserEmail?: string): Promise<PaymentRate> {
    const paymentRate = this.paymentRateRepository.create({
      ...createPaymentRateDto,
      createdBy: currentUserEmail,
      updatedBy: currentUserEmail,
    });

    return this.paymentRateRepository.save(paymentRate);
  }

  async findAll(filters?: FindPaymentRateDto): Promise<PaginatedResult<PaymentRate>> {
    const queryBuilder = this.paymentRateRepository.createQueryBuilder('paymentRate');

    if (filters?.employmentType) {
      queryBuilder.andWhere('paymentRate.employmentType = :employmentType', { 
        employmentType: filters.employmentType 
      });
    }

    if (filters?.level) {
      queryBuilder.andWhere('paymentRate.level = :level', { level: filters.level });
    }

    if (filters?.englishSkill) {
      queryBuilder.andWhere('paymentRate.englishSkill = :englishSkill', { 
        englishSkill: filters.englishSkill 
      });
    }

    if (filters?.paymentPhase) {
      queryBuilder.andWhere('paymentRate.paymentPhase = :paymentPhase', { 
        paymentPhase: filters.paymentPhase 
      });
    }

    if (filters?.isActive !== undefined) {
      const isActive = filters.isActive === 'true';
      queryBuilder.andWhere('paymentRate.isActive = :isActive', { isActive });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(paymentRate.employmentType LIKE :search OR paymentRate.level LIKE :search OR paymentRate.englishSkill LIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    // Sorting
    const sortBy = filters?.sortBy || 'createdAt';
    const sortOrder = filters?.sortOrder || 'DESC';
    queryBuilder.orderBy(`paymentRate.${sortBy}`, sortOrder);

    const totalCount = await queryBuilder.getCount();

    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    if (limit !== -1) {
      queryBuilder.limit(limit);
      if (page !== -1) {
        queryBuilder.offset((page - 1) * limit);
      }
    }

    const paymentRates = await queryBuilder.getMany();

    return new PaginatedResult(paymentRates, totalCount, page, limit);
  }

  async findOne(id: number): Promise<PaymentRate> {
    const paymentRate = await this.paymentRateRepository.findOne({ where: { id } });
    if (!paymentRate) {
      throw new NotFoundException(`Payment rate with ID "${id}" not found`);
    }
    return paymentRate;
  }

  async findByCriteria(
    employmentType: string,
    candidateLevel?: string,
    englishSkill?: string,
    commissionPhase?: string
  ): Promise<PaymentRate | null> {
    try {
      if (!employmentType || !commissionPhase) {
        return null;
      }

      let paymentPhase: PaymentPhase;
      switch (commissionPhase) {
        case CommissionPhase.INTERVIEW_PASS:
          paymentPhase = PaymentPhase.INTERVIEW_PASS;
          break;
        case CommissionPhase.ONBOARDING:
          paymentPhase = PaymentPhase.ONBOARDING;
          break;
        case CommissionPhase.OFFICIAL:
          paymentPhase = PaymentPhase.OFFICIAL;
          break;
        default:
          return null;
      }

      const whereConditions: any = {
        employmentType: employmentType as EmploymentType,
        paymentPhase,
        isActive: true,
      };

      // For FULLTIME employment, level and englishSkill are required
      if (employmentType === EmploymentType.FULLTIME) {
        if (!candidateLevel || !englishSkill) {
          return null;
        }
        whereConditions.level = candidateLevel as Level;
        whereConditions.englishSkill = englishSkill as EnglishSkill;
      }

      const paymentRate = await this.paymentRateRepository.findOne({
        where: whereConditions,
      });

      return paymentRate || null;
    } catch (error) {
      console.error('Error finding payment rate by criteria:', error);
      return null;
    }
  }

  async update(id: number, updatePaymentRateDto: UpdatePaymentRateDto, currentUserEmail?: string): Promise<PaymentRate> {
    const paymentRate = await this.findOne(id);
    
    const updatedPaymentRate = {
      ...paymentRate,
      ...updatePaymentRateDto,
      updatedBy: currentUserEmail,
    };

    return this.paymentRateRepository.save(updatedPaymentRate);
  }

  async remove(id: number): Promise<void> {
    const result = await this.paymentRateRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Payment rate with ID "${id}" not found`);
    }
  }

  async seedData(currentUserEmail: string = 'system'): Promise<{ message: string }> {
    const seedData = [
      // FULLTIME - JUNIOR
      { employmentType: EmploymentType.FULLTIME, level: Level.JUNIOR, englishSkill: EnglishSkill.FLUENT, paymentPhase: PaymentPhase.ONBOARDING, amount: 1000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.JUNIOR, englishSkill: EnglishSkill.FLUENT, paymentPhase: PaymentPhase.OFFICIAL, amount: 2000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.JUNIOR, englishSkill: EnglishSkill.NON, paymentPhase: PaymentPhase.ONBOARDING, amount: 500000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.JUNIOR, englishSkill: EnglishSkill.NON, paymentPhase: PaymentPhase.OFFICIAL, amount: 1500000, isActive: true },
      
      // FULLTIME - MIDDLE
      { employmentType: EmploymentType.FULLTIME, level: Level.MIDDLE, englishSkill: EnglishSkill.FLUENT, paymentPhase: PaymentPhase.ONBOARDING, amount: 2000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.MIDDLE, englishSkill: EnglishSkill.FLUENT, paymentPhase: PaymentPhase.OFFICIAL, amount: 3000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.MIDDLE, englishSkill: EnglishSkill.NON, paymentPhase: PaymentPhase.ONBOARDING, amount: 1000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.MIDDLE, englishSkill: EnglishSkill.NON, paymentPhase: PaymentPhase.OFFICIAL, amount: 2500000, isActive: true },
      
      // FULLTIME - SENIOR
      { employmentType: EmploymentType.FULLTIME, level: Level.SENIOR, englishSkill: EnglishSkill.FLUENT, paymentPhase: PaymentPhase.ONBOARDING, amount: 3000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.SENIOR, englishSkill: EnglishSkill.FLUENT, paymentPhase: PaymentPhase.OFFICIAL, amount: 5000000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.SENIOR, englishSkill: EnglishSkill.NON, paymentPhase: PaymentPhase.ONBOARDING, amount: 1500000, isActive: true },
      { employmentType: EmploymentType.FULLTIME, level: Level.SENIOR, englishSkill: EnglishSkill.NON, paymentPhase: PaymentPhase.OFFICIAL, amount: 3500000, isActive: true },
      
      // FREELANCER
      { employmentType: EmploymentType.FREELANCER, level: undefined, englishSkill: undefined, paymentPhase: PaymentPhase.INTERVIEW_PASS, amount: 300000, isActive: true },
      { employmentType: EmploymentType.FREELANCER, level: undefined, englishSkill: undefined, paymentPhase: PaymentPhase.ONBOARDING, amount: 700000, isActive: true },
    ];

    await this.paymentRateRepository.delete({});

    for (const data of seedData) {
      const paymentRate = this.paymentRateRepository.create({
        ...data,
        createdBy: currentUserEmail,
        updatedBy: currentUserEmail,
      });
      await this.paymentRateRepository.save(paymentRate);
    }

    return { message: 'Payment rates seeded successfully' };
  }
}

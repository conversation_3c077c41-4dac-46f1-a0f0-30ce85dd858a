import { Entity, Column, <PERSON><PERSON>o<PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseSoftDeleteEntity } from '../../common/entities/base.entity';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { CommissionPhase } from '../enums/commission-phase.enum';
import { CommissionStatus } from '../enums/commission-status.enum';
import { PaymentStatus } from '../enums/payment-status.enum';
import { EmploymentType } from '../enums/employment-type.enum';
import { CommissionReport } from './commission-report.entity';

@Entity('employer_commissions')
export class EmployerCommission extends BaseSoftDeleteEntity {

  @Column()
  employerEmail: string;

  @Column()
  jobApplicationId: number;

  @ManyToOne(() => CommissionReport, (report) => report.commissions, { nullable: true })
  @JoinColumn()
  report: CommissionReport;

  @Column({
    type: 'enum',
    enum: EmploymentType
  })
  employmentType: EmploymentType;

  @Column({
    type: 'enum',
    enum: Level,
    nullable: true
  })
  candidateLevel: Level;

  @Column({
    type: 'enum',
    enum: EnglishSkill,
    nullable: true
  })
  englishSkill: EnglishSkill;

  @Column({
    type: 'enum',
    enum: CommissionPhase
  })
  commissionPhase: CommissionPhase;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: CommissionStatus,
    default: CommissionStatus.PENDING
  })
  status: CommissionStatus;

  @Column({ type: 'datetime', nullable: true })
  onboardingDate: Date;

  @Column({ type: 'datetime', nullable: true })
  officialDate: Date;

  @Column({ nullable: true })
  approvedBy: string;

  @Column({ type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;
}
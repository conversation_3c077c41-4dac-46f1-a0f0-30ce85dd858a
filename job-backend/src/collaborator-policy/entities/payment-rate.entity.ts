import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
import { EmploymentType } from '../enums/employment-type.enum';
import { Level } from '../enums/level.enum';
import { EnglishSkill } from '../enums/english-skill.enum';
import { PaymentPhase } from '../enums/payment-phase.enum';

@Entity('payment_rates')
export class PaymentRate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: EmploymentType
  })
  employmentType: EmploymentType;

  @Column({
    type: 'enum',
    enum: Level,
    nullable: true
  })
  level: Level;

  @Column({
    type: 'enum',
    enum: EnglishSkill,
    nullable: true
  })
  englishSkill: EnglishSkill;

  @Column({
    type: 'enum',
    enum: PaymentPhase
  })
  paymentPhase: PaymentPhase;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}
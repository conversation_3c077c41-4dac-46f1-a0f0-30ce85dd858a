import { Controller, Get, Post, Put, Delete, Body, Param, ParseIntPipe, UseGuards, Request, Query, Patch, UseInterceptors } from '@nestjs/common';
import { EmployerCommissionService } from '../services/employer-commission.service';
import { CreateEmployerCommissionDto } from '../dto/create-employer-commission.dto';
import { UpdateEmployerCommissionDto } from '../dto/update-employer-commission.dto';
import { FindEmployerCommissionDto } from '../dto/find-employer-commission.dto';
import { ApproveCommissionDto } from '../dto/approve-commission.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { LogActivity } from '../../activities/activity-log.decorator';
import { ActionType } from '../../activities/entities/activity-log.entity';
import { ActivityLogInterceptor } from '../../activities/activity-log.interceptor';

@Controller('employer-commissions')
@UseInterceptors(ActivityLogInterceptor)
@UseGuards(JwtAuthGuard)
export class EmployerCommissionController {
  constructor(private readonly employerCommissionService: EmployerCommissionService) {}

  @Post()
  @LogActivity({
    actionType: ActionType.CREATE,
    action: 'Create employer-commissions',
    resourceType: EmployerCommissionController.name,
    includeBody: true
  })
  create(@Body() createEmployerCommissionDto: CreateEmployerCommissionDto) {
    return this.employerCommissionService.create(createEmployerCommissionDto);
  }

  @Get()
  findAll(@Query() findEmployerCommissionDto: FindEmployerCommissionDto) {
    return this.employerCommissionService.findAll(findEmployerCommissionDto);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.employerCommissionService.findOne(id);
  }

  @Put(':id')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update employer-commissions',
    resourceType: EmployerCommissionController.name,
    includeBody: true
  })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmployerCommissionDto: UpdateEmployerCommissionDto,
  ) {
    return this.employerCommissionService.update(id, updateEmployerCommissionDto);
  }

  @Delete(':id')
  @LogActivity({
    actionType: ActionType.DELETE,
    action: 'Delete employer-commissions',
    resourceType: EmployerCommissionController.name
  })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.employerCommissionService.remove(id);
  }

  @Post(':id/copy-to-official')
  @LogActivity({
    actionType: ActionType.COPY,
    action: 'Copy employer-commissions to official',
    resourceType: EmployerCommissionController.name,
    includeBody: true
  })
  copyToOfficial(@Param('id', ParseIntPipe) id: number) {
    return this.employerCommissionService.copyToOfficial(id);
  }

  @Patch(':id/approve')
  @LogActivity({
    actionType: ActionType.APPROVE,
    action: 'Approve employer-commissions',
    resourceType: EmployerCommissionController.name
  })
  approve(
    @Param('id', ParseIntPipe) id: number,
    @Body() approveDto: ApproveCommissionDto,
  ) {
    return this.employerCommissionService.approve(id, approveDto);
  }
}

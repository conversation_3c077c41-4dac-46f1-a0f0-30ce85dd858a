import { Controller, Get, Post, Put, Delete, Body, Param, ParseIntPipe, UseGuards, Request, Query } from '@nestjs/common';
import { PaymentRateService } from '../services/payment-rate.service';
import { CreatePaymentRateDto } from '../dto/create-payment-rate.dto';
import { UpdatePaymentRateDto } from '../dto/update-payment-rate.dto';
import { FindPaymentRateDto } from '../dto/find-payment-rate.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('payment-rates')
@UseGuards(JwtAuthGuard)
export class PaymentRateController {
  constructor(private readonly paymentRateService: PaymentRateService) {}

  @Post()
  create(@Request() req, @Body() createPaymentRateDto: CreatePaymentRateDto) {
    return this.paymentRateService.create(createPaymentRateDto, req.user.email);
  }

  @Get()
  findAll(@Query() findPaymentRateDto: FindPaymentRateDto) {
    return this.paymentRateService.findAll(findPaymentRateDto);
  }

  @Get('by-criteria')
  findByCriteria(@Query() query: any) {
    const { employmentType, candidateLevel, englishSkill, commissionPhase } = query;
    return this.paymentRateService.findByCriteria(employmentType, candidateLevel, englishSkill, commissionPhase);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.paymentRateService.findOne(id);
  }

  @Put(':id')
  update(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePaymentRateDto: UpdatePaymentRateDto,
  ) {
    return this.paymentRateService.update(id, updatePaymentRateDto, req.user.email);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.paymentRateService.remove(id);
  }

  @Post('seed')
  seedData(@Request() req) {
    return this.paymentRateService.seedData(req.user.email);
  }
}

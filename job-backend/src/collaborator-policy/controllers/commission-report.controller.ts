import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  ParseIntPipe, 
  UseGuards, 
  Query, 
  Patch,
  UseInterceptors 
} from '@nestjs/common';
import { CommissionReportService } from '../services/commission-report.service';
import { CreateCommissionReportDto } from '../dto/create-commission-report.dto';
import { UpdateCommissionReportDto } from '../dto/update-commission-report.dto';
import { FindCommissionReportDto } from '../dto/find-commission-report.dto';
import { ApproveCommissionReportDto } from '../dto/approve-commission-report.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { LogActivity } from '../../activities/activity-log.decorator';
import { ActionType } from '../../activities/entities/activity-log.entity';
import { ActivityLogInterceptor } from '../../activities/activity-log.interceptor';
import { UpdatePaymentStatusDto } from '../dto/update-payment-status.dto';

@Controller('commission-reports')
@UseInterceptors(ActivityLogInterceptor)
@UseGuards(JwtAuthGuard)
export class CommissionReportController {
  constructor(private readonly commissionReportService: CommissionReportService) {}

  @Post()
  @LogActivity({
    actionType: ActionType.CREATE,
    action: 'Create commission-reports',
    resourceType: CommissionReportController.name,
    includeBody: true
  })
  create(@Body() createCommissionReportDto: CreateCommissionReportDto) {
    return this.commissionReportService.create(createCommissionReportDto);
  }

  @Get()
  findAll(@Query() findCommissionReportDto: FindCommissionReportDto) {
    return this.commissionReportService.findAll(findCommissionReportDto);
  }

  @Get('employer-emails')
  getEmployerEmails() {
    return this.commissionReportService.getEmployerEmails();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.commissionReportService.findOne(id);
  }

  @Put(':id')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update commission-reports',
    resourceType: CommissionReportController.name,
    includeBody: true
  })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCommissionReportDto: UpdateCommissionReportDto,
  ) {
    return this.commissionReportService.update(id, updateCommissionReportDto);
  }

  @Delete(':id')
  @LogActivity({
    actionType: ActionType.DELETE,
    action: 'Delete commission-reports',
    resourceType: CommissionReportController.name
  })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.commissionReportService.remove(id);
  }

  @Patch(':id/approve')
  @LogActivity({
    actionType: ActionType.APPROVE,
    action: 'Approve commission-reports',
    resourceType: CommissionReportController.name
  })
  approve(
    @Param('id', ParseIntPipe) id: number,
    @Body() approveDto: ApproveCommissionReportDto,
  ) {
    return this.commissionReportService.approve(id, approveDto);
  }

  @Patch(':id/mark-paid')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Mark commission-reports as paid',
    resourceType: CommissionReportController.name
  })
  markAsPaid(@Param('id', ParseIntPipe) id: number) {
    return this.commissionReportService.markAsPaid(id);
  }

  @Patch(':id/payment-status')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update payment status of employer-commissions',
    resourceType: CommissionReportController.name,
    includeBody: true
  })
  updatePaymentStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePaymentStatusDto: UpdatePaymentStatusDto,
  ) {
    return this.commissionReportService.updatePaymentStatus(id, updatePaymentStatusDto);
  }
}

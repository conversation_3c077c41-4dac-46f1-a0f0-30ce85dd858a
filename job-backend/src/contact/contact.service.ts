import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Contact } from './entities/contact.entity';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { ContactStatus } from './enums/contact-status.enum';
import { EmailService } from '../email/email.service';

@Injectable()
export class ContactService {
  constructor(
    @InjectRepository(Contact)
    private contactRepository: Repository<Contact>,
    private emailService: EmailService,
  ) {}

  async create(createContactDto: CreateContactDto) {
    const contact = this.contactRepository.create({
      ...createContactDto,
      status: ContactStatus.NEW,
    });
    const savedContact = await this.contactRepository.save(contact);
    
    // Send email notification
    await this.emailService.sendContactNotification(savedContact);
    
    return savedContact;
  }

  findAll() {
    return this.contactRepository.find();
  }

  findOne(id: number) {
    return this.contactRepository.findOneBy({ id });
  }

  async update(id: number, updateContactDto: UpdateContactDto) {
    await this.contactRepository.update(id, updateContactDto);
    return this.findOne(id);
  }

  remove(id: number) {
    return this.contactRepository.delete(id);
  }
}
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Job } from '../entities/job.entity';
import { jobsData } from '../../job-data/jobsData';
import { unaccent } from '../../utils/string.utils';
import { JobStatistics } from '../entities/job-statistics.entity';
import { JobStatisticsDto } from '../dto/job-statistics.dto';
import { FindJobDto } from '../dto/find-job.dto';
import { PaginatedResult } from '../../common/dto/pagination.dto';

@Injectable()
export class JobsService {
  constructor(
    @InjectRepository(Job)
    private jobsRepository: Repository<Job>,
    private dataSource: DataSource,
  ) {}

  async findAll(findJobDto?: FindJobDto): Promise<PaginatedResult<Job>> {
    const { page = 1, limit = 10, search, sortBy, sortOrder, category, status, type } = findJobDto || {};
    const queryBuilder = this.jobsRepository.createQueryBuilder('job');

    if (search) {
      queryBuilder.andWhere(
        '(LOWER(job.title) LIKE :search OR LOWER(job.description) LIKE :search)',
        { search: `%${search.toLowerCase()}%` }
      );
    }

    if (category) {
      queryBuilder.andWhere('job.category = :category', { category });
    }

    if (status) {
      queryBuilder.andWhere('job.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('job.type = :type', { type });
    }

    if (sortBy) {
      queryBuilder.orderBy(`job.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy('job.updatedAt', 'DESC').addOrderBy('job.createdAt', 'DESC');
    }

    const totalItems = await queryBuilder.getCount();

    if (limit !== -1) {
      queryBuilder.limit(limit);
      if (page !== -1) {
        queryBuilder.offset((page - 1) * limit);
      }
    }
    const items = await queryBuilder.getMany();

    return new PaginatedResult<Job>(items, totalItems, page, limit);
  }
  async userFindAll(): Promise<Job[]> {
    return this.jobsRepository.createQueryBuilder('job')
      .orderBy(`CASE WHEN job.status = 'OPEN' THEN 0 ELSE 1 END`, 'ASC')
      .addOrderBy('job.updatedAt', 'DESC')
      .addOrderBy('job.createdAt', 'DESC')
      .getMany();
  }
  async getJobStatistics(jobId: string): Promise<JobStatisticsDto> {
    const result = await this.dataSource.createQueryBuilder(Job, 'job')
      .leftJoinAndSelect('job.statistics', 'stats')
      .leftJoin('job_application', 'apps', 'apps.jobId = job.id')
      .select([
        'job.id AS jobId',
        'COALESCE(stats.totalViews, 0) AS totalViews',
        'COUNT(DISTINCT apps.id) AS applications'
      ])
      .where('job.id = :jobId', { jobId })
      .groupBy('job.id')
      .addGroupBy('stats.totalViews')
      .getRawOne();

    if (!result) {
      throw new NotFoundException(`Không tìm thấy thống kê cho job có ID: ${jobId}`);
    }

    return {
      jobId: result.jobId,
      totalViews: parseInt(result.totalViews, 10),
      applications: parseInt(result.applications, 10)
    };
  }

  async findOne(id: string): Promise<Job> {
    const job = await this.jobsRepository.findOne({ where: { id } });
    if (!job) {
      throw new NotFoundException(`Job with ID "${id}" not found`);
    }
    return job;
  }

  async findByIdOrSlug(id: string): Promise<Job> {
    // Check if id is a valid UUID
    const isUUID =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        id,
      );

    let job: Job | null = null;

    if (isUUID) {
      job = await this.jobsRepository.findOne({ where: { id } });
    }

    // If not found by UUID or not a UUID, try to find by slug
    if (!job) {
      job = await this.jobsRepository.findOne({ where: { slug: id } });
    }

    if (!job) {
      throw new NotFoundException(`Job with ID or slug "${id}" not found`);
    }

    return job;
  }

  async create(job: Partial<Job>, email: string): Promise<Job> {
    let slug = '';
    if (job.title) {
      slug = unaccent(job.title);
      // Check if slug already exists
      let counter = 1;
      let existingJob = await this.jobsRepository.findOne({ where: { slug } });
      while (existingJob) {
        slug = `${unaccent(job.title)}-${counter}`;
        existingJob = await this.jobsRepository.findOne({ where: { slug } });
        counter++;
      }
    }

    const newJob = this.jobsRepository.create({
      ...job,
      slug,
      createdBy: email,
      updatedBy: email,
    });

    const jobStatistics = new JobStatistics();
    jobStatistics.totalViews = 0;
    newJob.statistics = jobStatistics;

    return this.jobsRepository.save(newJob);
  }

  async update(id: string, job: Partial<Job>, email: string): Promise<Job> {
    if (job.title) {
      let slug = unaccent(job.title);
      // Check if slug already exists and isn't this job's slug
      const currentJob = await this.jobsRepository.findOne({ where: { id } });
      if (currentJob && slug !== currentJob.slug) {
        let counter = 1;
        let existingJob = await this.jobsRepository.findOne({
          where: { slug },
        });
        while (existingJob && existingJob.id !== id) {
          slug = `${unaccent(job.title)}-${counter}`;
          existingJob = await this.jobsRepository.findOne({ where: { slug } });
          counter++;
        }
      }
      job.slug = slug;
    }

    await this.jobsRepository.update(id, {
      ...job,
      updatedBy: email,
    });
    const updatedJob = await this.jobsRepository.findOne({ where: { id } });
    if (!updatedJob) {
      throw new NotFoundException(`Job with ID "${id}" not found`);
    }
    return updatedJob;
  }

  async remove(id: string): Promise<void> {
    await this.jobsRepository.delete(id);
  }

  async seedData(): Promise<void> {
    for (const job of jobsData) {
      const exists = await this.jobsRepository.findOne({
        where: { code: job.code },
      });
      if (!exists) {
        await this.create(job, '<EMAIL>');
      }
    }
  }

  async incrementTotalViews(jobId: string): Promise<number> {
    const job = await this.jobsRepository.findOne({
      where: { id: jobId },
      relations: ['statistics'],
    });
    if (!job) {
      throw new NotFoundException(`Job with ID "${jobId}" not found`);
    }
    if (!job.statistics) {
      job.statistics = new JobStatistics();
      job.statistics.totalViews = 0;
    }
    job.statistics.totalViews += 1;
    await this.jobsRepository.manager.save(job.statistics);
    return job.statistics.totalViews;
  }
}

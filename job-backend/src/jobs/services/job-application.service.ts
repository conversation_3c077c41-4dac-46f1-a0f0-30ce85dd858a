import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobApplication } from '../entities/job-application.entity';
import { FindJobApplicationDto } from '../dto/find-job-application.dto';
import { ApplicationStatus } from '../enums/application-status.enum';
import { EmailService } from '../../email/email.service';
import * as fs from 'fs';
import { JobApplicationDetailDto } from '../dto/job-application-detail.dto';
import { plainToInstance } from 'class-transformer';
import { ApplicantStats, MonthData } from '../dto/applicant-stats.dto';
import { ProcessStatus } from '../enums/process-status.enum';
import { Job } from '../entities/job.entity';
import { JobStatus } from '../enums/job-status.enum';
import { PaginatedResult } from '../../common/dto/pagination.dto';
import { UserRole } from 'src/users/enums/user-role.enum';
import { EmployerCommissionService } from '../../collaborator-policy/services/employer-commission.service';
import { CommissionPhase } from '../../collaborator-policy/enums/commission-phase.enum';

import { EmploymentType } from '../../collaborator-policy/enums/employment-type.enum';

@Injectable()
export class JobApplicationService {
  constructor(
    @InjectRepository(JobApplication)
    private jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
    private emailService: EmailService,
    private employerCommissionService: EmployerCommissionService,
  ) {}

  async create(jobApplication: Partial<JobApplication>, sendEmail: boolean = false, userRole?: string): Promise<JobApplication> {
    const isEmployer = (userRole?.toLowerCase() === UserRole.EMPLOYER.toLowerCase());
    const newApplication = {
      ...jobApplication,
      status: ApplicationStatus.NEW,
      refPerson: isEmployer ? jobApplication.createdBy : jobApplication.refPerson,
    };
    const savedApplication = await this.jobApplicationRepository.save(newApplication as JobApplication);

    // Do not send email notification
    if (sendEmail || isEmployer) {
      this.emailService.sendJobApplicationNotification(await this.findOne(savedApplication.id));
    }

    return savedApplication;
  }

  //send received email to applicant because submit application from cms dont send email
  async sendReceivedEmail(id: number, updatedBy: string): Promise<void> {
    const application = await this.findOne(id);
    //change application status to received
    application.processStatus = ProcessStatus.RECEIVED_CV;
    await this.update(id, application, updatedBy);
    //send email to applicant
    this.emailService.sendJobApplicationNotification(application);
  }

  async findAll(filters?: FindJobApplicationDto): Promise<PaginatedResult<JobApplicationDetailDto>> {
    console.log("filters", filters);
    console.log("--------------------------------");
    const queryBuilder = this.jobApplicationRepository
      .createQueryBuilder('jobApplication')
      .select([
        'jobApplication.*',
        'job.id as jobId',
        'job.code as jobCode',
        'job.title as jobTitle',
        'job.type as jobType',
        'job.location as jobLocation',
        'job.status as jobStatus',
        'job.timeAlive as jobTimeAlive',
        'job.salary as jobSalary',
        'job.createdAt as jobCreatedAt'
      ])
      .leftJoin('jobs', 'job', 'job.id  = jobApplication.jobId');

    if (filters?.status) {
      queryBuilder.andWhere('jobApplication.status = :status', { status: filters.status });
    }

    if (filters?.processStatus) {
      queryBuilder.andWhere('jobApplication.processStatus = :processStatus', { processStatus: filters.processStatus });
    }

    if (filters?.createdBy) {
      queryBuilder.andWhere('jobApplication.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    if (filters?.startDate) {
      queryBuilder.andWhere('jobApplication.createdAt >= :startDate', { 
        startDate: new Date(filters.startDate) 
      });
    }

    if (filters?.endDate) {
      queryBuilder.andWhere('jobApplication.createdAt <= :endDate', { 
        endDate: new Date(filters.endDate) 
      });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(LOWER(job.title) LIKE LOWER(:search) OR LOWER(job.code) LIKE LOWER(:search) OR LOWER(job.location) LIKE LOWER(:search))',
        { search: `%${filters.search}%` }
      );
    }

    if (filters?.sortBy && filters?.sortOrder) {
      const allowedSortFields = {
        'createdAt': 'jobApplication.createdAt',
        'jobTitle': 'job.title',
        'status': 'jobApplication.status',
        'processStatus': 'jobApplication.processStatus',
      };
      
      const field = allowedSortFields[filters.sortBy];
      if (field) {
        queryBuilder.orderBy(field, filters.sortOrder);
      }
    } else {
      queryBuilder.addOrderBy(`CASE
        WHEN jobApplication.status = '${ApplicationStatus.NEW}' THEN 0
        WHEN jobApplication.status = '${ApplicationStatus.REJECT}' THEN 2
        ELSE 1
      END`, 'ASC');
      queryBuilder.addOrderBy('jobApplication.createdAt', 'DESC');
    }

    const totalCount = await queryBuilder.getCount();

    // Handle pagination
    const page = filters?.page !== undefined ? Number(filters.page) : 1;
    const limit = filters?.limit !== undefined ? Number(filters.limit) : 10;

    console.log('page', page);
    console.log('limit', limit);

    // Apply pagination if limit is valid
    if (limit > 0) {
      console.log('page * limit', page * limit);
      // Force TypeORM to add LIMIT and OFFSET to the query
      queryBuilder
        .limit(limit)
        .offset((page - 1) * limit);
    }

    const applications = await queryBuilder.getRawMany();
    const transformedApplications = plainToInstance(JobApplicationDetailDto, applications);
    
    return new PaginatedResult(
      transformedApplications,
      totalCount,
      page,
      limit
    );
  }

  async findForCommission(refPerson?: string): Promise<JobApplicationDetailDto[]> {
    const queryBuilder = this.jobApplicationRepository
      .createQueryBuilder('jobApplication')
      .select([
        'jobApplication.*',
        'job.id as jobId',
        'job.code as jobCode',
        'job.title as jobTitle',
        'job.type as jobType',
        'job.location as jobLocation',
        'job.status as jobStatus',
        'job.timeAlive as jobTimeAlive',
        'job.salary as jobSalary',
        'job.createdAt as jobCreatedAt'
      ])
      .leftJoin('jobs', 'job', 'job.id = jobApplication.jobId')
      .where("jobApplication.refPerson IS NOT NULL AND TRIM(jobApplication.refPerson) != ''");

    if (refPerson) {
      queryBuilder.andWhere('jobApplication.refPerson LIKE :refPerson', {
        refPerson: `%${refPerson}%`
      });
    }

    queryBuilder.orderBy('jobApplication.createdAt', 'DESC');

    const applications = await queryBuilder.getRawMany();
    return plainToInstance(JobApplicationDetailDto, applications);
  }

  async findOne(id: number): Promise<JobApplicationDetailDto> {
    const application = await this.jobApplicationRepository
      .createQueryBuilder('jobApplication')
      .select([
        'jobApplication.*',
        'job.id as jobId',
        'job.code as jobCode',
        'job.title as jobTitle',
        'job.type as jobType',
        'job.location as jobLocation',
        'job.softSkills as jobSoftSkills',
        'job.status as jobStatus',
        'job.timeAlive as jobTimeAlive',
        'job.salary as jobSalary',
        'job.createdAt as jobCreatedAt'
      ])
      .leftJoin('jobs', 'job', 'job.id = jobApplication.jobId')
      .where('jobApplication.id = :id', { id })
      .getRawOne();

    if (!application) {
      throw new NotFoundException(`Job application with ID ${id} not found`);
    }

    return plainToInstance(JobApplicationDetailDto, application);
  }

  async getCvFilePath(id: number): Promise<string> {
    const application = await this.findOne(id);

    if (!application.cvFile) {
      throw new NotFoundException('No CV file found for this application');
    }

    return application.cvFile;
  }

  async update(id: number, updateData: Partial<JobApplication>, updatedBy: string): Promise<JobApplication> {
    const application = await this.findOne(id);

    if (!application) {
      throw new NotFoundException(`Job application with ID ${id} not found`);
    }

    if (updateData.status === ApplicationStatus.REJECT) {
      if (!updateData.processStatus || ![ProcessStatus.CANCELLED, ProcessStatus.FAILED].includes(updateData.processStatus)) {
        updateData.processStatus = ProcessStatus.CANCELLED;
      }
    }

    const updatedApplication = {
      ...application,
      ...updateData,
      updatedBy: updatedBy
    };

    const savedApplication = await this.jobApplicationRepository.save(updatedApplication);

    // Auto-create employer commission when processStatus changes to PASSED or ONBOARD
    if (updateData.processStatus && application.refPerson) {
      await this.handleCommissionCreation(savedApplication, updateData.processStatus);
    }

    return savedApplication;
  }

  private async handleCommissionCreation(
    application: JobApplication,
    newProcessStatus: ProcessStatus
  ): Promise<void> {
    try {
      let commissionPhase: CommissionPhase | null = null;

      switch (newProcessStatus) {
        case ProcessStatus.PASSED:
          commissionPhase = CommissionPhase.INTERVIEW_PASS;
          break;
        case ProcessStatus.ONBOARD:
          commissionPhase = CommissionPhase.ONBOARDING;
          break;
        default:
          return;
      }

      const jobApplication = await this.findOne(application.id);

      const employerEmail = application.refPerson;
      const employmentType = jobApplication.jobType && ['full-time', 'internship'].includes(jobApplication.jobType.toLowerCase()) 
      ? EmploymentType.FULLTIME 
      : EmploymentType.FREELANCER;

      await this.employerCommissionService.autoCreateCommission(
        application.id,
        employerEmail,
        application.candidateLevel,
        application.englishSkill,
        commissionPhase,
        employmentType
      );
    } catch (error) {
      console.error('Error creating commission:', error);
    }
  }

  async reject(id: number, updateData: Partial<JobApplication>, updatedBy: string): Promise<JobApplication> {
   
    const application = await this.findOne(id);

    if (!application) {
      throw new NotFoundException(`Job application with ID ${id} not found`);
    }
    
    application.processStatus = ProcessStatus.CANCELLED;
    application.status = ApplicationStatus.REJECT;
    this.emailService.sendRejectionEmail(application);

    const updatedApplication = {
      ...application,
      ...updateData,
      updatedBy: updatedBy
    };

    return this.jobApplicationRepository.save(updatedApplication);
  }

  async remove(id: number): Promise<void> {
    const application = await this.findOne(id);

    // Delete the CV file if it exists
    if (application.cvFile) {
      try {
        await fs.promises.unlink(application.cvFile);
      } catch (error) {
        // Log the error but continue with deletion
        console.error(`Error deleting CV file: ${error.message}`);
      }
    }

    const result = await this.jobApplicationRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Job application with ID ${id} not found`);
    }
  }

  async getApplicantStats(months: number, filters?: FindJobApplicationDto): Promise<ApplicantStats> {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const baseQuery = this.getBaseQuery(filters);

    // 1. Get total active jobs
    const jobsQuery = this.jobRepository.createQueryBuilder('job')
      .where('job.status = :status', { status: JobStatus.OPEN });
    const totalActiveJobs = await jobsQuery.getCount();

    // 2. Get current month applicants
    const currentMonthStart = new Date(currentYear, currentMonth, 1);
    const nextMonthStart = new Date(currentYear, currentMonth + 1, 1);

    const currentMonthQuery = baseQuery.clone()
      .select('COUNT(jobApplication.id)', 'count')
      .andWhere('jobApplication.createdAt >= :startDate', { startDate: currentMonthStart })
      .andWhere('jobApplication.createdAt < :endDate', { endDate: nextMonthStart });

    const currentMonthResult = await currentMonthQuery.getRawOne();
    const currentMonthApplicants = parseInt(currentMonthResult?.count || '0');

    // 3. Get total applicants count
    const totalApplicantsQuery = baseQuery.clone()
      .select('COUNT(jobApplication.id)', 'count');

    const totalApplicantsResult = await totalApplicantsQuery.getRawOne();
    const totalApplicants = parseInt(totalApplicantsResult?.count || '0');

    // 4. Get process status counts
    const statusCountsQuery = baseQuery.clone()
      .select([
        `SUM(CASE
          WHEN jobApplication.processStatus IS NULL OR
               jobApplication.processStatus = '${ProcessStatus.INVITED}' OR
               jobApplication.processStatus = '${ProcessStatus.INTERVIEWED}'
          THEN 1 ELSE 0 END) AS inProgress`,
        `SUM(CASE
          WHEN jobApplication.processStatus = '${ProcessStatus.PASSED}' OR
               jobApplication.processStatus = '${ProcessStatus.ONBOARD}'
          THEN 1 ELSE 0 END) AS passed`,
        `SUM(CASE
          WHEN jobApplication.processStatus = '${ProcessStatus.FAILED}' OR
               jobApplication.processStatus = '${ProcessStatus.CANCELLED}'
          THEN 1 ELSE 0 END) AS failed`
      ]);

    const statusCounts = await statusCountsQuery.getRawOne();
    const totalInProgress = parseInt(statusCounts?.inProgress || '0');
    const totalPass = parseInt(statusCounts?.passed || '0');
    const totalFail = parseInt(statusCounts?.failed || '0');

    // 5. Get monthly statistics in N months
    const startDate = new Date(currentDate);
    startDate.setMonth(currentDate.getMonth() - months + 1);
    startDate.setDate(1);
    startDate.setHours(0, 0, 0, 0);

    const monthlyCountsQuery = baseQuery.clone()
      .select([
        'MONTH(jobApplication.createdAt) as month',
        'YEAR(jobApplication.createdAt) as year',
        'COUNT(jobApplication.id) as count'
      ])
      .andWhere('jobApplication.createdAt >= :startDate', { startDate })
      .groupBy('YEAR(jobApplication.createdAt), MONTH(jobApplication.createdAt)');

    const monthlyCounts = await monthlyCountsQuery.getRawMany();
    const monthsData = this.getStructureMonthData(months, currentDate);
    monthlyCounts.forEach(item => {
      const monthData = monthsData.find(m =>
        m.month === (parseInt(item.month) - 1) &&
        m.year === parseInt(item.year)
      );

      if (monthData) {
        monthData.count = parseInt(item.count);
      }
    });

    return {
      monthsData,
      totalActiveJobs,
      totalApplicants,
      currentMonthApplicants,
      totalPass,
      totalFail,
      totalInProgress
    };
  }

  private getBaseQuery(filters?: FindJobApplicationDto) {
    const baseQuery = this.jobApplicationRepository.createQueryBuilder('jobApplication');

    if (filters?.status) {
      baseQuery.andWhere('jobApplication.status = :status', { status: filters.status });
    }

    if (filters?.processStatus) {
      baseQuery.andWhere('jobApplication.processStatus = :processStatus', { processStatus: filters.processStatus });
    }

    if (filters?.createdBy) {
      baseQuery.andWhere('jobApplication.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    return baseQuery;
  }

  private getStructureMonthData(months: number, currentDate: Date): MonthData[] {
    const monthsData: MonthData[] = [];

    for (let i = 0; i < months; i++) {
      const date = new Date(currentDate);
      date.setMonth(currentDate.getMonth() - i);
      date.setDate(1);
      date.setHours(0, 0, 0, 0);

      monthsData.unshift({
        month: date.getMonth(),
        year: date.getFullYear(),
        count: 0,
        label: `${date.toLocaleString('en-US', { month: 'short' })} ${date.getFullYear()}`
      });
    }

    return monthsData;
  }
}
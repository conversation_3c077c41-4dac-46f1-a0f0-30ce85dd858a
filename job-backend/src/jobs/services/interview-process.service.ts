import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InterviewProcess } from '../entities/interview-process.entity';
import { CreateInterviewProcessDto } from '../dto/create-interview-process.dto';
import { UpdateInterviewProcessDto } from '../dto/update-interview-process.dto';
import { UpdateInterviewRound1Dto } from '../dto/update-interview-round1.dto';
import { UpdateInterviewRound2Dto } from '../dto/update-interview-round2.dto';
import { EmailService } from '../../email/email.service';
import { JobApplicationService } from './job-application.service';

@Injectable()
export class InterviewProcessService {
  constructor(
    @InjectRepository(InterviewProcess)
    private interviewProcessRepository: Repository<InterviewProcess>,
    private emailService: EmailService,
    private jobApplicationService: JobApplicationService,
  ) {}

  async create(createInterviewProcessDto: CreateInterviewProcessDto): Promise<InterviewProcess> {
    const interviewProcess = this.interviewProcessRepository.create(createInterviewProcessDto);
    return this.interviewProcessRepository.save(interviewProcess);
  }

  async findAll(): Promise<InterviewProcess[]> {
    return this.interviewProcessRepository.find();
  }

  async findByJobApplication(jobApplicationId: number): Promise<InterviewProcess[]> {
    return this.interviewProcessRepository.find({ where: { jobApplicationId } });
  }

  async getInterviewProcess(jobApplicationId: number): Promise<InterviewProcess> {
    const interviewProcess = await this.interviewProcessRepository.findOne({
      where: { jobApplicationId }
    });

    if (!interviewProcess) {
      throw new NotFoundException(`Interview process for job application ID ${jobApplicationId} not found`);
    }

    return interviewProcess;
  }

  async findOne(id: number): Promise<InterviewProcess> {
    const interviewProcess = await this.interviewProcessRepository.findOne({ where: { id } });
    if (!interviewProcess) {
      throw new NotFoundException(`Interview process with ID ${id} not found`);
    }
    return interviewProcess;
  }

  async update(id: number, updateInterviewProcessDto: UpdateInterviewProcessDto): Promise<InterviewProcess> {
    const interviewProcess = await this.findOne(id);

    Object.assign(interviewProcess, updateInterviewProcessDto);

    return this.interviewProcessRepository.save(interviewProcess);
  }

  async updateRound1(id: number, updateRound1Dto: UpdateInterviewRound1Dto): Promise<InterviewProcess> {
    const interviewProcess = await this.findOne(id);

    const isScheduleUpdated = updateRound1Dto.scheduleRound1 !== undefined &&
      (interviewProcess.scheduleRound1 === null ||
       this.areDatesNotEqual(interviewProcess.scheduleRound1, updateRound1Dto.scheduleRound1));

    // Only update Round 1 fields
    if (updateRound1Dto.scheduleRound1 !== undefined) interviewProcess.scheduleRound1 = updateRound1Dto.scheduleRound1;
    if (updateRound1Dto.interviewerRound1 !== undefined) interviewProcess.interviewerRound1 = updateRound1Dto.interviewerRound1;
    if (updateRound1Dto.contentRound1 !== undefined) interviewProcess.contentRound1 = updateRound1Dto.contentRound1;
    if (updateRound1Dto.reviewRound1 !== undefined) interviewProcess.reviewRound1 = updateRound1Dto.reviewRound1;
    if (updateRound1Dto.resultRound1 !== undefined) interviewProcess.resultRound1 = updateRound1Dto.resultRound1;
    if (updateRound1Dto.updatedBy !== undefined) interviewProcess.updatedBy = updateRound1Dto.updatedBy;

    const savedProcess = await this.interviewProcessRepository.save(interviewProcess);

    // Send email notification if schedule was updated and sendEmailRound1 is true
    if (
      isScheduleUpdated &&
      interviewProcess.jobApplicationId &&
      updateRound1Dto.sendEmailRound1
    ) {
      try {
        const jobApplication = await this.jobApplicationService.findOne(interviewProcess.jobApplicationId);
        await this.emailService.sendInterviewScheduleEmail(jobApplication, savedProcess, 1);
      } catch (error) {
        console.error('Failed to send interview schedule email:', error);
      }
    }

    return savedProcess;
  }

  async updateRound2(id: number, updateRound2Dto: UpdateInterviewRound2Dto): Promise<InterviewProcess> {
    const interviewProcess = await this.findOne(id);

    const isScheduleUpdated = updateRound2Dto.scheduleRound2 !== undefined &&
      (interviewProcess.scheduleRound2 === null ||
       this.areDatesNotEqual(interviewProcess.scheduleRound2, updateRound2Dto.scheduleRound2));

    // Only update Round 2 fields
    if (updateRound2Dto.scheduleRound2 !== undefined) interviewProcess.scheduleRound2 = updateRound2Dto.scheduleRound2;
    if (updateRound2Dto.interviewerRound2 !== undefined) interviewProcess.interviewerRound2 = updateRound2Dto.interviewerRound2;
    if (updateRound2Dto.contentRound2 !== undefined) interviewProcess.contentRound2 = updateRound2Dto.contentRound2;
    if (updateRound2Dto.reviewRound2 !== undefined) interviewProcess.reviewRound2 = updateRound2Dto.reviewRound2;
    if (updateRound2Dto.resultRound2 !== undefined) interviewProcess.resultRound2 = updateRound2Dto.resultRound2;
    if (updateRound2Dto.updatedBy !== undefined) interviewProcess.updatedBy = updateRound2Dto.updatedBy;

    const savedProcess = await this.interviewProcessRepository.save(interviewProcess);

    // Send email notification if schedule was updated and sendEmailRound2 is true
    if (
      isScheduleUpdated &&
      interviewProcess.jobApplicationId &&
      updateRound2Dto.sendEmailRound2
    ) {
      try {
        const jobApplication = await this.jobApplicationService.findOne(interviewProcess.jobApplicationId);
        await this.emailService.sendInterviewScheduleEmail(jobApplication, savedProcess, 2);
      } catch (error) {
        console.error('Failed to send interview schedule email:', error);
      }
    }

    return savedProcess;
  }

  async remove(id: number): Promise<void> {
    const result = await this.interviewProcessRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Interview process with ID ${id} not found`);
    }
  }

  private areDatesNotEqual(date1: Date, date2: Date | string): boolean {
    if (!date1 || !date2) return true;

    const d1 = new Date(date1);
    const year1 = d1.getUTCFullYear();
    const month1 = d1.getUTCMonth();
    const day1 = d1.getUTCDate();

    const d2 = new Date(date2);
    const year2 = d2.getUTCFullYear();
    const month2 = d2.getUTCMonth();
    const day2 = d2.getUTCDate();

    return (year1 !== year2 || month1 !== month2 || day1 !== day2);
  }
}
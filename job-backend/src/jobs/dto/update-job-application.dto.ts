import { ApplicationStatus } from '../enums/application-status.enum';
import { ProcessStatus } from '../enums/process-status.enum';
import { Level } from '../../collaborator-policy/enums/level.enum';
import { EnglishSkill } from '../../collaborator-policy/enums/english-skill.enum';

export class UpdateJobApplicationDto {
  status?: ApplicationStatus;
  processStatus?: ProcessStatus;
  notes?: string;
  refPerson?: string;
  candidateLevel?: Level;
  englishSkill?: EnglishSkill;
}
import { IsBoolean, IsDate, IsEmail, IsOptional, IsString, Max<PERSON><PERSON>th } from 'class-validator';

export class UpdateInterviewRound2Dto {
  @IsOptional()
  @IsDate()
  scheduleRound2?: Date;

  @IsOptional()
  @IsString()
  @IsEmail()
  interviewerRound2?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  contentRound2?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  reviewRound2?: string;

  @IsOptional()
  @IsBoolean()
  resultRound2?: boolean;

  @IsOptional()
  @IsString()
  updatedBy?: string;

  @IsOptional()
  @IsBoolean()
  sendEmailRound2?: boolean;
}
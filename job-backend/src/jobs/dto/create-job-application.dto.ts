import { IsString, IsEmail, IsOptional, IsEnum } from 'class-validator';
import { Level } from '../../collaborator-policy/enums/level.enum';
import { EnglishSkill } from '../../collaborator-policy/enums/english-skill.enum';

export class CreateJobApplicationDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @IsString()
  jobId: string;

  @IsString()
  @IsOptional()
  refPerson?: string;

  @IsEnum(Level)
  @IsOptional()
  candidateLevel?: Level;

  @IsEnum(EnglishSkill)
  @IsOptional()
  englishSkill?: EnglishSkill;
}
import { IsBoolean, IsDate, IsEmail, IsOptional, IsString, Max<PERSON><PERSON>th } from 'class-validator';

export class UpdateInterviewRound1Dto {
  @IsOptional()
  @IsDate()
  scheduleRound1?: Date;

  @IsOptional()
  @IsString()
  @IsEmail()
  interviewerRound1?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  contentRound1?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  reviewRound1?: string;

  @IsOptional()
  @IsBoolean()
  resultRound1?: boolean;

  @IsOptional()
  @IsString()
  updatedBy?: string;

  @IsOptional()
  @IsBoolean()
  sendEmailRound1?: boolean;
}
import { ApplicationStatus } from '../enums/application-status.enum';
import { ProcessStatus } from '../enums/process-status.enum';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BaseFilterDto } from '../../common/dto/base-filter.dto';

export class FindJobApplicationDto extends BaseFilterDto {
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @IsOptional()
  @IsEnum(ProcessStatus)
  processStatus?: ProcessStatus;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsString()
  createdBy?: string;

  @IsOptional()
  @IsString()
  refPerson?: string;
}

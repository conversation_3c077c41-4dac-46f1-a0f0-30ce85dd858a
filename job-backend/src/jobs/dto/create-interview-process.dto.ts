import { IsBoolean, IsDate, <PERSON>N<PERSON>ber, IsOptional, IsString, Max<PERSON>eng<PERSON> } from 'class-validator';

export class CreateInterviewProcessDto {
  @IsOptional()
  @IsDate()
  scheduleRound1?: Date;

  @IsOptional()
  @IsString()
  interviewerRound1?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  contentRound1?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  reviewRound1?: string;

  @IsOptional()
  @IsBoolean()
  resultRound1?: boolean;

  @IsOptional()
  @IsDate()
  scheduleRound2?: Date;

  @IsOptional()
  @IsString()
  interviewerRound2?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  contentRound2?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  reviewRound2?: string;

  @IsOptional()
  @IsBoolean()
  resultRound2?: boolean;

  @IsNumber()
  jobApplicationId: number;

  @IsOptional()
  @IsString()
  createdBy?: string;
} 
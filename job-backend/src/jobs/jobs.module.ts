import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobsService } from './services/jobs.service';
import { JobsController } from './controllers/jobs.controller';
import { Job } from './entities/job.entity';
import { JobApplication } from './entities/job-application.entity';
import { InterviewProcess } from './entities/interview-process.entity';
import { JobApplicationController } from './controllers/job-application.controller';
import { JobApplicationService } from './services/job-application.service';
import { InterviewProcessService } from './services/interview-process.service';
import { InterviewProcessController } from './controllers/interview-process.controller';
import { EmailModule } from '../email/email.module';
import { CollaboratorPolicyModule } from '../collaborator-policy/collaborator-policy.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Job, JobApplication, InterviewProcess]),
    EmailModule,
    CollaboratorPolicyModule,
  ],
  controllers: [
    Jobs<PERSON>ontroll<PERSON>,
    JobApplicationController,
    InterviewProcessController,
  ],
  providers: [
    JobsService,
    JobApplicationService,
    InterviewProcessService,
  ],
  exports: [JobsService, JobApplicationService, InterviewProcessService],
})
export class JobsModule {}
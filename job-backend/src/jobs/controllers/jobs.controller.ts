import { Controller, Get, Post, Put, Delete, Body, Param, Request, Query } from '@nestjs/common';
import { JobsService } from '../services/jobs.service';
import { Job } from '../entities/job.entity';
import { FindJobDto } from '../dto/find-job.dto';

@Controller('jobs')
export class JobsController {
  constructor(private readonly jobsService: JobsService) {}

  @Get()
  findAll(@Query() findJobDto: FindJobDto) {
    return this.jobsService.findAll(findJobDto);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.jobsService.findByIdOrSlug(id);
  }

  @Post()
  create(@Body() job: Partial<Job>, @Request() req) {
    return this.jobsService.create(job, req.user.email);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() job: Partial<Job>, @Request() req) {
    return this.jobsService.update(id, job, req.user.email);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.jobsService.remove(id);
  }

  @Get('seed/data')
  seedData() {
    return this.jobsService.seedData();
  }

  @Get(':id/statistics')
  async getJobStatistics(@Param('id') id: string) {
    return this.jobsService.getJobStatistics(id);
  }

}

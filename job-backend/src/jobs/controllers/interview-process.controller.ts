import { Controller, Get, Post, Body, Patch, Param, Delete, Request, UseInterceptors } from '@nestjs/common';
import { InterviewProcessService } from '../services/interview-process.service';
import { InterviewProcess } from '../entities/interview-process.entity';
import { CreateInterviewProcessDto } from '../dto/create-interview-process.dto';
import { UpdateInterviewProcessDto } from '../dto/update-interview-process.dto';
import { UpdateInterviewRound1Dto } from '../dto/update-interview-round1.dto';
import { UpdateInterviewRound2Dto } from '../dto/update-interview-round2.dto';
import { ActivityLogInterceptor } from '../../activities/activity-log.interceptor';
import { LogActivity } from '../../activities/activity-log.decorator';
import { ActionType } from '../../activities/entities/activity-log.entity';

@Controller('interview-process')
@UseInterceptors(ActivityLogInterceptor)
export class InterviewProcessController {
  constructor(private readonly interviewProcessService: InterviewProcessService) {}

  @Post()
  @LogActivity({
    actionType: ActionType.CREATE,
    action: 'Create interview-process',
    resourceType: InterviewProcessController.name,
    includeBody: true
  })
  create(@Body() createInterviewProcessDto: CreateInterviewProcessDto) {
    return this.interviewProcessService.create(createInterviewProcessDto);
  }

  @Get()
  findAll() {
    return this.interviewProcessService.findAll();
  }

  @Get('job-application/:jobApplicationId')
  findByJobApplication(@Param('jobApplicationId') jobApplicationId: string) {
    return this.interviewProcessService.findByJobApplication(+jobApplicationId);
  }

  @Get('by-job-application/:jobApplicationId')
  getInterviewProcess(@Param('jobApplicationId') jobApplicationId: string) {
    return this.interviewProcessService.getInterviewProcess(+jobApplicationId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.interviewProcessService.findOne(+id);
  }

  @Patch(':id')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update interview-process',
    resourceType: InterviewProcessController.name,
    includeBody: true
  })
  update(
    @Param('id') id: string,
    @Body() updateInterviewProcessDto: UpdateInterviewProcessDto,
  ) {
    return this.interviewProcessService.update(+id, updateInterviewProcessDto);
  }

  @Patch(':id/round1')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update interview-process round1',
    resourceType: InterviewProcessController.name,
    includeBody: true
  })
  updateRound1(
    @Request() req,
    @Param('id') id: string,
    @Body() updateRound1Dto: UpdateInterviewRound1Dto,
  ) {
    updateRound1Dto.updatedBy = req.user.email;
    return this.interviewProcessService.updateRound1(+id, updateRound1Dto);
  }

  @Patch(':id/round2')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update interview-process round2',
    resourceType: InterviewProcessController.name,
    includeBody: true
  })
  updateRound2(
    @Request() req,
    @Param('id') id: string,
    @Body() updateRound2Dto: UpdateInterviewRound2Dto,
  ) {
    updateRound2Dto.updatedBy = req.user.email;
    return this.interviewProcessService.updateRound2(+id, updateRound2Dto);
  }

  @Delete(':id')
  @LogActivity({
    actionType: ActionType.DELETE,
    action: 'Delete interview-process',
    resourceType: InterviewProcessController.name
  })
  remove(@Param('id') id: string) {
    return this.interviewProcessService.remove(+id);
  }
} 
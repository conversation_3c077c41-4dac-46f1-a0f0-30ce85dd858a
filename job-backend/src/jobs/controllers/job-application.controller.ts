import {
  Controller,
  Post,
  Get,
  Query,
  Body,
  Param,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  Res,
  NotFoundException,
  PayloadTooLargeException,
  Delete,
  Put,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response, Request as ExpressRequest } from 'express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as path from 'path';
import * as fs from 'fs';
import { JobApplicationService } from '../services/job-application.service';
import { FindJobApplicationDto } from '../dto/find-job-application.dto';
import { UpdateJobApplicationDto } from '../dto/update-job-application.dto';
import { CreateJobApplicationDto } from '../dto/create-job-application.dto';
import { UserRole } from '../../users/enums/user-role.enum';
import { unaccent } from 'src/utils/string.utils';
import { ApplicantStats } from '../dto/applicant-stats.dto';
import { ActivityLogInterceptor } from '../../activities/activity-log.interceptor';
import { LogActivity } from '../../activities/activity-log.decorator';
import { ActionType } from '../../activities/entities/activity-log.entity';
import { JobApplication } from '../entities/job-application.entity';

interface RequestWithUser extends ExpressRequest {
  user: {
    email: string;
    role: UserRole;
  };
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MONTHS_TO_FETCH = 5;

@Controller('job-applications')
@UseInterceptors(ActivityLogInterceptor)
export class JobApplicationController {
  constructor(private readonly jobApplicationService: JobApplicationService) {}

  @Post()
  @LogActivity({
    actionType: ActionType.CREATE,
    action: 'Create job-application',
    resourceType: JobApplication.name,
    includeBody: true
  })
  @UseInterceptors(
    FileInterceptor('cv', {
      storage: diskStorage({
        destination: './uploads/cv',
        filename: (req, file, callback) => {
          const applicantName = req.body.name || 'unnamed';
          const timestamp = Math.floor(Date.now() / 1000);
          const filename = `${unaccent(applicantName)}_${timestamp}${extname(file.originalname)}`;
          callback(null, filename);
        },
      }),
      limits: {
        fileSize: MAX_FILE_SIZE,
      },
      fileFilter: (req, file, callback) => {
        if (file.size > MAX_FILE_SIZE) {
          return callback(
            new PayloadTooLargeException('File size exceeds 10MB limit'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async create(
    @Body() createJobApplicationDto: CreateJobApplicationDto,
    @UploadedFile() file: Express.Multer.File,
    @Request() req
  ) {
    const cvFilePath = file ? file.path : undefined;
    return this.jobApplicationService.create({
      ...createJobApplicationDto,
      cvFile: cvFilePath,
      createdBy: req.user.email || '',
      updatedBy: req.user.email || '',
    }, false, req.user.role);
  }

  @Get()
  async findAll(@Query() filters: FindJobApplicationDto, @Request() req: RequestWithUser) {
    if (req.user?.role && req.user.role.toLowerCase() === UserRole.EMPLOYER.toLowerCase()) {
      filters.createdBy = req.user.email;
    }
    return this.jobApplicationService.findAll(filters);
  }

  @Get('for-commission')
  async findForCommission(@Query('refPerson') refPerson?: string) {
    return this.jobApplicationService.findForCommission(refPerson);
  }

  @Post(':id/send-received-email')
  async sendReceivedEmail(@Param('id', ParseIntPipe) id: number, @Request() req: RequestWithUser) {
    return this.jobApplicationService.sendReceivedEmail(id, req.user.email || '');
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.jobApplicationService.findOne(id);
  }

  @Get(':id/download-cv')
  async downloadCv(
    @Param('id', ParseIntPipe) id: number,
    @Res() res: Response
  ) {
    const cvPath = await this.jobApplicationService.getCvFilePath(id);

    if (!fs.existsSync(cvPath)) {
      throw new NotFoundException('CV file not found on server');
    }

    const filename = path.basename(cvPath);
    res.download(cvPath, filename);
  }

  @Put(':id')
  @LogActivity({
    actionType: ActionType.UPDATE,
    action: 'Update job application',
    resourceType: JobApplication.name,
    includeBody: true
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateJobApplicationDto: UpdateJobApplicationDto,
    @Request() req
  ) {
    return this.jobApplicationService.update(id, updateJobApplicationDto, req.user.email || '');
  }

  @Put(':id/reject')
  @LogActivity({
    actionType: ActionType.REJECT,
    action: 'Reject job application',
    resourceType: JobApplication.name,
  })
  async reject(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateJobApplicationDto: UpdateJobApplicationDto,
    @Request() req
  ) {
    return this.jobApplicationService.reject(id, updateJobApplicationDto, req.user.email || '');
  }

  @Delete(':id')
  @LogActivity({
    actionType: ActionType.DELETE,
    action: 'Delete job application',
    resourceType: JobApplication.name,
  })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.jobApplicationService.remove(id);
  }

  @Get('statistics/monthly')
  async getApplicantStats(@Request() req: RequestWithUser): Promise<ApplicantStats> {
    const filters: any = {};
    if (req.user?.role && req.user.role.toLowerCase() === UserRole.EMPLOYER.toLowerCase()) {
      filters.createdBy = req.user.email;
    }

    return this.jobApplicationService.getApplicantStats(MONTHS_TO_FETCH, filters);
  }
}
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApplicationStatus } from '../enums/application-status.enum';
import { ProcessStatus } from '../enums/process-status.enum';
import { Level } from '../../collaborator-policy/enums/level.enum';
import { EnglishSkill } from '../../collaborator-policy/enums/english-skill.enum';

@Entity()
export class JobApplication {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ nullable: true })
  cvFile: string;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: null,
    nullable: true
  })
  status: ApplicationStatus;

  @Column({
    type: 'enum',
    enum: ProcessStatus,
    default: null,
    nullable: true
  })
  processStatus: ProcessStatus;

  @Column({ type: 'longtext', nullable: true })
  notes: string;

  @Column({ nullable: true })
  refPerson: string;

  @Column()
  jobId: string;

  @Column({
    type: 'enum',
    enum: Level,
    default: null,
    nullable: true
  })
  candidateLevel: Level;

  @Column({
    type: 'enum',
    enum: EnglishSkill,
    default: null,
    nullable: true
  })
  englishSkill: EnglishSkill;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('interview_processes')
export class InterviewProcess {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true, type: 'datetime' })
  scheduleRound1: Date;

  @Column({ nullable: true })
  interviewerRound1: string;

  @Column({ nullable: true, type: 'varchar', length: 2000 })
  contentRound1: string;

  @Column({ nullable: true, type: 'varchar', length: 2000 })
  reviewRound1: string;

  @Column({ nullable: true, default: null })
  resultRound1: boolean;

  @Column({ nullable: true, type: 'datetime' })
  scheduleRound2: Date;

  @Column({ nullable: true })
  interviewerRound2: string;

  @Column({ nullable: true, type: 'varchar', length: 2000 })
  contentRound2: string;

  @Column({ nullable: true, type: 'varchar', length: 2000 })
  reviewRound2: string;

  @Column({ nullable: true, default: null })
  resultRound2: boolean;

  @Column({ nullable: true })
  jobApplicationId: number;

  // Audit fields
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
} 
import { <PERSON><PERSON>ty, Column, PrimaryGeneratedC<PERSON>umn, OneToOne } from 'typeorm';
import { JobStatus } from '../enums/job-status.enum';
import { JobStatistics } from './job-statistics.entity';

@Entity('jobs')
export class Job {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column()
  slug: string;

  @Column('text')
  description: string;

  @Column()
  category: string;

  @Column()
  type: string;

  @Column()
  location: string;

  @Column()
  fullAddress: string;

  @Column()
  workingHours: string;

  @Column()
  salary: string;

  @Column()
  timeAlive: number;  // Changed from postedDays

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.OPEN
  })
  status: JobStatus;

  @Column()
  featured: boolean;

  @Column()
  code: string;

  @Column('text', { transformer: { 
    to: (value: string[]) => value.join(';'),
    from: (value: string) => value.split(';')
  }})
  responsibilities: string[];

  @Column('text', { transformer: { 
    to: (value: string[]) => value.join(';'),
    from: (value: string) => value.split(';')
  }})
  technicalSkills: string[];

  @Column('text', { transformer: { 
    to: (value: string[]) => value.join(';'),
    from: (value: string) => value.split(';')
  }})
  softSkills: string[];

  @Column('text', { transformer: { 
    to: (value: string[]) => value.join(';'),
    from: (value: string) => value.split(';')
  }})
  benefits: string[];

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @OneToOne(() => JobStatistics, (statistics) => statistics.job, {
    cascade: true
  })
  statistics: JobStatistics;
}
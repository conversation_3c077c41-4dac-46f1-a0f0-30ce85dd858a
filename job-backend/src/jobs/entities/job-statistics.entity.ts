import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToOne, JoinColumn } from 'typeorm';
import { Job } from './job.entity';

@Entity('job_statistics')
export class JobStatistics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int', default: 0 })
  totalViews: number;

  @OneToOne(() => Job, (job) => job.statistics)
  @JoinColumn()
  job: Job;
}

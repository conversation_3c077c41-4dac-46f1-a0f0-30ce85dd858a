import { Injectable, Scope } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';

export interface CurrentUser {
  email: string;
  role?: string;
  id?: number;
}
export interface RequestContext {
  requestId: string;
  user?: CurrentUser;
}

@Injectable()
export class ContextService {
  private readonly asyncLocalStorage = new AsyncLocalStorage<RequestContext>();

  run(context: RequestContext, callback: () => void) {
    this.asyncLocalStorage.run(context, callback);
  }

  get context(): RequestContext | undefined {
    return this.asyncLocalStorage.getStore();
  }

  get user(): RequestContext['user'] | undefined {
    return this.context?.user;
  }

  get requestId(): string | undefined {
    return this.context?.requestId;
  }

  setContext(partialContext: Partial<RequestContext>) {
    const store = this.asyncLocalStorage.getStore();
    if (store) {
      Object.assign(store, partialContext);
    }
  }
}
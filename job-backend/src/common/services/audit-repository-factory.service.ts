

import { Injectable, Type } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ContextService } from './context.service';
import { BaseAuditRepository } from '../repositories/base-audit.repository';
import { BaseAuditEntity } from '../entities/base.entity';

@Injectable()
export class AuditRepositoryFactory {
  constructor(
    private readonly dataSource: DataSource,
    private readonly contextService: ContextService
  ) {}

  createRepository<T extends BaseAuditEntity>(
    entityClass: Type<T>
  ): BaseAuditRepository<T> {
    const dataSource = this.dataSource;
    const contextService = this.contextService;

    class ConcreteAuditRepository extends BaseAuditRepository<T> {
      constructor() {
        super(entityClass, dataSource, contextService);
      }
    }
    
    return new ConcreteAuditRepository();
  }
}
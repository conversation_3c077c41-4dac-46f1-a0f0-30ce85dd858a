import { Repository, DataSource, DeepPartial, SaveOptions, FindOptionsWhere, SelectQueryBuilder } from 'typeorm';
import { BaseAuditEntity, BaseSoftDeleteEntity } from '../entities/base.entity';
import { ContextService } from '../services/context.service';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

export abstract class BaseAuditRepository<T extends BaseAuditEntity> extends Repository<T> {
  constructor(
    target: any,
    dataSource: DataSource,
    private readonly contextService: ContextService
  ) {
    super(target, dataSource.createEntityManager(), dataSource.createQueryRunner());
  }

  // Override ALL save methods
  async save<Entity extends DeepPartial<T>>(
    entities: Entity[],
    options?: SaveOptions
  ): Promise<T[]>;
  async save<Entity extends DeepPartial<T>>(
    entity: Entity,
    options?: SaveOptions
  ): Promise<T>;
  async save<Entity extends DeepPartial<T>>(
    entityOrEntities: Entity | Entity[],
    options?: SaveOptions
  ): Promise<T | T[]> {
    if (Array.isArray(entityOrEntities)) {
      entityOrEntities.forEach(entity => this.applyAuditFields(entity as any));
      return super.save(entityOrEntities as any, options);
    } else {
      this.applyAuditFields(entityOrEntities as any);
      return super.save(entityOrEntities as any, options);
    }
  }

  // Override insert method
  async insert(entity: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[]): Promise<any> {
    if (Array.isArray(entity)) {
      entity.forEach(e => this.applyAuditFieldsForInsert(e as any));
    } else {
      this.applyAuditFieldsForInsert(entity as any);
    }
    return super.insert(entity);
  }

  // Override update method
  async update(
    criteria: string | string[] | number | number[] | Date | Date[] | FindOptionsWhere<T>,
    partialEntity: QueryDeepPartialEntity<T>
  ): Promise<any> {
    this.applyAuditFieldsForUpdate(partialEntity as any);
    return super.update(criteria, partialEntity);
  }

  // Override upsert method
  async upsert(
    entityOrEntities: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
    conflictPathsOrOptions: string[] | any
  ): Promise<any> {
    if (Array.isArray(entityOrEntities)) {
      entityOrEntities.forEach(entity => this.applyAuditFields(entity as any));
    } else {
      this.applyAuditFields(entityOrEntities as any);
    }
    return super.upsert(entityOrEntities, conflictPathsOrOptions);
  }

  // Override create method to ensure audit fields are ready
  create(): T;
  create(entityLikeArray: DeepPartial<T>[]): T[];
  create(entityLike: DeepPartial<T>): T;
  create(plainEntityLikeOrPlainEntityLikes?: DeepPartial<T> | DeepPartial<T>[]): T | T[] {
    const result = super.create(plainEntityLikeOrPlainEntityLikes as any);
    
    if (Array.isArray(result)) {
      result.forEach(entity => this.prepareNewEntity(entity));
    } else if (result) {
      this.prepareNewEntity(result);
    }
    
    return result;
  }

  // Override remove methods
  async remove(entities: T[], options?: any): Promise<T[]>;
  async remove(entity: T, options?: any): Promise<T>;
  async remove(entityOrEntities: T | T[], options?: any): Promise<T | T[]> {
    if (Array.isArray(entityOrEntities)) {
      const processedEntities = await Promise.all(
        entityOrEntities.map(entity => this.handleRemove(entity))
      );
      return processedEntities;
    } else {
      return this.handleRemove(entityOrEntities);
    }
  }

  // Override delete method for soft delete entities
  async delete(criteria: any): Promise<any> {
    const entities = await this.find({ where: criteria });
    
    if (entities.length > 0 && this.isSoftDeleteEntity(entities[0])) {
      const softDeleteData = this.createSoftDeleteData();
      return this.update(criteria, softDeleteData as any);
    }
    
    return super.delete(criteria);
  }

  async hardDelete(criteria: any): Promise<any> {
    return super.delete(criteria);
  }

  // Override find methods to exclude soft deleted records by default
  createQueryBuilder(alias?: string): SelectQueryBuilder<T> {
    const qb = super.createQueryBuilder(alias);
    if (this.hasSoftDeleteSupport()) {
      qb.andWhere(`${alias || qb.alias}.isDeleted = false`);
    }
    return qb;
  }

  async find(options?: any): Promise<T[]> {
    if (this.hasSoftDeleteSupport()) {
      options = this.addSoftDeleteFilter(options);
    }
    return super.find(options);
  }

  async findBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<T[]> {
    if (this.hasSoftDeleteSupport()) {
      where = this.addSoftDeleteFilterToWhere(where);
    }
    return super.findBy(where);
  }

  async findOne(options: any): Promise<T | null> {
    if (this.hasSoftDeleteSupport()) {
      options = this.addSoftDeleteFilter(options);
    }
    return super.findOne(options);
  }

  async findOneBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<T | null> {
    if (this.hasSoftDeleteSupport()) {
      where = this.addSoftDeleteFilterToWhere(where);
    }
    return super.findOneBy(where);
  }

  async findOneOrFail(options: any): Promise<T> {
    if (this.hasSoftDeleteSupport()) {
      options = this.addSoftDeleteFilter(options);
    }
    return super.findOneOrFail(options);
  }

  async findOneByOrFail(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<T> {
    if (this.hasSoftDeleteSupport()) {
      where = this.addSoftDeleteFilterToWhere(where);
    }
    return super.findOneByOrFail(where);
  }

  async count(options?: any): Promise<number> {
    if (this.hasSoftDeleteSupport()) {
      options = this.addSoftDeleteFilter(options);
    }
    return super.count(options);
  }

  async countBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<number> {
    if (this.hasSoftDeleteSupport()) {
      where = this.addSoftDeleteFilterToWhere(where);
    }
    return super.countBy(where);
  }

  async exist(options?: any): Promise<boolean> {
    if (this.hasSoftDeleteSupport()) {
      options = this.addSoftDeleteFilter(options);
    }
    return super.exist(options);
  }

  async existsBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<boolean> {
    if (this.hasSoftDeleteSupport()) {
      where = this.addSoftDeleteFilterToWhere(where);
    }
    return super.existsBy(where);
  }

  // Private helper methods
  private async handleRemove(entity: T): Promise<T> {
    if (this.isSoftDeleteEntity(entity)) {
      this.applySoftDeleteFields(entity);
      return super.save(entity as any);
    }
    return super.remove(entity);
  }

  private prepareNewEntity(entity: T): void {
    const currentUser = this.contextService?.user;
    const now = new Date();
    
    if (!entity.createdAt) {
      entity.createdAt = now;
      entity.createdBy = currentUser?.email;
    }
    if (!entity.updatedAt) {
      entity.updatedAt = now;
      entity.updatedBy = currentUser?.email;
    }
  }

  private applyAuditFields(entity: Partial<T>): void {
    if (!entity.id) {
      entity.createdAt = new Date();
      entity.createdBy = this.contextService?.user?.email;
    }

    entity.updatedAt = new Date();
    entity.updatedBy = this.contextService?.user?.email;
  }

  private applyAuditFieldsForInsert(entity: Partial<T>): void {
    entity.createdAt = new Date();
    entity.createdBy = this.contextService?.user?.email;
  }

  private applyAuditFieldsForUpdate(entity: Partial<T>): void {
    entity.updatedAt = new Date();
    entity.updatedBy = this.contextService?.user?.email;
  }

  private applySoftDeleteFields(entity: T & BaseSoftDeleteEntity): void {
    entity.isDeleted = true;
    entity.deletedAt = new Date();
    entity.updatedAt = new Date();
    entity.updatedBy = this.contextService?.user?.email;
  }

  private createSoftDeleteData(): Partial<BaseSoftDeleteEntity> {
    return {
      deletedAt: new Date(),
      isDeleted: true,
      updatedAt: new Date(),
      updatedBy: this.contextService?.user?.email,
    };
  }

  private isSoftDeleteEntity(entity: any): entity is BaseSoftDeleteEntity {
    return entity && 'deletedAt' in entity && 'isDeleted' in entity;
  }

  private hasSoftDeleteSupport(): boolean {
    const sampleEntity = this.create();
    return this.isSoftDeleteEntity(sampleEntity);
  }

  private addSoftDeleteFilter(options: any = {}): any {
    if (!options.where) {
      options.where = {};
    }
    
    if (Array.isArray(options.where)) {
      options.where = options.where.map(whereClause => ({
        ...whereClause,
        isDeleted: false
      }));
    } else {
      options.where = {
        ...options.where,
        isDeleted: false
      };
    }
    
    return options;
  }

  private addSoftDeleteFilterToWhere(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): FindOptionsWhere<T> | FindOptionsWhere<T>[] {
    if (Array.isArray(where)) {
      return where.map(whereClause => ({
        ...whereClause,
        isDeleted: false
      } as FindOptionsWhere<T>));
    } else {
      return {
        ...where,
        isDeleted: false
      } as FindOptionsWhere<T>;
    }
  }
}

import {
  PrimaryGeneratedColumn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column
} from 'typeorm';

export abstract class BaseIdEntity {
  @PrimaryGeneratedColumn()
  id: number;
}

export abstract class BaseAuditEntity extends BaseIdEntity {
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  updatedBy?: string;

  updateAuditFields(currentUserEmail?: string): void {
    this.updatedAt = new Date();
    this.updatedBy = currentUserEmail;
  }

  createAuditFields(currentUserEmail?: string): void {
    this.createdAt = new Date();
    this.createdBy = currentUserEmail;
    this.updateAuditFields(currentUserEmail);
  }
}

export abstract class BaseSoftDeleteEntity extends BaseAuditEntity {
  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean = false;

  softDelete(currentUserEmail?: string): void {
    this.deletedAt = new Date();
    this.isDeleted = true;
    this.updateAuditFields(currentUserEmail);
  }
}


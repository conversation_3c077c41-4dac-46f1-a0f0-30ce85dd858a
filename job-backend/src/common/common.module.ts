import { Module, Global } from '@nestjs/common';
import { ContextService } from './services/context.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditRepositoryFactory } from './services/audit-repository-factory.service';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([])],
  providers: [
    ContextService,
    AuditRepositoryFactory,
  ],
  exports: [
    ContextService,
    AuditRepositoryFactory,
  ],
})
export class CommonModule {}

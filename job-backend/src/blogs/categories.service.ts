import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from './entities/category.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryTree } from './types/category.types';
import { unaccent } from '../utils/string.utils';

const MAX_DEPTH = 2;

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  private generateSlug(name: string): string {
    return unaccent(name);
  }

  private async validateCategoryDepth(parentId: string | null): Promise<void> {
    if (!parentId) {
      return; // Root category is allowed
    }

    const parent = await this.categoryRepository.findOne({
      where: { id: parentId },
      relations: ['parent'],
    });

    if (!parent) {
      throw new NotFoundException(
        `Parent category with ID "${parentId}" not found`,
      );
    }

    // If parent already has a parent, it means we're trying to create a level 3 category
    if (parent.parent) {
      throw new BadRequestException(
        `Cannot create or move category here. Maximum depth of ${MAX_DEPTH} levels exceeded. ` +
          'Categories can only be root categories or direct children of root categories.',
      );
    }
  }

  async create(createCategoryDto: CreateCategoryDto) {
    // Validate depth before creating
    await this.validateCategoryDepth(createCategoryDto.parentId || null);

    const category = this.categoryRepository.create(createCategoryDto);
    
    // Auto-generate slug if not provided
    if (!category.slug) {
      category.slug = this.generateSlug(category.name);
    }

    if (createCategoryDto.parentId) {
      const parent = await this.categoryRepository.findOne({
        where: { id: createCategoryDto.parentId },
      });
      if (!parent) {
        throw new NotFoundException(
          `Parent category with ID "${createCategoryDto.parentId}" not found`,
        );
      }
      category.parent = parent;
    }

    return this.categoryRepository.save(category);
  }

  findAll() {
    return this.categoryRepository.find({
      order: {
        name: 'ASC',
      }
    });
  }

  findAllActive() {
    return this.categoryRepository.find({
      where: {
        isActive: true,
      },
      order: {
        name: 'ASC',
      }
    });
  }

  private async buildCategoryTree(categoryId: string): Promise<CategoryTree> {
    // Get all categories to build the tree
    const allCategories = await this.categoryRepository.find({
      relations: ['parent', 'children'],
    });

    const category = allCategories.find((c) => c.id === categoryId);
    if (!category) {
      throw new NotFoundException(`Category with ID "${categoryId}" not found`);
    }

    const buildTree = (cat: Category): CategoryTree => {
      const node: CategoryTree = {
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        description: cat.description,
        isActive: cat.isActive,
        parentId: cat.parent?.id,
        children: [],
      };

      const children = allCategories.filter((c) => c.parent?.id === cat.id);
      if (children.length > 0) {
        node.children = children.map((child) => buildTree(child));
      }

      return node;
    };

    return buildTree(category);
  }

  async findOne(id: string): Promise<CategoryTree> {
    // First check if category exists with all relations
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'blogs'],
    });

    if (!category) {
      throw new NotFoundException(`Category with ID "${id}" not found`);
    }

    // Build and return the category tree
    return this.buildCategoryTree(id);
  }

  private async checkCircularReference(
    categoryId: string,
    newParentId: string,
  ): Promise<void> {
    // Get all categories with their parent relationships in one query
    const categories = await this.categoryRepository.find({
      select: ['id'],
      relations: ['parent'],
    });

    // Create a map for faster lookups
    const categoryMap = new Map(categories.map((cat) => [cat.id, cat]));

    // Check for circular reference
    let currentId: string | null = newParentId;
    const visitedIds = new Set<string>();

    while (currentId) {
      // If we've seen this ID before, we have a circular reference
      if (visitedIds.has(currentId)) {
        throw new BadRequestException(
          'Circular reference detected in category hierarchy',
        );
      }

      // If we reach the category we're updating, we have a circular reference
      if (currentId === categoryId) {
        throw new BadRequestException(
          'Circular reference detected in category hierarchy',
        );
      }

      visitedIds.add(currentId);
      const currentCategory = categoryMap.get(currentId);
      currentId = currentCategory?.parent?.id || null;
    }
  }

  async update(id: string, updateCategoryDto: UpdateCategoryDto) {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'blogs'],
    });

    if (!category) {
      throw new NotFoundException(`Category with ID "${id}" not found`);
    }

    // Auto-generate slug if name is being updated and slug is not provided
    if (updateCategoryDto.name && !updateCategoryDto.slug) {
      updateCategoryDto.slug = this.generateSlug(updateCategoryDto.name);
    }

    if (updateCategoryDto.parentId !== undefined) {
      // Validate depth before updating
      await this.validateCategoryDepth(updateCategoryDto.parentId);

      // Prevent setting parent to self
      if (updateCategoryDto.parentId === id) {
        throw new BadRequestException('Category cannot be its own parent');
      }

      if (updateCategoryDto.parentId) {
        // Check if new parent exists
        const parent = await this.categoryRepository.findOne({
          where: { id: updateCategoryDto.parentId },
        });
        if (!parent) {
          throw new NotFoundException(
            `Parent category with ID "${updateCategoryDto.parentId}" not found`,
          );
        }

        // Check for circular references
        await this.checkCircularReference(id, updateCategoryDto.parentId);

        category.parent = parent;
      } else {
        // Remove parent if explicitly set to null
        category.parent = null as unknown as Category;
      }
    }

    // Check if this category has children and is being moved to level 2
    if (category.children?.length > 0 && updateCategoryDto.parentId) {
      throw new BadRequestException(
        'Cannot move a category with children to level 2. Only root categories can have children.',
      );
    }

    const updatedCategory = Object.assign(category, updateCategoryDto);
    const savedCategory = await this.categoryRepository.save(updatedCategory);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { children, ...result } = savedCategory;
    return result;
  }

  async remove(id: string) {
    const category = await this.categoryRepository.findOne({
      where: { id },
      relations: ['children', 'blogs'],
    });

    if (!category) {
      throw new NotFoundException(`Category with ID "${id}" not found`);
    }

    // Check if category has children
    if (category.children?.length > 0) {
      throw new BadRequestException(
        'Cannot delete category with child categories. Please remove or reassign child categories first.',
      );
    }

    // Check if category has associated blogs
    if (category.blogs?.length > 0) {
      throw new BadRequestException(
        'Cannot delete category with associated blogs. Please remove or reassign the blogs first.',
      );
    }

    return this.categoryRepository.remove(category);
  }

  async getTree(): Promise<CategoryTree[]> {
    // Get all categories
    const categories = await this.categoryRepository.find({
      relations: ['parent', 'children'],
    });

    // Filter to get only root categories (those without parents)
    const rootCategories = categories.filter((category) => !category.parent);

    // Recursive function to build tree
    const buildTree = (category: Category): CategoryTree => {
      const node: CategoryTree = {
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        isActive: category.isActive,
        parentId: category.parent?.id,
        children: [],
      };

      const children = categories.filter((c) => c.parent?.id === category.id);
      if (children.length > 0) {
        node.children = children.map((child) => buildTree(child));
      }

      return node;
    };

    // Build tree starting from root categories
    return rootCategories.map((root) => buildTree(root));
  }
} 
import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Category } from './category.entity';

@Entity('blogs')
export class Blog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ length: 500, nullable: true })
  slug?: string;

  @Column({ type: 'longtext' })
  content: string;

  @Column({ nullable: true })
  imageUrl?: string;

  @Column({ default: false })
  published: boolean;

  @ManyToOne(() => Category, category => category.blogs, { nullable: true })
  category: Category;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 
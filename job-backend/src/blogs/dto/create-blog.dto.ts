import {
  IsBoolean,
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateBlogDto {
  @IsString()
  @MinLength(3)
  title: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsString()
  @MinLength(10)
  content: string;

  @IsString()
  @IsOptional()
  imageUrl?: string;

  @IsBoolean()
  @IsOptional()
  published?: boolean;

  @IsUUID()
  @IsOptional()
  categoryId?: string;
} 
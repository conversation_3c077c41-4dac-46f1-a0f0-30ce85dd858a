import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BlogsService } from './blogs.service';
import { BlogsController } from './blogs.controller';
import { CategoriesService } from './categories.service';
import { CategoriesController } from './categories.controller';
import { Blog } from './entities/blog.entity';
import { Category } from './entities/category.entity';
import { PublicBlogsController } from '../public-controller/public-blogs.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Blog, Category])],
  controllers: [BlogsController, CategoriesController, PublicBlogsController],
  providers: [BlogsService, CategoriesService],
  exports: [BlogsService, CategoriesService],
})
export class BlogsModule {} 
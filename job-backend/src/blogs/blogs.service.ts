import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { Blog } from './entities/blog.entity';
import { Category } from './entities/category.entity';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { unaccent } from '../utils/string.utils';

@Injectable()
export class BlogsService {
  constructor(
    @InjectRepository(Blog)
    private readonly blogRepository: Repository<Blog>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  private generateSlug(title: string): string {
    return unaccent(title);
  }

  async create(createBlogDto: CreateBlogDto) {
    const blog = this.blogRepository.create(createBlogDto);
    
    // Auto-generate slug if not provided
    blog.slug = this.generateSlug(blog.title);
    
    // Check for duplicate slug
    const existingBlog = await this.blogRepository.findOne({
      where: { slug: blog.slug },
    });
    
    if (existingBlog) {
      // Add timestamp to make slug unique instead of throwing error
      blog.slug = `${blog.slug}-${Date.now().toString().slice(-6)}`;
    }
    
    if (createBlogDto.categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: createBlogDto.categoryId },
      });
      if (!category) {
        throw new NotFoundException(
          `Category with ID "${createBlogDto.categoryId}" not found`,
        );
      }
      blog.category = category;
    }
    
    return this.blogRepository.save(blog);
  }

  findAll() {
    return this.blogRepository.find({
      select: {
        id: true,
        title: true,
        imageUrl: true,
        slug: true,
        published: true,
        createdAt: true,
        category: {
          id: true,
          name: true,
        },
      },
      order: {
        createdAt: 'DESC',
      },
      relations: ['category'],
    });
  }

  async findOne(id: string) {
    // Check if the id is a valid UUID
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    
    const blog = await this.blogRepository.findOne({
      where: isUuid ? { id } : { slug: id },
      relations: ['category'],
    });
    
    if (!blog) {
      throw new NotFoundException(
        `Blog with ${isUuid ? 'ID' : 'slug'} "${id}" not found`
      );
    }
    
    return blog;
  }

  async findBySlug(slug: string) {
    const blog = await this.blogRepository.findOne({
      where: { slug },
      relations: ['category'],
    });
    if (!blog) {
      throw new NotFoundException(`Blog with slug "${slug}" not found`);
    }
    return blog;
  }

  async update(id: string, updateBlogDto: UpdateBlogDto) {
    const blog = await this.findOne(id);
    
    // Generate new slug if title is being updated
    if (updateBlogDto.title) {
      updateBlogDto.slug = this.generateSlug(updateBlogDto.title);
      
      // Check for duplicate slug
      const existingBlog = await this.blogRepository.findOne({
        where: { 
          slug: updateBlogDto.slug,
          id: Not(id) // Exclude current blog
        },
      });
      
      if (existingBlog) {
        // Add timestamp to make slug unique instead of throwing error
        updateBlogDto.slug = `${updateBlogDto.slug}-${Date.now().toString().slice(-6)}`;
      }
    }
    
    if (updateBlogDto.categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: updateBlogDto.categoryId },
      });
      if (!category) {
        throw new NotFoundException(
          `Category with ID "${updateBlogDto.categoryId}" not found`,
        );
      }
      blog.category = category;
      delete updateBlogDto.categoryId;
    }
    
    Object.assign(blog, updateBlogDto);
    return this.blogRepository.save(blog);
  }

  async remove(id: string) {
    const blog = await this.findOne(id);
    return this.blogRepository.remove(blog);
  }

  async findByCategorySlug(categorySlug: string) {
    console.log(categorySlug);
    if (categorySlug === 'all') {
      return this.blogRepository.find({
        where: {
          published: true,
        },
        order: {
          createdAt: 'DESC',
        },
        select: {
          id: true,
          title: true,
          slug: true,
          imageUrl: true,
          createdAt: true,
          published: true,
          category: {
            id: true,
            name: true,
            slug: true
          }
        },
        relations: ['category'],
      });
    }

    return this.blogRepository.find({
      where: {
        category: {
          slug: categorySlug,
        },
        published: true, // Only return published blogs
      },
      order: {
        createdAt: 'DESC',
      },
      select: {
        id: true,
        title: true,
        slug: true,
        imageUrl: true,
        createdAt: true,
        published: true,
        category: {
          id: true,
          name: true,
          slug: true
        }
      },
      relations: ['category'],
    });
  }
} 
{"name": "job-backend", "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", ".git", "**/*.spec.ts", "**/*.test.ts"], "language": {"typescript": {"formatter": "prettier", "importOrder": ["^@nestjs/(.*)$", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}}, "search": {"excludePatterns": ["**/node_modules/**", "**/dist/**", "**/coverage/**", "**/.git/**"]}, "linting": {"eslint": {"enable": true, "autofix": true}}, "formatting": {"prettier": {"enable": true, "autoformat": true, "config": {"singleQuote": true, "trailingComma": "all", "printWidth": 100, "semi": true, "tabWidth": 2}}}, "completion": {"typescript": {"enableAutomaticTypeAcquisition": true, "suggestionTimeout": 5000}}, "navigation": {"excludePatterns": ["**/node_modules/**", "**/dist/**", "**/coverage/**", "**/.git/**"], "includePatterns": ["src/**/*", "test/**/*"]}}
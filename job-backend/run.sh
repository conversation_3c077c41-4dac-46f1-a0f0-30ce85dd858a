#!/bin/bash

# Exit on error
set -e

echo "🚀 Starting production server..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Error: Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker compose &> /dev/null; then
    echo "❌ Error: Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "❌ Error: .env.production file not found."
    exit 1
fi

# Create uploads directory if it doesn't exist
if [ ! -d "uploads" ]; then
    echo "📁 Creating uploads directory..."
    mkdir -p uploads
fi

# Stop and remove existing containers
echo "🛑 Stopping and removing existing containers..."
docker compose down --remove-orphans

# Build and start containers
echo "🏗️ Building and starting containers..."
docker compose up -d --build

echo "✅ Production server started successfully!"
echo "📊 Check status with: docker compose ps"
echo "📝 View logs with: docker compose logs -f"

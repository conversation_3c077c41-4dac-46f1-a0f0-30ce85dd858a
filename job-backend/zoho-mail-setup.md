# Zoho Mail Configuration for Job Portal Backend

## Environment Variables

To configure the application to use Zoho Mail for sending emails, you need to set the following environment variables in your `.env` file (for development) and `.env.production` file (for production):

```
# Zoho Mail Configuration
SMTP_HOST=smtp.zoho.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_zoho_app_password
SMTP_FROM=<EMAIL>
```

## Setting Up an App Password in Zoho Mail

For security reasons, it's recommended to use an app-specific password rather than your main Zoho account password:

1. Log in to your Zoho Mail account
2. Go to Account Settings (gear icon) > Accounts > Security
3. Scroll down to "App Passwords" section
4. Click "Generate New Password"
5. Enter a name for the application (e.g., "Job Portal")
6. Copy the generated password and use it as the `SMTP_PASSWORD` value

## Testing the Configuration

Once you've set up the environment variables, you can test the email configuration using the `sendTestEmail` method in the `EmailService`:

```typescript
// Import the EmailService in your controller or service
import { EmailService } from '../email/email.service';

// Inject the EmailService
constructor(private readonly emailService: EmailService) {}

// Test sending an email
await this.emailService.sendTestEmail('<EMAIL>');
```

## Troubleshooting

If you encounter issues with email delivery:

1. Check that all environment variables are correctly set
2. Verify that the Zoho Mail account has SMTP access enabled
3. If using a free Zoho Mail plan, ensure you're sending from the same domain as your account
4. Check application logs for specific error messages

## Security Recommendations

1. Never commit your `.env` or `.env.production` files to version control
2. Use app-specific passwords instead of your main account password
3. Consider encrypting sensitive environment variables in production 
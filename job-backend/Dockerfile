# Use Node.js 20 as the base image
FROM node:20-alpine AS builder

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./

# First install all dependencies including dev dependencies for building
RUN npm ci --frozen-lockfile

# Bundle app source
COPY . .

# Build the application
RUN npm run build

FROM node:20-alpine AS production

WORKDIR /usr/src/app

# Using non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001 -G nodejs

COPY package*.json ./

RUN npm ci --omit=dev --frozen-lockfile && \
    npm cache clean --force

COPY --from=builder --chown=nestjs:nodejs /usr/src/app/dist ./dist

USER nestjs

# Expose the port the app runs on
EXPOSE 3002

# Start the application
CMD ["npm", "run", "start:prod"] 
{"name": "tekai-job-cms", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.17.1", "@mui/x-date-pickers": "^8.0.0", "@mui/x-tree-view": "^8.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "qs": "^6.14.0", "quill-image-resize-module-react": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.10.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "set PORT=3001 && react-scripts start --watch", "build": "react-scripts build", "build:dev": "REACT_APP_ENV=development react-scripts build", "build:prod": "REACT_APP_ENV=production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
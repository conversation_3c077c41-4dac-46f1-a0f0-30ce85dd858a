{"project": {"type": "react", "rules": {"fileExtensions": {"components": [".js"], "styles": [".css", ".module.css"], "pages": [".js"], "hooks": [".js"], "utils": [".js"], "constants": [".js"], "types": [".js"]}, "structure": {"src": {"components": {"description": "React components with their associated CSS modules", "rules": {"naming": "^[A-Z][a-zA-Z0-9]*$", "required": ["jsx/js file", "css module file"], "pattern": {"component": "components/{ComponentName}/{ComponentName}.jsx", "style": "components/{ComponentName}/{ComponentName}.module.css"}}}, "pages": {"description": "Next.js/React Router pages", "rules": {"naming": "^[a-z][a-zA-Z0-9-]*$", "pattern": "pages/{pageName}.js"}}, "hooks": {"description": "Custom React hooks", "rules": {"naming": "^use[A-Z][a-zA-Z0-9]*$", "pattern": "hooks/use{HookName}.js"}}, "utils": {"description": "Utility functions and helpers", "rules": {"naming": "^[a-z][a-zA-Z0-9]*$", "pattern": "utils/{utilityName}.js"}}, "constants": {"description": "Application constants and configurations", "rules": {"naming": "^[A-Z][A-Z0-9_]*$", "pattern": "constants/{constantName}.js"}}, "types": {"description": "Type definitions and interfaces", "rules": {"naming": "^[A-Z][a-zA-Z0-9]*$", "pattern": "types/{typeName}.js"}}, "assets": {"description": "Static assets like images, fonts, etc.", "allowedExtensions": [".svg", ".png", ".jpg", ".jpeg", ".gif", ".woff", ".woff2", ".ttf", ".eot"]}}}, "imports": {"order": ["react", "external", "internal", "components", "hooks", "utils", "constants", "types", "styles"], "groups": {"react": ["react", "react-dom"], "external": ["^@?\\w"], "internal": ["^@/.*"], "components": ["^@/components/.*"], "hooks": ["^@/hooks/.*"], "utils": ["^@/utils/.*"], "constants": ["^@/constants/.*"], "types": ["^@/types/.*"], "styles": ["\\.css$", "\\.module\\.css$"]}}}, "recommendations": {"componentStructure": {"example": {"components/Button/": {"Button.js": "Main component file", "Button.module.css": "Component-specific styles", "Button.test.js": "Optional: Component tests", "index.js": "Optional: Barrel export file"}}}, "bestPractices": ["Use PascalCase for component names", "Use camelCase for hook names starting with 'use'", "Keep components small and focused", "Use CSS modules for component-specific styles", "Place shared styles in a global styles directory", "Use meaningful directory names", "Group related components in subdirectories"]}}}
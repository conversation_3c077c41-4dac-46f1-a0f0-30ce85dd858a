---
description: 
globs: 
alwaysApply: false
---
All React components must be functional components using arrow functions.
Components should be named using PascalCase.
Props must be typed explicitly with TypeScript interfaces or types.
No `any` type allowed.
Prefer built-in React hooks (useState, useEffect, useMemo, etc).
Custom hooks must start with `use` and be placed inside a `hooks/` directory.
Avoid using useEffect for business logic, only for side effects.
Use TailwindCSS classes for all styling.
Do not write any custom CSS unless necessary.
Classes should be ordered logically: layout → spacing → typography → color.
All API calls must be handled inside a `/services` or `/api` directory.
Use Axios with custom instances (baseURL, interceptors).
Handle errors using try-catch blocks and show user-friendly messages.
Organize components in feature-based folders.
Each feature should have its own directory with `pages`, `components/`, `hooks/`, `services/`, and `types/`.
Avoid having a flat components/ folder for the entire project.
Follow Prettier formatting automatically.
Prefer destructuring props and state.
Avoid inline functions inside JSX unless necessary.

Use optional chaining and nullish coalescing operator (`??`) where needed.
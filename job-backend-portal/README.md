# Job CMS Frontend

This repository contains the frontend application for the Job CMS portal.

## Development Setup

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd job-cms-fe
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Create a `.env` file in the root directory with the following content:
```env
REACT_APP_API_URL=https://api-dev.tekai.vn
```

### Running the Development Server

To start the development server:

```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:3000`

## Staging Environment

The staging environment is accessible at the following URLs:

- Frontend (CMS): https://cms-dev.tekai.vn
- API: https://api-dev.tekai.vn

### Accessing Staging

1. Make sure you have the necessary credentials to access the staging environment
2. Use your provided login credentials at https://cms-dev.tekai.vn

## Available Scripts

- `npm run dev` - Starts the development server
- `npm run build` - Creates a production build
- `npm run start` - Runs the production server
- `npm run lint` - Runs the linter
- `npm run test` - Runs the test suite

## Project Structure

```
src/
├── components/     # Reusable React components
├── pages/         # Application pages/routes
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── constants/     # Application constants
├── types/         # Type definitions
└── assets/        # Static assets
```

## Contributing

1. Create a new branch from `develop`
2. Make your changes
3. Submit a pull request

## Support

For any issues or questions, please contact the development team.
version: '3.3'

services:
  web:
    build:
      context: .
      args:
        - REACT_APP_ENV=${REACT_APP_ENV:-production}
    image: tekai-job-cms:${REACT_APP_ENV:-production}
    container_name: tekai-job-cms-${REACT_APP_ENV:-production}
    ports:
      - "3001:80"
    restart: unless-stopped
    networks:
      - job-cms-network
    environment:
      - VIRTUAL_HOST=cms-dev.tekai.vn
      - VIRTUAL_PORT=80

networks:
  job-cms-network:
    driver: bridge 
#!/bin/bash

# Default environment
ENV=${1:-production}

# Function to display usage
usage() {
    echo "Usage: $0 [development|production]"
    echo "  development - Build and run in development mode"
    echo "  production  - Build and run in production mode (default)"
    exit 1
}

# Validate environment
if [[ "$ENV" != "development" && "$ENV" != "production" ]]; then
    usage
fi

# Export environment variable for docker-compose
export REACT_APP_ENV=$ENV

# Stop and remove existing containers
echo "Cleaning up existing containers..."
docker-compose down

# Build and start the containers
echo "Building and starting containers for $ENV environment..."
docker-compose up -d --build

echo "Application is running at http://localhost:3001"
echo "To view logs, run: docker-compose logs -f" 
/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4 {
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.3;
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1rem;
  color: #555;
}

ul {
  list-style-position: inside;
  margin-bottom: 1rem;
  padding-left: 1rem;
}

li {
  margin-bottom: 0.5rem;
  color: #555;
}

a {
  color: #1a73e8;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #0d47a1;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-btn {
  background-color: #1a73e8;
  color: white;
}

.primary-btn:hover {
  background-color: #0d47a1;
}

.light-btn {
  background-color: white;
  color: #1a73e8;
  border: 1px solid #e0e0e0;
}

.light-btn:hover {
  background-color: #f5f5f5;
}

.full-width {
  width: 100%;
}

/* Header */
header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.5rem;
  margin: 0;
}

.main-nav {
  display: none;
}

.main-nav a {
  margin-left: 20px;
  color: #555;
}

.main-nav a:hover {
  color: #1a73e8;
}

/* Job Title Section */
.job-title-section {
  background-color: #1a73e8;
  color: white;
  padding: 40px 0;
}

.job-title-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
}

.job-title-section h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-item i {
  font-size: 0.9rem;
}

/* Main Content */
.main-content {
  padding: 40px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 25px;
  margin-bottom: 30px;
}

.highlight-card {
  background-color: #e8f0fe;
}

/* Details List */
.details-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-item h3 {
  margin-bottom: 5px;
  font-size: 1rem;
  color: #555;
}

.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: #f0f0f0;
  color: #555;
  border: 1px solid #ddd;
}

.badge.active {
  background-color: #1a73e8;
  color: white;
  border-color: #1a73e8;
}

.badge.green {
  background-color: #34a853;
  color: white;
  border-color: #34a853;
}

/* Benefits List */
.benefits-list {
  list-style: none;
  padding: 0;
}

.benefits-list li {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
}

.benefits-list i {
  color: #1a73e8;
  margin-top: 4px;
}

/* Footer */
footer {
  background-color: #333;
  color: white;
  padding: 50px 0 20px;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.footer-column h3 {
  color: white;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.footer-column p {
  color: #bbb;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #bbb;
}

.footer-links a:hover {
  color: white;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transition: background-color 0.3s;
}

.social-icons a:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #999;
  font-size: 0.9rem;
}

/* Responsive styles */
@media (min-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  .main-nav {
    display: flex;
  }

  .job-title-content {
    flex-direction: row;
    align-items: center;
  }

  .content-grid {
    grid-template-columns: 2fr 1fr;
  }

  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

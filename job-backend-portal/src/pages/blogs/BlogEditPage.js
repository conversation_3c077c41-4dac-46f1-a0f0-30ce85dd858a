import React, { useState, useEffect, useCallback } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import ImageResize from 'quill-image-resize-module-react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  CircularProgress,
  MenuItem
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { getBlogById, createBlog, updateBlog } from '../../services/blogService';
import { getAllCategories } from '../../services/categoryService';

// Register the image resize module
ReactQuill.Quill.register('modules/imageResize', ImageResize);

const modules = {
  toolbar: [
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'align': ['', 'center', 'right', 'justify'] }],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'indent': '-1'}, { 'indent': '+1' }],
    [{ 'color': [] }, { 'background': [] }],
    ['link', 'image'],
    ['clean']
  ],
  clipboard: {
    matchVisual: true,
    matchers: [
      ['IMG', (node, delta) => {
        const img = delta.ops.find(op => op.insert && op.insert.image);
        if (img && node.style.float) {
          img.attributes = {
            ...img.attributes,
            class: `ql-align-${node.style.float === 'left' ? 'left' : 'right'}`
          };
        }
        return delta;
      }]
    ]
  },
  imageResize: {
    parchment: ReactQuill.Quill.import('parchment'),
    modules: ['Resize', 'DisplaySize', 'Toolbar'],
    displayStyles: {
      backgroundColor: 'black',
      border: 'none',
      color: 'white'
    },
    toolbarStyles: {
      backgroundColor: 'black',
      border: 'none',
      color: 'white'
    },
    handleStyles: {
      backgroundColor: 'black',
      border: 'none',
      color: 'white'
    }
  }
};

const formats = [
  'header',
  'bold', 'italic', 'underline', 'strike',
  'align',
  'list', 'bullet', 'indent',
  'color', 'background',
  'link', 'image',
  'clean'
];

const BlogEditPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    content: '',
    categoryId: '',
    published: false,
  });
  const isEditing = !!id && id !== 'create';

  const fetchBlogDetails = useCallback(async () => {
    try {
      const data = await getBlogById(id);
      // Parse and sanitize the HTML content while preserving formatting
      let content = data.content || '';
      
      // Ensure proper parsing of alignment classes
      if (content) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');
        
        // Fix alignment classes if needed
        doc.querySelectorAll('p, h1, h2, h3, h4, h5, h6, img').forEach(element => {
          const alignClass = Array.from(element.classList)
            .find(className => className.startsWith('ql-align-'));
          
          if (alignClass) {
            // Ensure the class is properly set
            element.classList.remove(alignClass);
            element.classList.add(alignClass);
          }
        });

        // Fix image sizing and alignment
        doc.querySelectorAll('img').forEach(img => {
          if (img.width) img.setAttribute('width', img.width);
          if (img.height) img.setAttribute('height', img.height);
          // Preserve image alignment
          const float = img.style.float;
          if (float === 'left') img.classList.add('ql-align-left');
          if (float === 'right') img.classList.add('ql-align-right');
          if (!float && !img.classList.contains('ql-align-left') && !img.classList.contains('ql-align-right')) {
            img.classList.add('ql-align-center');
          }
        });

        // Convert back to string
        content = doc.body.innerHTML;
      }

      setFormData({
        id: data.id || '',
        title: data.title || '',
        content: content,
        categoryId: data.category?.id || data.categoryId || '',
        published: data.published || false,
      });
    } catch (error) {
      console.error('Error fetching blog details:', error);
    }
  }, [id]);

  // Add DOMParser polyfill for older browsers
  useEffect(() => {
    if (!window.DOMParser) {
      window.DOMParser = function() {};
      window.DOMParser.prototype.parseFromString = function(str) {
        const div = document.createElement('div');
        div.innerHTML = str;
        return { body: div };
      };
    }
  }, []);

  useEffect(() => {
    fetchCategories();
    if (isEditing && id) {
      console.log('Fetching blog details for ID:', id);
      fetchBlogDetails();
    } else {
      console.log('Skipping blog details fetch - create mode');
    }
  }, [isEditing, fetchBlogDetails, id]);

  const fetchCategories = async () => {
    try {
      const data = await getAllCategories();
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'published' ? Boolean(value) : value
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    console.log('Form submission started:', { isEditing, formData });
    setLoading(true);
    try {
      if (isEditing) {
        console.log('Updating existing blog:', formData);
        await updateBlog(formData.id, formData);
        console.log('Blog updated successfully');
      } else {
        // Explicitly handle create case
        const { id, ...createData } = formData;
        console.log('Creating new blog:', createData);
        await createBlog(createData);
        console.log('Blog created successfully');
      }
      navigate('/blogs');
    } catch (error) {
      console.error('Error saving blog:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
    } finally {
      setLoading(false);
      console.log('Form submission completed');
    }
  };

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Paper sx={{ p: 4, maxWidth: '1200px', mx: 'auto' }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 4 }}>
          {isEditing ? 'Edit Blog' : 'Create Blog'}
        </Typography>

        <form onSubmit={handleSubmit}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Box sx={{ display: 'flex', gap: 3 }}>
              <TextField
                name="title"
                label="Title"
                value={formData.title}
                onChange={handleChange}
                required
                fullWidth
                sx={{ flex: 2 }}
              />

              <TextField
                name="categoryId"
                label="Category"
                select
                value={formData.categoryId}
                onChange={handleChange}
                fullWidth
                required
                sx={{ flex: 1 }}
              >
                <MenuItem value="">
                  <em>Select a category</em>
                </MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </TextField>

              <TextField
                name="published"
                label="Status"
                select
                value={formData.published}
                onChange={handleChange}
                fullWidth
                sx={{ flex: 1 }}
              >
                <MenuItem value={false}>Draft</MenuItem>
                <MenuItem value={true}>Published</MenuItem>
              </TextField>
            </Box>

            <Box sx={{ width: '100%' }}>
              <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
                Content
              </Typography>
              <Box sx={{ 
                width: '100%',
                '.ql-container': {
                  height: '500px',
                  fontSize: '1rem',
                  border: '1px solid rgba(0, 0, 0, 0.23)',
                  borderTop: 'none',
                  borderBottomLeftRadius: '4px',
                  borderBottomRightRadius: '4px',
                },
                '.ql-toolbar': {
                  border: '1px solid rgba(0, 0, 0, 0.23)',
                  borderTopLeftRadius: '4px',
                  borderTopRightRadius: '4px',
                  background: '#f8f9fa',
                  padding: '12px 8px',
                  '& button': {
                    height: '32px',
                    width: '32px',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    },
                  },
                  '.ql-picker': {
                    height: '32px',
                  }
                },
                '.ql-editor': {
                  minHeight: '500px',
                  padding: '20px',
                  fontSize: '16px',
                  lineHeight: '1.6',
                  background: '#ffffff',
                  '& img': {
                    maxWidth: '100%',
                    height: 'auto',
                    display: 'block',
                    margin: '1rem auto',
                    '&.ql-align-left': {
                      float: 'left',
                      marginRight: '1rem',
                      marginBottom: '1rem',
                    },
                    '&.ql-align-right': {
                      float: 'right',
                      marginLeft: '1rem',
                      marginBottom: '1rem',
                    },
                    '&.ql-align-center': {
                      margin: '1rem auto',
                    },
                  },
                  '& p, & h1, & h2, & h3, & h4, & h5, & h6': {
                    '&.ql-align-center': {
                      textAlign: 'center',
                    },
                    '&.ql-align-right': {
                      textAlign: 'right',
                    },
                    '&.ql-align-justify': {
                      textAlign: 'justify',
                    },
                  },
                },
                '.ql-editor h1, .ql-editor h2, .ql-editor h3': {
                  marginBottom: '1rem',
                  marginTop: '1.5rem',
                },
                '.ql-editor p': {
                  marginBottom: '1rem',
                },
                '.ql-tooltip': {
                  backgroundColor: 'white',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid rgba(0,0,0,0.1)',
                  '& input': {
                    border: '1px solid rgba(0,0,0,0.23)',
                    borderRadius: '4px',
                    padding: '4px 8px',
                    marginRight: '8px',
                    '&:focus': {
                      outline: 'none',
                      borderColor: '#1976d2',
                    }
                  },
                  '& a.ql-action': {
                    color: '#1976d2',
                    marginRight: '8px',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline',
                    }
                  }
                }
              }}>
                <ReactQuill
                  value={formData.content}
                  onChange={(value) => setFormData(prev => ({ ...prev, content: value }))}
                  modules={modules}
                  formats={formats}
                  placeholder="Write your blog content here..."
                />
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, mt: 4, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/blogs')}
                sx={{ px: 4 }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                sx={{ px: 4 }}
              >
                {loading ? <CircularProgress size={24} /> : (isEditing ? 'Update' : 'Create')}
              </Button>
            </Box>
          </Box>
        </form>
      </Paper>
    </Box>
  );
};

export default BlogEditPage; 
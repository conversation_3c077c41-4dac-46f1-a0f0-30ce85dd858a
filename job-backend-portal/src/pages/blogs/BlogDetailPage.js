import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Typography,
  Paper,
  Grid,
  Divider,
  <PERSON>,
  <PERSON><PERSON>,
  Stack,
  Container
} from '@mui/material';
import { Edit as EditIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { getBlogById } from '../../services/blogService';

const formatDate = (dateString) => {
  if (!dateString) return '-';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return '-';
  }
};

const DetailItem = ({ label, value, children }) => (
  <Grid container spacing={2} sx={{ py: 2 }}>
    <Grid item xs={12} md={3}>
      <Typography 
        variant="subtitle1" 
        color="text.secondary" 
        sx={{ 
          fontWeight: 600,
          textTransform: 'uppercase',
          fontSize: '0.875rem',
          letterSpacing: '0.5px'
        }}
      >
        {label}
      </Typography>
    </Grid>
    <Grid item xs={12} md={9}>
      {children || (
        <Typography 
          variant="body1" 
          sx={{ 
            whiteSpace: 'pre-wrap',
            color: 'text.primary',
            lineHeight: 1.6
          }}
        >
          {value || '-'}
        </Typography>
      )}
    </Grid>
  </Grid>
);

const BlogDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogDetails = async () => {
      try {
        const data = await getBlogById(id);
        setBlog(data);
      } catch (error) {
        console.error('Error fetching blog details:', error);
        setError('Failed to load blog details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogDetails();
  }, [id]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/blogs')}
        >
          Back to Blogs
        </Button>
      </Container>
    );
  }

  if (!blog) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Blog not found
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/blogs')}
        >
          Back to Blogs
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Paper 
        elevation={2} 
        sx={{ 
          p: { xs: 2, md: 4 },
          borderRadius: 2,
          backgroundColor: 'background.paper'
        }}
      >
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2,
            mb: 4 
          }}
        >
          <Typography 
            variant="h4" 
            component="h1"
            sx={{ 
              fontWeight: 700,
              color: 'text.primary',
              fontSize: { xs: '1.5rem', md: '2rem' }
            }}
          >
            {blog.title}
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/blogs')}
              sx={{ 
                px: 3,
                py: 1,
                borderRadius: 2,
                textTransform: 'none',
                fontSize: '1rem'
              }}
            >
              Back to Blogs
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              onClick={() => navigate(`/blogs/${id}`)}
              sx={{ 
                px: 3,
                py: 1,
                borderRadius: 2,
                textTransform: 'none',
                fontSize: '1rem'
              }}
            >
              Edit
            </Button>
          </Box>
        </Box>

        <Stack 
          direction="row" 
          spacing={2} 
          sx={{ 
            mb: 4,
            flexWrap: 'wrap',
            gap: 1
          }}
        >
          {blog.category?.name && (
            <Chip 
              label={blog.category.name} 
              color="primary" 
              variant="outlined"
              sx={{ borderRadius: 2 }}
            />
          )}
          <Chip 
            label={blog.published ? "Published" : "Draft"} 
            color={blog.published ? "success" : "default"} 
            variant="outlined"
            sx={{ borderRadius: 2 }}
          />
        </Stack>

        <Divider sx={{ mb: 4 }} />
        <DetailItem
          label="Category"
          value={blog.category?.name || 'Uncategorized'}
        />

        <DetailItem label="Status">
          <Chip 
            label={blog.published ? "Published" : "Draft"} 
            color={blog.published ? "success" : "default"} 
            size="small"
            sx={{ borderRadius: 2 }}
          />
        </DetailItem>

        <DetailItem
          label="Created At"
          value={formatDate(blog.createdAt)}
        />

        <DetailItem
          label="Updated At"
          value={formatDate(blog.updatedAt)}
        />
        
        <Box 
          className="blog-content"
          sx={{ 
            mt: 4,
            '& img': {
              maxWidth: '100%',
              height: 'auto',
              display: 'block',
              margin: '1rem auto',
              '&.ql-align-left': {
                float: 'left',
                marginRight: '1rem',
                marginBottom: '1rem',
              },
              '&.ql-align-right': {
                float: 'right',
                marginLeft: '1rem',
                marginBottom: '1rem',
              },
              '&.ql-align-center': {
                margin: '1rem auto',
              },
            },
            '& p, & h1, & h2, & h3, & h4, & h5, & h6': {
              '&.ql-align-center': {
                textAlign: 'center',
              },
              '&.ql-align-right': {
                textAlign: 'right',
              },
              '&.ql-align-justify': {
                textAlign: 'justify',
              },
            },
            '& h1': {
              fontSize: '2rem',
              fontWeight: 700,
              marginTop: '2rem',
            },
            '& h2': {
              fontSize: '1.75rem',
              fontWeight: 600,
              marginTop: '1.75rem',
            },
            '& h3': {
              fontSize: '1.5rem',
              fontWeight: 600,
              marginTop: '1.5rem',
            },
            '& h4': {
              fontSize: '1.25rem',
              fontWeight: 600,
              marginTop: '1.25rem',
            },
            '& h5': {
              fontSize: '1.1rem',
              fontWeight: 600,
              marginTop: '1.1rem',
            },
            '& h6': {
              fontSize: '1rem',
              fontWeight: 600,
              marginTop: '1rem',
            },
            '& p': {
              fontSize: '1rem',
              lineHeight: 1.6,
              marginBottom: '1rem',
            },
            '& .ql-indent-1': { paddingLeft: '3em' },
            '& .ql-indent-2': { paddingLeft: '6em' },
            '& .ql-indent-3': { paddingLeft: '9em' },
            '& .ql-indent-4': { paddingLeft: '12em' },
            '& .ql-indent-5': { paddingLeft: '15em' },
            '& .ql-indent-6': { paddingLeft: '18em' },
            '& .ql-indent-7': { paddingLeft: '21em' },
            '& .ql-indent-8': { paddingLeft: '24em' },
            '& ul, & ol': {
              marginBottom: '1rem',
              paddingLeft: '2rem',
              '& li': {
                '&.ql-indent-1': { paddingLeft: '3em' },
                '&.ql-indent-2': { paddingLeft: '6em' },
                '&.ql-indent-3': { paddingLeft: '9em' },
                '&.ql-indent-4': { paddingLeft: '12em' },
                '&.ql-indent-5': { paddingLeft: '15em' },
                '&.ql-indent-6': { paddingLeft: '18em' },
                '&.ql-indent-7': { paddingLeft: '21em' },
                '&.ql-indent-8': { paddingLeft: '24em' },
              }
            },
            '& li': {
              marginBottom: '0.5rem',
              '&.ql-indent-1': { paddingLeft: '3em' },
              '&.ql-indent-2': { paddingLeft: '6em' },
              '&.ql-indent-3': { paddingLeft: '9em' },
              '&.ql-indent-4': { paddingLeft: '12em' },
              '&.ql-indent-5': { paddingLeft: '15em' },
              '&.ql-indent-6': { paddingLeft: '18em' },
              '&.ql-indent-7': { paddingLeft: '21em' },
              '&.ql-indent-8': { paddingLeft: '24em' },
            },
            '& a': {
              color: 'primary.main',
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
              },
            },
            '& blockquote': {
              borderLeft: '4px solid',
              borderColor: 'grey.300',
              paddingLeft: '1rem',
              marginLeft: 0,
              marginRight: 0,
              fontStyle: 'italic',
              color: 'text.secondary',
            },
            '& pre': {
              backgroundColor: 'grey.100',
              padding: '1rem',
              borderRadius: '4px',
              overflowX: 'auto',
              marginBottom: '1rem',
            },
            '& code': {
              fontFamily: 'monospace',
              backgroundColor: 'grey.100',
              padding: '0.2rem 0.4rem',
              borderRadius: '4px',
              fontSize: '0.875rem',
            },
          }}
          dangerouslySetInnerHTML={{ __html: blog.content || '-' }} 
        />
      </Paper>
    </Container>
  );
};

export default BlogDetailPage; 
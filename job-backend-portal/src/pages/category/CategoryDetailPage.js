"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router-dom"
import { ArrowLeft, Edit } from "lucide-react"
import { getCategoryById } from "../../services/categoryService"
import {
  Box,
  Paper,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import TreeView from '@mui/lab/TreeView';
import TreeItem from '@mui/lab/TreeItem';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';

function CategoryDetailPage() {
  const { id } = useParams()
  const navigate = useNavigate();
  const [category, setCategory] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [expandedNodes, setExpandedNodes] = useState([])
  const [parentCategory, setParentCategory] = useState(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const categoryData = await getCategoryById(id);
        setCategory(categoryData);
        
        // If parent is null but parentId exists, fetch the parent category
        if (!categoryData.parent && categoryData.parentId) {
          try {
            const parentData = await getCategoryById(categoryData.parentId);
            setParentCategory(parentData);
          } catch (error) {
            console.error('Failed to fetch parent category:', error);
          }
        }
        
        // Expand the current category to show its children
        setExpandedNodes([id]);
      } catch (error) {
        setError('Failed to load category details');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id])

  const handleEdit = () => {
    navigate(`/categories/${id}`);
  }

  const renderTree = (node) => {
    if (!node) return null;
    return (
      <TreeItem
        key={node.id}
        nodeId={node.id}
        label={
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: 0.5,
            ...(node.id === id && {
              fontWeight: 'bold',
              color: 'primary.main'
            })
          }}>
            {node.name}
            {!node.isActive && (
              <Chip
                label="Inactive"
                size="small"
                sx={{ ml: 1 }}
              />
            )}
          </Box>
        }
        onClick={() => node.id !== id && navigate(`/categories/${node.id}/view`)}
      >
        {Array.isArray(node.children) && node.children.map((child) => renderTree(child))}
      </TreeItem>
    );
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            p: 3,
            flexDirection: 'column',
            gap: 2
          }}>
            <CircularProgress />
            <Typography variant="body1" color="text.secondary">
              Loading category details...
            </Typography>
          </Box>
        </div>
      </div>
    );
  }

  if (error) return <div className="error">{error}</div>
  if (!category) return <div>Category not found</div>

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3, backgroundColor: '#f5f7fa', minHeight: '100vh' }}>
          <Box 
            sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mb: 3,
              backgroundColor: 'white',
              p: 2,
              borderRadius: 1,
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}
          >
            <Box>
              <Button
                component={Link}
                to="/categories"
                startIcon={<ArrowLeft />}
                sx={{ 
                  mb: 1,
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'action.hover',
                  }
                }}
              >
                Back to Categories
              </Button>
              <Typography 
                variant="h5" 
                component="h1"
                sx={{ 
                  fontWeight: 600,
                  color: 'text.primary'
                }}
              >
                {category.name}
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Edit />}
              onClick={handleEdit}
              sx={{
                backgroundColor: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
                boxShadow: 'none'
              }}
            >
              Edit Category
            </Button>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Paper 
                sx={{ 
                  p: 3,
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <Typography 
                      variant="subtitle2" 
                      sx={{ 
                        color: 'text.secondary',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        fontSize: '0.75rem',
                        mb: 1
                      }}
                    >
                      Name
                    </Typography>
                    <Typography 
                      variant="body1"
                      sx={{ 
                        fontSize: '1.1rem',
                        color: 'text.primary',
                        fontWeight: 500
                      }}
                    >
                      {category.name}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography 
                      variant="subtitle2" 
                      sx={{ 
                        color: 'text.secondary',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        fontSize: '0.75rem',
                        mb: 1
                      }}
                    >
                      Slug
                    </Typography>
                    <Typography 
                      variant="body1"
                      sx={{ 
                        fontSize: '1.1rem',
                        color: 'text.primary',
                        fontWeight: 500
                      }}
                    >
                      {category.slug || '-'}
                    </Typography>
                  </Grid>

                  {category.parentId && (
                    <Grid item xs={12}>
                      <Typography 
                        variant="subtitle2"
                        sx={{ 
                          color: 'text.secondary',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px',
                          fontSize: '0.75rem',
                          mb: 1
                        }}
                      >
                        Parent Category
                      </Typography>
                      <Typography 
                        variant="body1"
                        sx={{
                          color: 'text.primary',
                          fontWeight: 500
                        }}
                      >
                        {category.parent?.name || parentCategory?.name || 'Unknown'}
                      </Typography>
                    </Grid>
                  )}

                  <Grid item xs={12}>
                    <Typography 
                      variant="subtitle2"
                      sx={{ 
                        color: 'text.secondary',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        fontSize: '0.75rem',
                        mb: 1
                      }}
                    >
                      Description
                    </Typography>
                    <Typography 
                      variant="body1"
                      sx={{
                        color: category.description ? 'text.primary' : 'text.secondary',
                        fontStyle: category.description ? 'normal' : 'italic'
                      }}
                    >
                      {category.description || 'No description provided'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography 
                      variant="subtitle2"
                      sx={{ 
                        color: 'text.secondary',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        fontSize: '0.75rem',
                        mb: 1
                      }}
                    >
                      Status
                    </Typography>
                    <Chip
                      label={category.isActive ? 'Active' : 'Inactive'}
                      color={category.isActive ? 'success' : 'default'}
                      sx={{ 
                        borderRadius: '6px',
                        fontWeight: 500,
                        '& .MuiChip-label': {
                          px: 2
                        }
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography 
                      variant="subtitle2"
                      sx={{ 
                        color: 'text.secondary',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        fontSize: '0.75rem',
                        mb: 2
                      }}
                    >
                      Child Categories {category.children?.length > 0 && `(${category.children.length})`}
                    </Typography>
                    {category.children?.length > 0 ? (
                      <List sx={{ p: 0 }}>
                        {category.children.map((child) => (
                          <ListItem
                            key={child.id}
                            button
                            component={Link}
                            to={`/categories/${child.id}/view`}
                            sx={{
                              borderRadius: 2,
                              mb: 1,
                              border: '1px solid',
                              borderColor: 'divider',
                              '&:hover': {
                                backgroundColor: 'action.hover',
                                borderColor: 'primary.main',
                              },
                            }}
                          >
                            <ListItemIcon sx={{ minWidth: 40 }}>
                              <SubdirectoryArrowRightIcon color="action" />
                            </ListItemIcon>
                            <ListItemText 
                              primary={
                                <Typography sx={{ fontWeight: 500 }}>
                                  {child.name}
                                </Typography>
                              }
                              secondary={
                                <Typography 
                                  variant="body2" 
                                  sx={{
                                    color: 'text.secondary',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden'
                                  }}
                                >
                                  {child.description || 'No description'}
                                </Typography>
                              }
                            />
                            {!child.isActive && (
                              <Chip
                                label="Inactive"
                                size="small"
                                sx={{ 
                                  ml: 1,
                                  backgroundColor: 'grey.100'
                                }}
                              />
                            )}
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography 
                        variant="body2" 
                        sx={{
                          color: 'text.secondary',
                          fontStyle: 'italic'
                        }}
                      >
                        No child categories
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            <Grid item xs={12} md={4}>
              <Paper 
                sx={{ 
                  p: 3,
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <Typography 
                  variant="subtitle2"
                  sx={{ 
                    color: 'text.secondary',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    fontSize: '0.75rem',
                    mb: 2
                  }}
                >
                  Category Hierarchy
                </Typography>
                <Box sx={{ 
                  minHeight: 200, 
                  flexGrow: 1, 
                  maxWidth: '100%',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1,
                  p: 2
                }}>
                  <TreeView
                    defaultCollapseIcon={<ExpandMoreIcon />}
                    defaultExpandIcon={<ChevronRightIcon />}
                    expanded={expandedNodes}
                    onNodeToggle={(event, nodeIds) => setExpandedNodes(nodeIds)}
                    sx={{
                      flexGrow: 1,
                      overflowY: 'auto',
                      maxHeight: '500px',
                      '& .MuiTreeItem-root': {
                        '& .MuiTreeItem-content': {
                          py: 0.75,
                          borderRadius: 1,
                          '&:hover': {
                            backgroundColor: 'action.hover'
                          },
                          '&.Mui-selected': {
                            backgroundColor: 'primary.light'
                          }
                        }
                      }
                    }}
                  >
                    {category.root && renderTree(category.root)}
                  </TreeView>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      </div>
    </div>
  );
}

export default CategoryDetailPage; 
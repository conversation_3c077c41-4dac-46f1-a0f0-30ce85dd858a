import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  FormControl,
  FormControlLabel,
  Switch,
  Alert,
  Grid,
  CircularProgress,
  Select,
  MenuItem,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Divider
} from '@mui/material';
import { getCategoryById, updateCategory, createCategory, getAllCategories } from '../../services/categoryService';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';

function CategoryEditPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewCategory = id === 'create';
  const [loading, setLoading] = useState(!isNewCategory);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [availableParents, setAvailableParents] = useState([]);
  const [categoryData, setCategoryData] = useState(null);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true,
    parentId: '',
    slug: ''
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        if (isNewCategory) {
          // For new category, load all categories as potential parents
          const allCategories = await getAllCategories();
          setAvailableParents(allCategories);
          setLoading(false);
        } else {
          // Load current category data
          const data = await getCategoryById(id);
          setCategoryData(data);
          
          // Load all categories for parent selection
          const allCategories = await getAllCategories();
          
          // Filter out the current category and its children from available parents
          const filterParents = (categories, currentId) => {
            return categories.filter(cat => {
              // Exclude self
              if (cat.id === currentId) return false;
              
              // Exclude children
              const isChild = data.children?.some(child => child.id === cat.id);
              if (isChild) return false;
              
              return true;
            });
          };
          
          setAvailableParents(filterParents(allCategories, id));
          
          // Set form data with the correct parent ID
          setFormData({
            name: data.name,
            description: data.description,
            isActive: data.isActive,
            parentId: data.parentId || '',
            slug: data.slug || ''
          });
        }
      } catch (err) {
        const errorMessage = err.response?.data?.message || 'Failed to load data';
        setError(errorMessage);
        console.error('Error loading data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isNewCategory]);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: name === 'isActive' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    try {
      const dataToSubmit = {
        ...formData,
        parentId: formData.parentId || null
      };

      if (isNewCategory) {
        const result = await createCategory(dataToSubmit);
        if (result.error) {
          setError(result.message || 'Failed to create category');
          return;
        }
        setSuccess(true);
        setTimeout(() => {
          navigate('/categories');
        }, 2000);
      } else {
        const result = await updateCategory(id, dataToSubmit);
        // Check if result exists and doesn't have an error property
        if (result) {
          setSuccess(true);
          setTimeout(() => {
            navigate('/categories');
          }, 2000);
        } else {
          setError('Failed to update category');
        }
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'An error occurred';
      setError(errorMessage);
      console.error('Error saving category:', err);
    }
  };

  if (loading) {
    return (
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '400px',
          flexDirection: 'column',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography variant="body1" color="text.secondary">
          Loading category details...
        </Typography>
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
              {isNewCategory ? 'Create Category' : 'Edit Category'}
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Category {isNewCategory ? 'created' : 'updated'} successfully! Redirecting...
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Category Name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Slug"
                        name="slug"
                        value={formData.slug || ''}
                        onChange={handleChange}
                        helperText="URL-friendly version of the name (auto-generated)"
                        InputProps={{
                          readOnly: true
                        }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <InputLabel id="parent-category-label">Parent Category</InputLabel>
                        <Select
                          labelId="parent-category-label"
                          id="parentId"
                          name="parentId"
                          value={formData.parentId}
                          onChange={handleChange}
                          label="Parent Category"
                        >
                          <MenuItem value="">
                            <em>None (Root Category)</em>
                          </MenuItem>
                          {availableParents.map((parent) => (
                            <MenuItem 
                              key={parent.id} 
                              value={parent.id}
                            >
                              {parent.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        name="description"
                        value={formData.description || ''}
                        onChange={handleChange}
                        multiline
                        rows={4}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.isActive}
                            onChange={handleChange}
                            name="isActive"
                            color="primary"
                          />
                        }
                        label="Active"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                        <Button
                          variant="outlined"
                          onClick={() => navigate('/categories')}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          variant="contained"
                          color="primary"
                        >
                          {isNewCategory ? 'Create Category' : 'Update Category'}
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Grid>

                {!isNewCategory && categoryData && (
                  <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography variant="h6" gutterBottom>
                        Child Categories {categoryData.children?.length > 0 && `(${categoryData.children.length})`}
                      </Typography>
                      <Divider sx={{ mb: 2 }} />
                      {categoryData.children?.length > 0 ? (
                        <List>
                          {categoryData.children.map((child) => (
                            <ListItem
                              key={child.id}
                              sx={{
                                borderRadius: 1,
                                mb: 1,
                                bgcolor: 'background.paper',
                              }}
                            >
                              <ListItemIcon>
                                <SubdirectoryArrowRightIcon />
                              </ListItemIcon>
                              <ListItemText 
                                primary={child.name}
                                secondary={child.description || 'No description'}
                              />
                              {!child.isActive && (
                                <Chip
                                  label="Inactive"
                                  size="small"
                                  sx={{ ml: 1 }}
                                />
                              )}
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No child categories
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </form>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default CategoryEditPage; 
import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  useTheme
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { getApplicantStats } from '../services/jobApplicationService';
import { useUser } from '../contexts/UserContext';
import './AdminDashboard.css';

function AdminDashboard() {
  const theme = useTheme();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [monthlyStats, setMonthlyStats] = useState([]);
  const [dashboardStats, setDashboardStats] = useState({
    totalActiveJobs: 0,
    totalApplicants: 0,
    currentMonthApplicants: 0,
    totalPass: 0,
    totalFail: 0,
    totalInProgress: 0
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const stats = await getApplicantStats();
        setMonthlyStats(stats.monthsData);
        setDashboardStats({
          totalActiveJobs: stats.totalActiveJobs,
          totalApplicants: stats.totalApplicants,
          currentMonthApplicants: stats.currentMonthApplicants,
          totalPass: stats.totalPass,
          totalFail: stats.totalFail,
          totalInProgress: stats.totalInProgress
        });

        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data. Please try again later.');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatChartData = () => {
    return monthlyStats.map(stat => ({
      name: stat.label,
      applications: stat.count
    }));
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Admin Dashboard
      </Typography>

      <Typography variant="subtitle1" gutterBottom>
        Welcome back, {user?.name || 'Admin'}!
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {/* Summary Cards */}
        <Grid item xs={12} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Total Active Jobs
              </Typography>
              <Typography className="dashboard-stat" variant="h3" component="div" color="primary">
                {dashboardStats.totalActiveJobs}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Jobs currently active
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                This Month Applicants
              </Typography>
              <Typography className="dashboard-stat" variant="h3" component="div" color="primary">
                {dashboardStats.currentMonthApplicants}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Submitted applicants this month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Total Applicants
              </Typography>
              <Typography className="dashboard-stat" variant="h3" component="div" color="primary">
                {dashboardStats.totalApplicants}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                All submitted applicants
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Typography variant="h6" component="div" gutterBottom>
                Applicant Status
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Box sx={{ textAlign: 'center', flex: 1 }}>
                  <Typography variant="h4" color="success.main">
                    {dashboardStats.totalPass}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Passed
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center', flex: 1 }}>
                  <Typography variant="h4" color="error.main">
                    {dashboardStats.totalFail}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Failed
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center', flex: 1 }}>
                  <Typography variant="h4" color="info.main">
                    {dashboardStats.totalInProgress}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In Progress
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Chart */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }} className="dashboard-chart-container">
            <Typography variant="h6" component="div" gutterBottom align="center">
              Monthly Job Applications
            </Typography>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={formatChartData()}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <Tooltip />
                <Legend />
                <Bar dataKey="applications" fill={theme.palette.primary.main} name="Applications" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default AdminDashboard;

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material';
import { ArrowLeft, Edit, Copy } from 'lucide-react';
import { getEmployerCommissionById, copyCommissionToOfficial } from '../../services/employerCommissionService';
import {
  COMMISSION_PHASE,
  EMPLOYMENT_TYPE,
  COMMISSION_STATUS_LABELS,
  COMMISSION_PHASE_LABELS,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS
} from '../../constants/collaboratorPolicy';
import { formatDate } from '../../utils/dateUtils';
import { useToast } from "../../contexts/ToastContext";
import { formatCurrency } from '../../utils/currencyUtils';

const EmployerCommissionViewPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [commission, setCommission] = useState(null);
  const [loading, setLoading] = useState(true);
  const [copying, setCopying] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const { showToast } = useToast();

  const loadCommission = async () => {
    setLoading(true);
    try {
      const response = await getEmployerCommissionById(id);
      setCommission(response);
    } catch (error) {
      console.error('Failed to load commission:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToOfficial = async () => {
    setCopying(true);
    try {
      const newCommission = await copyCommissionToOfficial(id);
      setCopySuccess(true);
      setTimeout(() => {
        navigate(`/employer-commissions/${newCommission.id}`);
        setCopySuccess(false);
      }, 1500);
    } catch (error) {
      console.error('Failed to copy commission:', error);
      showToast(`${error.response?.data?.message || 'Failed to copy commission to official phase'}`, 'error')
    } finally {
      setCopying(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadCommission();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Commission Details
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<ArrowLeft />}
                component={Link}
                to="/employer-commissions"
              >
                Back to List
              </Button>
              {commission?.employmentType === EMPLOYMENT_TYPE.FULLTIME &&
                commission?.commissionPhase === COMMISSION_PHASE.ONBOARDING && (
                <Button
                  variant="outlined"
                  startIcon={<Copy />}
                  onClick={handleCopyToOfficial}
                  disabled={copying}
                  color="secondary"
                >
                  {copying ? 'Copying...' : 'Copy to Official'}
                </Button>
              )}
              <Button
                variant="contained"
                startIcon={<Edit />}
                component={Link}
                to={`/employer-commissions/${id}/edit`}
              >
                Edit Commission
              </Button>
            </Box>
          </Box>

          {copySuccess && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Commission copied to official phase successfully! Redirecting...
            </Alert>
          )}

          <Paper sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Employer Email
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.employerEmail}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Applicant
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.applicantName && (
                    <Link
                      to={`/applicants/${commission.jobApplicationId}`}
                      style={{ textDecoration: 'none' }}
                    >
                      {commission.applicantName}
                    </Link>
                  )}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Employment Type
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {EMPLOYMENT_TYPE_LABELS[commission?.employmentType]}
                </Typography>
              </Grid>

              {commission?.employmentType === EMPLOYMENT_TYPE.FULLTIME && (
                <>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Candidate Level
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {LEVEL_LABELS[commission?.candidateLevel]}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      English Skill
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {ENGLISH_SKILL_LABELS[commission?.englishSkill]}
                    </Typography>
                  </Grid>
                </>
              )}

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Commission Phase
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={COMMISSION_PHASE_LABELS[commission?.commissionPhase]} 
                    color="info"
                    size="small"
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Onboarding Date
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.onboardingDate ? formatDate(commission.onboardingDate) : 'Not set'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Official Date
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.officialDate ? formatDate(commission.officialDate) : 'Not set'}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Financial Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Commission Amount
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mb: 2 }}>
                  {formatCurrency(commission?.amount)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={COMMISSION_STATUS_LABELS[commission?.status]} 
                    color={commission?.status === 'Paid' ? 'success' : commission?.status === 'Pending' ? 'warning' : 'default'}
                    size="medium"
                  />
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Notes
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.notes || 'No notes available'}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Audit Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Approved By
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.approvedBy || 'Not approved yet'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Approved At
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.approvedAt ? formatDate(commission.approvedAt) : 'Not approved yet'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created Date
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {formatDate(commission?.createdAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Updated Date
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {formatDate(commission?.updatedAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created By
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.createdBy || 'System'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Updated By
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commission?.updatedBy || 'System'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </div>
    </div>
  );
};

export default EmployerCommissionViewPage;

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router-dom"
import { ArrowLeft, MapPin, Clock, Briefcase, DollarSign, UserPlus, Save } from "lucide-react"
import { CircularProgress, TextField } from '@mui/material';
import { getJobById } from "../../services/jobService"
import "./EmployerJobDetailPage.css"
import ApplicationModal from "../../components/ApplicationModal"

function EmployerJobDetailPage() {
  const { id } = useParams()
  const navigate = useNavigate();
  const [job, setJob] = useState({
    title: '',
    location: '',
    type: '',
    code: '',
    salary: '',
    description: '',
    responsibilities: [],
    technicalSkills: [],
    softSkills: [],
    fullAddress: '',
    workingHours: '',
    benefits: [],
    slug: ''
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [saveStatus, setSaveStatus] = useState({ loading: false, error: null, success: false })

  useEffect(() => {
    const fetchJobDetail = async () => {
      try {
        const jobData = await getJobById(id);
        setJob(jobData);
      } catch (error) {
        setError('Failed to load job details');
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetail();
  }, [id])

  if (loading) return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="job-detail-page cms-view">
          <div className="loading-container" style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <CircularProgress />
            <p>Loading job details...</p>
          </div>
        </div>
      </div>
    </div>
  );

  if (error) return <div className="error">{error}</div>
  if (!job) return <div>Job not found</div>

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="job-detail-page cms-view">
          <div className="page-header">
            <div className="header-content">
              <div className="left-content">
                <Link to="/employer/jobs" className="back-link">
                  <ArrowLeft size={16} /> Back to Jobs List
                </Link>
                <h1>{job.title}</h1>
                <div className="job-meta">
                  <div className="meta-item">
                    <MapPin size={16} />
                    <span>{job.location}</span>
                  </div>
                  <div className="meta-item">
                    <Clock size={16} />
                    <span>{job.type}</span>
                  </div>
                  <div className="meta-item">
                    <Briefcase size={16} />
                    <span>Code: {job.code}</span>
                  </div>
                  <div className="meta-item">
                    <DollarSign size={16} />
                    <span>{job.salary}</span>
                  </div>
                  <div className="meta-item">
                    <span>Slug: {job.slug}</span>
                  </div>
                </div>
              </div>
              <div className="right-content">
                <div className="action-buttons">
                  <button className="add-applicant-btn" onClick={() => setIsModalOpen(true)}>
                    <UserPlus size={16} /> Add Applicants
                  </button>
                </div>
              </div>
            </div>
          </div>

          <main className="main-content">
            <div className="page-layout">
              {/* Left Column - Job Details */}
              <div className="content-area">
                <div className="container">
                  {/* Job Overview */}
                  <div className="card">
                    <h2>Job Overview</h2>
                    <p>{job.description}</p>
                  </div>

                  {/* Key Responsibilities */}
                  <div className="card">
                    <h2>Key Responsibilities</h2>
                    <ul>
                      {job.responsibilities.map((responsibility, index) => (
                        <li key={index}>{responsibility}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Job Requirements */}
                  <div className="card">
                    <h2>Job Requirements</h2>
                    <h3>Technical Skills:</h3>
                    <ul>
                      {job.technicalSkills.map((skill, index) => (
                        <li key={index}>{skill}</li>
                      ))}
                    </ul>
                    <h3>Soft Skills:</h3>
                    <ul>
                      {job.softSkills.map((skill, index) => (
                        <li key={index}>{skill}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Right Sidebar */}
              <div className="page-sidebar">
                {/* Position Details Card */}
                <div className="card">
                  <h2>Position Details</h2>
                  <div className="details-list">
                    <div className="detail-item">
                      <h4>Full Address</h4>
                      <p>{job.fullAddress}</p>
                    </div>
                    <div className="detail-item">
                      <h4>Working Hours</h4>
                      <p>{job.workingHours}</p>
                    </div>
                    <div className="detail-item">
                      <h4>Benefits</h4>
                      <ul>
                        {job.benefits.map((benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      <ApplicationModal
        showRefPerson={false}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        openJobs={[job]}
      />
    </div>
  );
}

export default EmployerJobDetailPage; 
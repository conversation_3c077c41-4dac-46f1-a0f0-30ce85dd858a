"use client"

import React, { useState, useEffect, useMemo } from "react"
import { <PERSON> } from "react-router-dom"
import { deleteJobApplication, getAllJobApplications } from "../../services/jobApplicationService"
import { getAllJobs } from "../../services/jobService"
import ApplicationModal from "../../components/ApplicationModal"
import { RoleBasedButton } from "../../components/RoleBased"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  Pagination,
  Button,
  Box,
  Typography,
  Chip,
  Grid,
  FormControl,
  InputLabel,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  CircularProgress
} from '@mui/material'
import { APPLICATION_STATUS, PROCESS_STATUS } from "../../constants/jobApplications"

function EmployerJobApplicationsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [appliedSearchTerm, setAppliedSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [processStatusFilter, setProcessStatusFilter] = useState('ALL')
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  })
  const [applications, setApplications] = useState([])
  const [openJobs, setOpenJobs] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [page, setPage] = useState(1)
  const [rowsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [applicationToDelete, setApplicationToDelete] = useState(null)

  const formatDate = (date) => {
    if (!date) return 'N/A';
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        return 'N/A';
      }
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  };

  const loadApplications = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage,
        search: appliedSearchTerm,
        status: statusFilter !== 'ALL' ? statusFilter : undefined,
        processStatus: processStatusFilter !== 'ALL' ? processStatusFilter : undefined,
        startDate: dateRange.start || undefined,
        endDate: dateRange.end || undefined
      };
      const response = await getAllJobApplications(params);
      setApplications(response.items);
      setTotalPages(response.meta.totalPages);
    } catch (error) {
      setError('Failed to load applications');
      console.error('Error loading applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadOpenJobs = async () => {
    try {
      const jobsData = await getAllJobs();
      const openJobsData = jobsData.items.filter(job => job.status === 'OPEN');
      setOpenJobs(openJobsData);
    } catch (error) {
      console.error('Failed to load open jobs:', error);
    }
  };

  useEffect(() => {
    loadApplications();
    loadOpenJobs();
  }, [page, rowsPerPage, appliedSearchTerm, statusFilter, processStatusFilter, dateRange]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const getStatusChipColor = (status) => {
    switch (status) {
      case APPLICATION_STATUS.ACCEPT:
        return 'success'
      case APPLICATION_STATUS.REJECT:
        return 'error'
      case APPLICATION_STATUS.NEW:
        return 'info'
      default:
        return 'default'
    }
  }

  const getProcessStatusChipColor = (status) => {
    switch (status) {
      case PROCESS_STATUS.INTERVIEW_SCHEDULED:
        return 'primary'
      case PROCESS_STATUS.OFFER_SENT:
        return 'success'
      case PROCESS_STATUS.PENDING_REVIEW:
        return 'warning'
      default:
        return 'default'
    }
  }

  const getStatusLabel = (status) => {
    const labels = {
      [APPLICATION_STATUS.NEW]: 'New Application',
      [APPLICATION_STATUS.ACCEPT]: 'Accepted',
      [APPLICATION_STATUS.REJECT]: 'Rejected'
    };
    return labels[status] || status;
  };

  const getProcessStatusLabel = (status) => {
    return status ? status.charAt(0) + status.slice(1).toLowerCase() : '';
  };

  const calculateRemainTime = (timeAlive, createdAt) => {
    if (!timeAlive || !createdAt) return 0;
    const createdDate = new Date(createdAt);
    const now = new Date();
    const daysSinceCreated = Math.floor((now - createdDate) / (1000 * 60 * 60 * 24));
    const remainingDays = timeAlive - daysSinceCreated;
    return Math.max(0, remainingDays);
  }

  const handleDeleteClick = (application) => {
    setApplicationToDelete(application)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      await deleteJobApplication(applicationToDelete.id)
      await loadApplications() // Refresh the list
      setDeleteDialogOpen(false)
      setApplicationToDelete(null)
    } catch (error) {
      console.error('Failed to delete application:', error)
    }
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
    setApplicationToDelete(null)
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Job Applications Management
            </Typography>
            <Button
              variant="contained"
              color="primary"
              onClick={() => setIsModalOpen(true)}
            >
              Create Application
            </Button>
          </Box>

          <Paper sx={{ width: '100%', mb: 2 }}>
            <Box sx={{ display: 'flex', gap: 2, p: 2, flexDirection: 'column' }}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="Search Applications"
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      setAppliedSearchTerm(searchTerm);
                    }
                  }}
                  fullWidth
                />
                <Button
                  onClick={() => setAppliedSearchTerm(searchTerm)}
                  variant="contained"
                  size="small">
                  Search
                </Button>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Status Filter</InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      label="Status Filter"
                    >
                      <MenuItem value="ALL">All Statuses</MenuItem>
                      {Object.values(APPLICATION_STATUS).map((status) => (
                        <MenuItem key={status} value={status}>
                          {getStatusLabel(status)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Process Status Filter</InputLabel>
                    <Select
                      value={processStatusFilter}
                      onChange={(e) => setProcessStatusFilter(e.target.value)}
                      label="Process Status Filter"
                    >
                      <MenuItem value="ALL">All Process Statuses</MenuItem>
                      {Object.values(PROCESS_STATUS).map((status) => (
                        <MenuItem key={status} value={status}>
                          {getProcessStatusLabel(status)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    type="date"
                    label="From Date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    type="date"
                    label="To Date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    fullWidth
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ overflowX: 'auto' }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Applicant Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Phone</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Reference Person</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Job Title</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Job Location</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Salary</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Submitted Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Remaining Time (Job)</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Process Status</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {applications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell>
                        <Link
                          to={`/employer/applicants/${application.id}/view`}
                          style={{
                            color: '#1976d2',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          {application.name}
                        </Link>
                      </TableCell>
                      <TableCell>{application.email}</TableCell>
                      <TableCell>{application.phoneNumber}</TableCell>
                      <TableCell>{application.refPerson || '-'}</TableCell>
                      <TableCell>{application.jobTitle || '-'}</TableCell>
                      <TableCell>{application.jobLocation || '-'}</TableCell>
                      <TableCell>{application.jobSalary || '-'}</TableCell>
                      <TableCell>{formatDate(application.jobCreatedAt)}</TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            backgroundColor: calculateRemainTime(application.jobTimeAlive, application.jobCreatedAt) === 0 ? '#ffebee' : '#f3f4f6',
                            color: calculateRemainTime(application.jobTimeAlive, application.jobCreatedAt) === 0 ? '#f44336' : '#4b5563',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            display: 'inline-block',
                            fontSize: '0.875rem'
                          }}
                        >
                          {calculateRemainTime(application.jobTimeAlive, application.jobCreatedAt)} days
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <Chip
                            label={getStatusLabel(application.status || APPLICATION_STATUS.NEW)}
                            color={getStatusChipColor(application.status || APPLICATION_STATUS.NEW)}
                            size="small"
                          />
                        </Stack>
                      </TableCell>
                      <TableCell>
                        {application.processStatus && (
                          <Stack direction="row" spacing={1}>
                            <Chip
                              label={getProcessStatusLabel(application.processStatus)}
                              color={getProcessStatusChipColor(application.processStatus)}
                              size="small"
                              variant="outlined"
                            />
                          </Stack>
                        )}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            component={Link}
                            to={`/employer/applicants/${application.id}/view`}
                          >
                            Review
                          </Button>
                          <RoleBasedButton
                            allowedRoles={['Admin', 'HR']}
                            buttonProps={{
                              variant: "outlined",
                              size: "small",
                              color: "error",
                              onClick: () => handleDeleteClick(application)
                            }}
                          >
                            Delete
                          </RoleBasedButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, p: 2 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handleChangePage}
                color="primary"
              />
            </Box>
          </Paper>
        </Box>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete Application
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete the application for {applicationToDelete?.name}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <ApplicationModal
        showRefPerson={false}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          loadApplications();
        }}
        openJobs={openJobs}
      />
    </div>
  );
}

export default EmployerJobApplicationsPage;
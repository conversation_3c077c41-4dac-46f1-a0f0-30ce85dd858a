"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"
import { ArrowLeft, Mail, Phone, Edit } from "lucide-react"
import { getJobApplicationById, updateJobApplication, downloadCV } from "../../services/jobApplicationService"
import { getJobById } from "../../services/jobService"
import { getInterviewByJobApplicationId } from "../../services/interviewService"
import CVViewerModal from "../../components/CVViewerModal"
import {
  Box,
  Button,
  TextField,
  CircularProgress
} from '@mui/material'
import { APPLICATION_STATUS } from "../../constants/jobApplications"
import "./EmployerApplicationDetailPage.css"

// Import our component sections
import { JobSection, ApplicationSection, InterviewSection } from '../jobs/components'

function EmployerApplicationDetailPage() {
  const { id } = useParams()
  const [application, setApplication] = useState(null)
  const [job, setJob] = useState(null)
  const [interview, setInterview] = useState(null)
  const [interviewLoading, setInterviewLoading] = useState(false)
  const [interviewError, setInterviewError] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [editMode, setEditMode] = useState(false)
  const [editedNotes, setEditedNotes] = useState("")
  const [updateError, setUpdateError] = useState(null)
  const [updateSuccess, setUpdateSuccess] = useState(false)
  const [cvModalOpen, setCvModalOpen] = useState(false)
  const [cvUrl, setCvUrl] = useState(null)
  const [cvLoading, setCvLoading] = useState(false)
  const [cvError, setCvError] = useState(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const applicationData = await getJobApplicationById(id)
        setApplication(applicationData)
        setEditedNotes(applicationData.notes || "")

        // Only fetch job details if we have a valid jobId (not "general")
        if (applicationData.jobId && applicationData.jobId !== "general") {
          try {
            const jobData = await getJobById(applicationData.jobId)
            setJob(jobData)
          } catch (jobError) {
            console.error('Error loading job:', jobError)
          }
        }

        // Fetch interview details if application exists
        if (applicationData.id) {
          try {
            setInterviewLoading(true)
            const interviewData = await getInterviewByJobApplicationId(applicationData.id)
            setInterview(interviewData)
          } catch (interviewError) {
            console.error('Error loading interview:', interviewError)
          } finally {
            setInterviewLoading(false)
          }
        }
      } catch (error) {
        setError('Failed to load application details')
        console.error('Error loading application:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [id])

  const handleNotesUpdate = async () => {
    try {
      const updatedData = {
        ...application,
        notes: editedNotes,
        lastUpdated: new Date().toISOString()
      }

      await updateJobApplication(id, updatedData)
      setApplication(prev => ({
        ...prev,
        ...updatedData
      }))
      setUpdateSuccess(true)
      setTimeout(() => {
        setUpdateSuccess(false)
        setEditMode(false)
      }, 2000)
    } catch (error) {
      console.error('Error updating notes:', error)
      setUpdateError('Failed to update notes. Please try again.')
    }
  }

  // Helper functions for formatting and display
  const getStatusLabel = (status) => {
    const labels = {
      [APPLICATION_STATUS.NEW]: 'New Application',
      [APPLICATION_STATUS.ACCEPT]: 'Accepted',
      [APPLICATION_STATUS.REJECT]: 'Rejected'
    }
    return labels[status] || status
  }

  const getProcessStatusLabel = (status) => {
    return status ? status.charAt(0) + status.slice(1).toLowerCase() : '';
  }

  const formatDate = (date) => {
    if (!date) return 'N/A';
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        return 'N/A';
      }
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  };

  const getStatusClass = (status) => {
    if (!status) return 'new';
    return status.toLowerCase();
  }

  const getProcessStatusClass = (status) => {
    if (!status) return '';
    return status.toLowerCase();
  }

  const formatTimeAlive = (timeAlive) => {
    if (!timeAlive) return 'N/A';
    return `${timeAlive} days`;
  }

  const calculateRemainTime = (timeAlive, createdAt) => {
    if (!timeAlive || !createdAt) return 0;

    const createdDate = new Date(createdAt);
    const now = new Date();
    const daysSinceCreated = Math.floor((now - createdDate) / (1000 * 60 * 60 * 24));
    const remainingDays = timeAlive - daysSinceCreated;

    return Math.max(0, remainingDays);
  }

  // CV handling
  const handleViewCV = async () => {
    if (!application?.id) return;

    setCvLoading(true);
    setCvError(null);

    try {
      const url = await downloadCV(application.id);
      setCvUrl(url);
      setCvModalOpen(true);
    } catch (error) {
      setCvError('Failed to load CV. Please try again.');
      console.error('Error loading CV:', error);
    } finally {
      setCvLoading(false);
    }
  };

  const handleCloseModal = () => {
    setCvModalOpen(false);
    if (cvUrl) {
      URL.revokeObjectURL(cvUrl);
      setCvUrl(null);
    }
  };

  // Loading and error states
  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <div className="employer-application-detail-page cms-view">
            <div className="loading-container">
              <CircularProgress />
              <p>Loading application details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <div className="employer-application-detail-page cms-view">
            <Box color="error" sx={{ p: 3 }}>{error}</Box>
          </div>
        </div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <div className="employer-application-detail-page cms-view">
            <Box sx={{ p: 3 }}>Application not found</Box>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="employer-application-detail-page cms-view">
          {/* Page Header */}
          <div className="page-header">
            <div className="header-content">
              <div className="left-content">
                <Link to="/employer/applicants" className="back-link">
                  <ArrowLeft size={16} /> Back to Applications
                </Link>
                <h1>{application?.name || 'Application Details'}</h1>
                <div className="applicant-meta">
                  <div className="meta-item">
                    <Mail size={16} />
                    <span>{application?.email || 'N/A'}</span>
                  </div>
                  <div className="meta-item">
                    <Phone size={16} />
                    <span>{application?.phoneNumber || 'N/A'}</span>
                  </div>
                  {application?.refPerson && (
                  <div className="meta-item">
                    <div style={{ width: 16, height: 16, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>👤</div>
                    <span>Ref: {application.refPerson}</span>
                  </div>
                  )}
                </div>
              </div>
              <div className="right-content">
                <div className="action-buttons">
                  {!editMode && (
                    <button className="btn secondary-btn" onClick={() => setEditMode(true)}>
                      <Edit size={16} /> Edit Notes
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="page-layout">
            <div className="content-area">
              {/* Job Section */}
              <JobSection
                job={job}
                application={application}
                formatDate={formatDate}
                formatTimeAlive={formatTimeAlive}
                calculateRemainTime={calculateRemainTime}
              />

              {/* Application Section */}
              <ApplicationSection
                application={application}
                getStatusLabel={getStatusLabel}
                getStatusClass={getStatusClass}
                getProcessStatusLabel={getProcessStatusLabel}
                getProcessStatusClass={getProcessStatusClass}
                formatDate={formatDate}
                handleViewCV={handleViewCV}
                cvLoading={cvLoading}
                cvError={cvError}
              />

              {/* Interview Section */}
              <InterviewSection
                interview={interview}
                interviewLoading={interviewLoading}
                interviewError={interviewError}
                application={application}
                formatDate={formatDate}
                handleCreateInterview={() => {}}
                showRound1Modal={false}
                setShowRound1Modal={() => {}}
                showRound2Modal={false}
                setShowRound2Modal={() => {}}
                round1Data={{}}
                setRound1Data={() => {}}
                round2Data={{}}
                setRound2Data={() => {}}
                handleUpdateRound1={() => {}}
                handleUpdateRound2={() => {}}
                handleOpenRound1Modal={() => {}}
                handleOpenRound2Modal={() => {}}
                handleCreateNewRound2={() => {}}
              />
            </div>

            {/* Edit Notes Sidebar */}
            {editMode && (
              <div className="page-sidebar">
                <div className="card">
                  <h2>Edit Notes</h2>
                  {updateError && (
                    <Box color="error" sx={{ mb: 2 }}>
                      {updateError}
                    </Box>
                  )}
                  {updateSuccess && (
                    <Box color="success.main" sx={{ mb: 2 }}>
                      Notes updated successfully!
                    </Box>
                  )}
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Notes"
                    value={editedNotes || ''}
                    onChange={(e) => setEditedNotes(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      onClick={handleNotesUpdate}
                    >
                      Save Notes
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setEditMode(false)}
                    >
                      Cancel
                    </Button>
                  </Box>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CV Viewer Modal */}
      <CVViewerModal
        open={cvModalOpen}
        onClose={handleCloseModal}
        cvUrl={cvUrl}
        fileName={`${application?.name}'s CV`}
      />
    </div>
  )
}

export default EmployerApplicationDetailPage
/* Banner Section */
.job-banner {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url("../../../public/images/office-banner.jpg");
  background-size: cover;
  background-position: center;
  height: 300px;
  display: flex;
  align-items: center;
  position: relative;
  color: white;
}

.banner-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: rgba(138, 127, 255, 0.2);
}

.banner-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.banner-subtitle {
  font-size: 1.25rem;
  max-width: 600px;
  color: rgba(255, 255, 255, 0.9);
}

/* Layout */
.admin-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-content {
  flex: 1;
  padding: 0;
  width: 100% !important;
}

.job-detail-page {
  min-height: 100vh;
}

.job-detail-page.cms-view {
  background-color: #f5f6fa;
}

/* Header Styles */
.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.left-content {
  flex: 1;
}

.left-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #5a5c69;
  text-decoration: none;
  margin-bottom: 16px;
  font-weight: 500;
  transition: all 0.2s;
}

.back-link:hover {
  color: #4e73df;
}

.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-top: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
}

.meta-item svg {
  color: #4f46e5;
}

/* Layout Structure */
.page-layout {
  display: flex;
  gap: 2rem;
  padding: 0 2rem;
}

.content-area {
  flex: 1;
  min-width: 0;
}

.page-sidebar {
  width: 320px;
  position: sticky;
  top: 1.5rem;
  height: fit-content;
}

/* Card Styles */
.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s, border-color 0.3s;
}

.card h2 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e3e6f0;
}

.card h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 1.1rem;
  color: #4b5563;
}

.card ul {
  margin: 0;
  padding-left: 20px;
}

.card li {
  margin-bottom: 8px;
  color: #4b5563;
}

/* Stats Card */
.stats-card {
  background: linear-gradient(145deg, #4f46e5, #4338ca);
  color: white;
}

.stats-card h2 {
  color: white;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 16px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item h4 {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-item p {
  margin: 4px 0 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

/* Badge Styles */
.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #6b7280;
  transition: all 0.2s ease;
}

.badge.active {
  background-color: #4f46e5;
  color: white;
}

.badge.green {
  background-color: #10b981;
  color: white;
}

.badge.red {
  background-color: #ef4444;
  color: white;
}

/* Details List */
.details-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item h4 {
  margin: 0 0 8px;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.detail-item p {
  margin: 0;
  color: #111827;
}

/* Benefits List */
.benefits-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.benefits-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
  color: #334155;
}

.benefits-list li:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.benefits-list li::before {
  content: "•";
  color: #4f46e5;
  font-weight: bold;
}

/* Button Styles */
.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
}

.secondary-btn {
  background: #4f46e5;
  color: white;
}

.secondary-btn:hover {
  background: #4338ca;
}

.danger-btn {
  background: #ef4444;
  color: white;
}

.danger-btn:hover {
  background: #dc2626;
}

.outline-btn {
  background: transparent;
  border: 1px solid #4e73df;
  color: #4e73df;
  padding: 0.625rem 1.25rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.outline-btn:hover {
  background: #4e73df;
  color: white;
}

/* Add Applicant Button */
.add-applicant-btn {
  background: #10b981;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.add-applicant-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.add-applicant-btn:active {
  transform: translateY(0);
}

.add-applicant-btn svg {
  width: 18px;
  height: 18px;
}

/* Status Controls */
.status-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #ffffff;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.04);
}

.status-select {
  padding: 0.5rem 2rem 0.5rem 1rem;
  border: 1px solid #e3e6f0;
  border-radius: 0.375rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

.error {
  color: #ef4444;
  padding: 16px;
  text-align: center;
  background-color: #fee2e2;
  border-radius: 8px;
  margin: 16px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .page-layout {
    flex-direction: column;
    padding: 0 1rem;
  }

  .page-sidebar {
    width: 100%;
    position: static;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .left-content h1 {
    font-size: 1.5rem;
  }

  .job-meta {
    gap: 1rem;
  }

  .card {
    padding: 1rem;
  }
}

/* Notes Section Styles */
.notes-section {
  margin-bottom: 2rem;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.save-notes-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  border-radius: 6px;
  font-weight: 500;
  background: #10b981;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.save-notes-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.save-notes-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  color: #ef4444;
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #fee2e2;
  border-radius: 6px;
  font-size: 0.875rem;
}

.success-message {
  color: #059669;
  padding: 0.75rem;
  margin-bottom: 1rem;
  background-color: #d1fae5;
  border-radius: 6px;
  font-size: 0.875rem;
} 
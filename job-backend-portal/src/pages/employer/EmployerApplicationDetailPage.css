.employer-application-detail-page {
  min-height: 100vh;
}

.employer-application-detail-page.cms-view {
  background-color: #f5f6fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.left-content {
  flex: 1;
}

.left-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
}

.applicant-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.meta-item svg {
  color: #4f46e5;
}

.right-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.back-link {
  color: #5a5c69;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.back-link:hover {
  color: #4e73df;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.secondary-btn {
  background: #4f46e5;
  color: white;
}

.secondary-btn:hover {
  background: #4338ca;
}

.page-layout {
  display: flex;
  gap: 2rem;
  padding: 0 2rem;
}

.content-area {
  flex: 1;
  min-width: 0;
}

.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card h2 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item h3 {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.info-item p {
  color: #111827;
  font-size: 1rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.new {
  background-color: #e0f2fe;
  color: #0369a1;
}

.status-badge.accept {
  background-color: #dcfce7;
  color: #15803d;
}

.status-badge.reject {
  background-color: #fee2e2;
  color: #b91c1c;
}

/* Process badges */
.process-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.process-badge.interview_scheduled {
  background-color: #e0f2fe;
  color: #0369a1;
}

.process-badge.offer_sent {
  background-color: #dcfce7;
  color: #15803d;
}

.process-badge.pending_review {
  background-color: #fef3c7;
  color: #b45309;
}

/* Page sidebar */
.page-sidebar {
  width: 400px;
  flex-shrink: 0;
} 
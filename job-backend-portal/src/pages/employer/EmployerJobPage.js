import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getOptions } from '../../services/optionService';
import { OPTION_TYPES } from '../../constants/jobs';
import { getAllJobs } from '../../services/jobService';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  Pagination,
  Box,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
  Button,
  Grid,
  FormControl,
  InputLabel
} from '@mui/material';

const JobStatus = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED'
};

const EmployerJobPage = () => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState('All');
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [categories, setCategories] = useState(['All']);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const formatDate = (date) => {
    return new Date(date).toISOString().split('T')[0];
  };

  const loadJobs = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage,
        search: searchTerm,
        category: filterCategory,
      };

      const response = await getAllJobs(params);
      setJobs(response.items);
      setTotalPages(response.meta.totalPages);
    } catch (error) {
      console.error('Failed to load jobs:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load jobs',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const params = {
          page: -1,
          limit: -1,
          optionNames: OPTION_TYPES.JOB_CATEGORIES
        };
        const categories = await getOptions(params);
        setCategories(["All",...categories.items.map(c => c.optionTitle)]); 
      }
      catch (error) {
        console.error('Failed to load categories:', error);
      }
    };
    
    loadCategories();
  }, [])

  useEffect(() => {
    loadJobs();
  }, [page, rowsPerPage, appliedSearchTerm, filterCategory]);

  useEffect(() => {
    setPage(1);
  }, [appliedSearchTerm, filterCategory]);

  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        p: 3
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h1">
          My Jobs
        </Typography>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <TextField
            label="Search Jobs"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ flexGrow: 1 }}
          />
          <Button
            onClick={() => setAppliedSearchTerm(searchTerm)}
            variant="contained"
            size="small"
          >
            Search
          </Button>
        </Box>

        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Category Filter</InputLabel>
              <Select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                label="Category Filter"
              >
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box sx={{ width: '100%', overflowX: 'auto' }}>
          <Table sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Category</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Location</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Created Date</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Duration (d)</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jobs.map((job) => (
                <TableRow
                  key={job.id}
                  hover
                  component={Link}
                  to={`/employer/jobs/${job.id}/view`}
                  sx={{
                    textDecoration: 'none',
                    '&:hover': {
                      cursor: 'pointer',
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    }
                  }}
                >
                  <TableCell sx={{ color: '#1976d2' }}>{job.title}</TableCell>
                  <TableCell>{job.category}</TableCell>
                  <TableCell>{job.location}</TableCell>
                  <TableCell>{job.type}</TableCell>
                  <TableCell>
                    <Box
                      sx={{
                        backgroundColor: job.status === JobStatus.OPEN ? '#e8f5e9' : '#ffebee',
                        color: job.status === JobStatus.OPEN ? '#2e7d32' : '#c62828',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontSize: '0.875rem'
                      }}
                    >
                      {job.status || JobStatus.OPEN}
                    </Box>
                  </TableCell>
                  <TableCell>{formatDate(job.createdAt)}</TableCell>
                  <TableCell>{job.timeAlive} days</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={handleChangePage}
            color="primary"
          />
        </Box>
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EmployerJobPage;
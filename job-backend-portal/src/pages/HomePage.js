"use client"

import { <PERSON> } from "react-router-dom"
import { ArrowRight, Users, Award, Globe, Code, Briefcase, Smartphone, CheckCircle } from "lucide-react"
import "./HomePage.css"

function HomePage({ openModal }) {
  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Lasting Software, Lasting Partnerships_</h1>
            <p className="hero-subtitle">
              Tekai Vietnam delivers high-quality software solutions with a focus on long-term partnerships and
              sustainable growth.
            </p>
            <div className="hero-buttons">
              <button className="btn primary-btn hero-btn" onClick={() => openModal()}>
                Let's talk <ArrowRight size={16} />
              </button>
              <Link to="/jobs" className="btn secondary-btn hero-btn">
                View open positions <ArrowRight size={16} />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="company-overview">
        <div className="container">
          <div className="section-header">
            <h2>About Tekai Vietnam_</h2>
            <p>
              Established in 2018, Tekai Vietnam is a dynamic software development company specializing in delivering
              high-quality solutions for businesses worldwide.
            </p>
          </div>

          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <Users />
              </div>
              <h3>50+</h3>
              <p>Talented Professionals</p>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Award />
              </div>
              <h3>5+</h3>
              <p>Years of Excellence</p>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Globe />
              </div>
              <h3>10+</h3>
              <p>Global Clients</p>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Code />
              </div>
              <h3>30+</h3>
              <p>Successful Projects</p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services-section">
        <div className="container">
          <div className="section-header">
            <h2>
              Our Services<span className="text-accent">_</span>
            </h2>
            <p>We provide comprehensive software development services tailored to your business needs.</p>
          </div>

          <div className="services-grid">
            <div className="service-card service-card-purple">
              <div className="service-icon">
                <Code size={24} />
              </div>
              <h3>Custom Software Development</h3>
              <p>
                Tailored solutions designed specifically for your business requirements, ensuring optimal performance
                and scalability.
              </p>
            </div>
            <div className="service-card service-card-blue">
              <div className="service-icon">
                <Globe size={24} />
              </div>
              <h3>Web Application Development</h3>
              <p>
                Modern, responsive web applications built with the latest technologies to provide exceptional user
                experiences.
              </p>
            </div>
            <div className="service-card service-card-green">
              <div className="service-icon">
                <Smartphone size={24} />
              </div>
              <h3>Mobile App Development</h3>
              <p>Native and cross-platform mobile applications that deliver seamless experiences across all devices.</p>
            </div>
            <div className="service-card service-card-orange">
              <div className="service-icon">
                <CheckCircle size={24} />
              </div>
              <h3>Quality Assurance & Testing</h3>
              <p>
                Comprehensive testing services to ensure your software meets the highest quality standards and performs
                flawlessly.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Jobs Section */}
      <section className="featured-jobs">
        <div className="container">
          <div className="section-header">
            <h2>Join Our Team_</h2>
            <p>Explore exciting career opportunities at Tekai Vietnam and be part of our growing team.</p>
          </div>

          <div className="jobs-preview">
            <div className="job-card">
              <div className="job-card-content">
                <span className="job-badge">Featured</span>
                <h3>Delivery Lead / Senior Automation Test</h3>
                <p>
                  Lead our testing efforts and ensure the quality of our software solutions. Design and implement
                  automated test frameworks.
                </p>
                <div className="job-meta">
                  <span>
                    <Briefcase size={16} /> Full-time
                  </span>
                  <span>Hanoi, Vietnam</span>
                </div>
              </div>
              <Link to="/jobs/delivery-lead" className="job-link">
                View Details <ArrowRight size={16} />
              </Link>
            </div>

            <div className="job-card">
              <div className="job-card-content">
                <h3>Senior Frontend Developer</h3>
                <p>
                  Create exceptional user interfaces and experiences using modern frontend technologies like React,
                  Angular, or Vue.
                </p>
                <div className="job-meta">
                  <span>
                    <Briefcase size={16} /> Full-time
                  </span>
                  <span>Hanoi, Vietnam</span>
                </div>
              </div>
              <Link to="/jobs" className="job-link">
                View Details <ArrowRight size={16} />
              </Link>
            </div>

            <div className="job-card">
              <div className="job-card-content">
                <h3>Backend Developer</h3>
                <p>
                  Develop robust and scalable backend systems using Node.js, Java, or .NET to power our client
                  applications.
                </p>
                <div className="job-meta">
                  <span>
                    <Briefcase size={16} /> Full-time
                  </span>
                  <span>Hanoi, Vietnam</span>
                </div>
              </div>
              <Link to="/jobs" className="job-link">
                View Details <ArrowRight size={16} />
              </Link>
            </div>
          </div>

          <div className="view-all-jobs">
            <Link to="/jobs" className="btn primary-btn">
              View all positions <ArrowRight size={16} />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to start your career with us?</h2>
            <p>Join our team of talented professionals and work on exciting projects in a collaborative environment.</p>
            <button className="btn primary-btn cta-btn" onClick={() => openModal()}>
              Get in touch <ArrowRight size={16} />
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}

export default HomePage

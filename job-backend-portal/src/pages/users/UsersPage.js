"use client"

import { useState, useEffect } from "react"
import { Link } from "react-router-dom"
import { Plus } from "lucide-react"
import { getAllUsers } from "../../services/userService"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Pagination,
  Button,
  Box,
  Typography,
  Switch,
  Chip
} from '@mui/material'

function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [appliedSearchTerm, setAppliedSearchTerm] = useState("")
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [rowsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(1)

  const formatDate = (date) => {
    return new Date(date).toISOString().split('T')[0];
  };

  const loadUsers = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage,
        search: searchTerm
      };

      const response = await getAllUsers(params);
      setUsers(response.items);
      setTotalPages(response.meta.totalPages);
    } catch (error) {
      console.error('Failed to load users:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, [page, rowsPerPage, appliedSearchTerm]);

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
            <Typography variant="h6">Loading...</Typography>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              User Management
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Plus />}
              component={Link}
              to="/users/create"
            >
              Add New User
            </Button>
          </Box>

          <Paper sx={{ p: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                label="Search Users"
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    setAppliedSearchTerm(searchTerm);
                  }
                }}
                sx={{ flexGrow: 1 }}
              />
              <Button
                onClick={() => setAppliedSearchTerm(searchTerm)}
                variant="contained"
                size="small">
                Search
              </Button>
            </Box>

            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Table sx={{ minWidth: 800 }}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Full Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Phone</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Role</TableCell>   
                    <TableCell sx={{ fontWeight: 'bold' }}>Birthday</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Created Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Link 
                          to={`/users/${user.id}/view`}
                          style={{ 
                            color: '#1976d2', 
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          {user.fullName}
                        </Link>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.phone}</TableCell>
                      <TableCell>
                        <Chip 
                          label={user.role} 
                          color={
                            user.role === 'Admin' ? 'primary' : 
                            user.role === 'Employer' ? 'success' :
                            user.role === 'User' ? 'warning' :
                            user.role === 'HR' ? 'secondary' :
                            user.role === 'Content Creator' ? 'info' : 'error'
                          } 
                        />
                      </TableCell>
                      <TableCell>{formatDate(user.birthday)}</TableCell>
                      <TableCell>
                        <Switch
                          checked={user.active}
                          disabled
                          color="primary"
                        />
                      </TableCell>
                      <TableCell>{formatDate(user.createdAt)}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            component={Link}
                            to={`/users/${user.id}`}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                          >
                            Delete
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handleChangePage}
                color="primary"
              />
            </Box>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default UsersPage
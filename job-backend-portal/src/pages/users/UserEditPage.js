import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import { getUserById, updateUser, createUser, updatePassword } from '../../services/userService';
import './UserEditPage.css';
import { Link } from 'react-router-dom';
const userRoles = ['Admin', 'Employer', 'User', 'Content Creator', 'HR'];

function UserEditPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewUser = id === 'create';
  const [loading, setLoading] = useState(!isNewUser);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState({ info: false, password: false });
  const [user, setUser] = useState({
    fullName: '',
    email: '',
    phone: '',
    birthday: '',
    role: '',
    active: true,
    password: '',
    confirmPassword: ''
  });

  useEffect(() => {
    if (!isNewUser) {
      const loadUser = async () => {
        try {
          const userData = await getUserById(id);
          setUser(prev => ({
            ...prev,
            ...userData,
            password: '',
            confirmPassword: ''
          }));
        } catch (err) {
          console.error('Error loading user:', err);
          setError('Failed to load user details');
        } finally {
          setLoading(false);
        }
      };

      loadUser();
    } else {
      setLoading(false);
    }
  }, [id, isNewUser]);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setUser(prevUser => ({
      ...prevUser,
      [name]: e.target.type === 'checkbox' ? checked : value
    }));
  };

  const validatePassword = () => {
    if (!user.password) {
      setError('New password is required');
      return false;
    }
    if (user.password !== user.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    if (user.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    return true;
  };

  const handleSaveInformation = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess({ info: false, password: false });

    try {
      const userData = {
        fullName: user.fullName,
        email: user.email,
        phone: user.phone,
        birthday: user.birthday,
        role: user.role,
        active: user.active
      };
      
      if (isNewUser) {
        if (!validatePassword()) return;
        await createUser({ ...userData, password: user.password });
        navigate('/users');
      } else {
        await updateUser(id, userData);
        setSuccess(prev => ({ ...prev, info: true }));
        setTimeout(() => {
          setSuccess(prev => ({ ...prev, info: false }));
        }, 3000);
      }
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to save user information');
      console.error('Error saving user information:', err);
    }
  };

  const handleChangePassword = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess({ info: false, password: false });

    if (!validatePassword()) return;

    try {
      await updatePassword(id, {
        newPassword: user.password
      });
      setSuccess(prev => ({ ...prev, password: true }));
      setUser(prev => ({ 
        ...prev, 
        password: '', 
        confirmPassword: ''
      }));
      setTimeout(() => {
        setSuccess(prev => ({ ...prev, password: false }));
      }, 3000);
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to change password');
      console.error('Error changing password:', err);
    }
  };

  if (loading) {
    return (
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '400px',
          flexDirection: 'column',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography variant="body1" color="text.secondary">
          Loading user details...
        </Typography>
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
              {isNewUser ? 'Create New User' : 'Edit User'}
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {/* User Information Section */}
            <form onSubmit={handleSaveInformation}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                User Information
              </Typography>
              {success.info && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  User information updated successfully!
                </Alert>
              )}

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    name="fullName"
                    value={user.fullName}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    name="email"
                    type="email"
                    value={user.email}
                    onChange={handleChange}
                    required
                    disabled={!isNewUser}
                    helperText={!isNewUser ? "Email cannot be changed" : ""}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone"
                    name="phone"
                    value={user.phone}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Birthday"
                    name="birthday"
                    type="date"
                    value={user.birthday || ''}
                    onChange={handleChange}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Role</InputLabel>
                    <Select
                      name="role"
                      value={user.role}
                      onChange={handleChange}
                      label="Role"
                      required
                    >
                      {userRoles.map((role) => (
                        <MenuItem key={role} value={role}>
                          {role}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {isNewUser && (
                  <Grid item xs={12}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Password"
                          name="password"
                          type="password"
                          value={user.password}
                          onChange={handleChange}
                          required
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Confirm Password"
                          name="confirmPassword"
                          type="password"
                          value={user.confirmPassword}
                          onChange={handleChange}
                          required
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={user.active}
                        onChange={handleChange}
                        name="active"
                        color="primary"
                      />
                    }
                    label="Active"
                  />
                </Grid>
              </Grid>

              {/* Information Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                <Button
                  variant="outlined"
                  component={Link}
                  to="/users"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                >
                  {isNewUser ? 'Create User' : 'Save Changes'}
                </Button>
              </Box>
            </form>

            {/* Password Section - Only for existing users */}
            {!isNewUser && (
              <>
                <Box sx={{ mt: 4, mb: 3 }}>
                  <Divider />
                </Box>
                
                <form onSubmit={handleChangePassword}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Change Password
                  </Typography>
                  {success.password && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      Password changed successfully!
                    </Alert>
                  )}

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="New Password"
                            name="password"
                            type="password"
                            value={user.password}
                            onChange={handleChange}
                            required
                          />
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Confirm New Password"
                            name="confirmPassword"
                            type="password"
                            value={user.confirmPassword}
                            onChange={handleChange}
                            required
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>

                  {/* Password Action Button */}
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                    >
                      Change Password
                    </Button>
                  </Box>
                </form>
              </>
            )}
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default UserEditPage; 
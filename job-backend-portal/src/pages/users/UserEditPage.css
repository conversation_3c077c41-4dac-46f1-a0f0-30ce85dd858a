.user-edit-page {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.edit-form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-header {
  margin-bottom: 2rem;
}

.form-header h1 {
  color: #2d3748;
  font-size: 1.875rem;
  font-weight: 600;
}

.form-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
}

.text-area-field {
  min-height: 120px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.cancel-button {
  background-color: transparent;
  color: #4a5568;
  border: 1px solid #cbd5e0;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #f7fafc;
  border-color: #a0aec0;
}

.save-button {
  background-color: #4f46e5;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.save-button:hover {
  background-color: #4338ca;
}

.alert {
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.alert-error {
  background-color: #fff5f5;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.alert-success {
  background-color: #f0fff4;
  color: #2f855a;
  border: 1px solid #9ae6b4;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .form-section {
    padding: 1.5rem;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .form-actions button {
    width: 100%;
  }
}

/* Material-UI Overrides */
.MuiTextField-root {
  background-color: white;
}

.MuiInputLabel-root {
  color: #4a5568;
}

.MuiOutlinedInput-root {
  border-radius: 0.375rem;
}

.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #4f46e5;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #4f46e5;
}

.MuiSelect-select {
  background-color: white;
} 
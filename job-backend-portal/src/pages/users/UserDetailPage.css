.user-detail-page {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.user-detail-page.cms-view {
  background-color: #f5f6fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.left-content {
  flex: 1;
}

.left-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
}

.user-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.meta-item svg {
  color: #4f46e5;
}

.right-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.secondary-btn {
  background: #4f46e5;
  color: white;
}

.secondary-btn:hover {
  background: #4338ca;
}

.page-layout {
  display: flex;
  gap: 2rem;
  padding: 0 2rem;
}

.content-area {
  flex: 1;
  min-width: 0;
}

.page-sidebar {
  width: 320px;
  position: sticky;
  top: 1.5rem;
  height: fit-content;
}

.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card h2 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item h3 {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.info-item p {
  color: #111827;
  font-size: 1rem;
}

.badge-container {
  display: inline-block;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.badge.green {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.badge.red {
  background-color: #ffebee;
  color: #c62828;
}

.stats-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.375rem;
}

.stat-item h4 {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.stat-item p {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
}

@media (max-width: 1024px) {
  .page-layout {
    flex-direction: column;
  }

  .page-sidebar {
    width: 100%;
    position: static;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 1rem;
}

.error {
  color: #dc2626;
  padding: 1rem;
  background: #fee2e2;
  border-radius: 0.375rem;
  margin: 1rem;
} 
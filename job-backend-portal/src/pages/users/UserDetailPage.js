"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom"
import { ArrowLeft, Mail, Phone, User, Edit } from "lucide-react"
import "./UserDetailPage.css"
import { getUserById } from "../../services/userService"
import { CircularProgress, Switch } from '@mui/material';

const UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

function UserDetailPage() {
  const { id } = useParams()
  const navigate = useNavigate();
  const [user, setUser] = useState({
    fullName: '',
    email: '',
    phone: '',
    birthday: '',
    active: '',
    createdAt: '',
    lastLogin: ''
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchUserDetail = async () => {
      try {
        const userData = await getUserById(id);
        setUser(userData);
      } catch (error) {
        setError('Failed to load user details');
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetail();
  }, [id])

  const handleEdit = () => {
    navigate(`/users/${id}`);
  }

  if (loading) return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="user-detail-page cms-view">
          <div className="loading-container" style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <CircularProgress />
            <p>Loading user details...</p>
          </div>
        </div>
      </div>
    </div>
  );

  if (error) return <div className="error">{error}</div>
  if (!user) return <div>User not found</div>

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="user-detail-page cms-view">
          <div className="page-header">
            <div className="header-content">
              <div className="left-content">
                <Link to="/users" className="back-link">
                  <ArrowLeft size={16} /> Back to Users List
                </Link>
                <h1>{user.fullName} </h1>
                <div className="user-meta">
                  <div className="meta-item">
                    <Mail size={16} />
                    <span>{user.email}</span>
                  </div>
                  <div className="meta-item">
                    <Phone size={16} />
                    <span>{user.phone}</span>
                  </div>
                  <div className="meta-item">
                  <Switch
                        checked={user.active}
                        disabled
                        color="primary"
                      />
                  </div>
                  <div className="meta-item">
                    <User size={16} />
                    <span>{user.role}</span>
                  </div>
                </div>
              </div>
              <div className="right-content">
                <div className="action-buttons">
                  <button className="btn secondary-btn" onClick={handleEdit}>
                    <Edit size={16} /> Edit User
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="page-layout">
            <div className="content-area">
              <div className="container">
                <div className="card">
                  <h2>User Information</h2>
                  <div className="info-grid">
                    <div className="info-item">
                      <h3>Full Name</h3>
                      <p>{user.fullName}</p>
                    </div>
                    <div className="info-item">
                      <h3>Email</h3>
                      <p>{user.email}</p>
                    </div>
                    <div className="info-item">
                      <h3>Phone</h3>
                      <p>{user.phone}</p>
                    </div>
                    
                    <div className="info-item">
                      <h3>Status</h3>
                      <div className="badge-container">
                        <span className={`badge ${user.status === UserStatus.ACTIVE ? 'green' : 'red'}`}>
                          {user.status || UserStatus.ACTIVE}
                        </span>
                      </div>
                    </div>
                    <div className="info-item">
                      <h3>Last Login</h3>
                      <div className="badge-container">
                        <span>
                        {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="card">
                  <h2>Roles</h2>
                  <div className="info-grid">
                    <div className="info-item">
                      <h3>Role</h3>
                      <p>{user.role}</p>
                    </div>
                  </div>
                  </div>
              </div>
            </div>  
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserDetailPage 
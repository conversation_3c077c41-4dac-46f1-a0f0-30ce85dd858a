"use client"

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Chip,
  CircularProgress,
  Divider
} from '@mui/material';
import { ArrowLeft, Edit, Mail, Phone, Calendar, MessageSquare } from 'lucide-react';
import { getContactById } from '../../services/contactService';
import { ContactStatus } from '../../types/contact';

function ContactDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [contact, setContact] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadContact = async () => {
      try {
        const data = await getContactById(id);
        setContact(data);
      } catch (error) {
        setError('Failed to load contact');
        console.error('Error loading contact:', error);
      } finally {
        setLoading(false);
      }
    };
    loadContact();
  }, [id]);

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case ContactStatus.DONE:
        return 'success';
      case ContactStatus.NEW:
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            p: 3 
          }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  if (error || !contact) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ p: 3 }}>
            <Typography color="error">
              {error || 'Contact not found'}
            </Typography>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Button
              startIcon={<ArrowLeft />}
              onClick={() => navigate('/contacts')}
              sx={{ mr: 2 }}
            >
              Back to Contacts
            </Button>
            <Typography variant="h5" component="h1" sx={{ flex: 1 }}>
              Contact Details
            </Typography>
            <Button
              variant="contained"
              startIcon={<Edit />}
              onClick={() => navigate(`/contacts/${id}`)}
            >
              Edit Contact
            </Button>
          </Box>

          <Paper sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    {contact.fullname}
                  </Typography>
                  <Chip
                    label={contact.status}
                    color={getStatusColor(contact.status)}
                    size="small"
                  />
                </Box>
                <Divider sx={{ my: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Mail size={20} style={{ marginRight: '8px' }} />
                  <Typography>
                    <strong>Email:</strong> {contact.email}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Phone size={20} style={{ marginRight: '8px' }} />
                  <Typography>
                    <strong>Phone:</strong> {contact.phoneNumber}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Calendar size={20} style={{ marginRight: '8px' }} />
                  <Typography>
                    <strong>Created At:</strong> {formatDate(contact.createdAt)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <MessageSquare size={20} style={{ marginRight: '8px', marginTop: '4px' }} />
                    <Typography variant="h6">Message Content</Typography>
                  </Box>
                  <Typography sx={{ 
                    whiteSpace: 'pre-wrap',
                    backgroundColor: '#f5f5f5',
                    p: 2,
                    borderRadius: 1
                  }}>
                    {contact.content}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default ContactDetailPage; 
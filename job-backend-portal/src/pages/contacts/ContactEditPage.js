"use client"

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material';
import { getContactById, updateContact, createContact } from '../../services/contactService';
import { ContactStatus } from '../../types/contact';

function ContactEditPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewContact = !id;
  const [loading, setLoading] = useState(!isNewContact);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  
  const [contact, setContact] = useState({
    fullname: "",
    phoneNumber: "",
    email: "",
    content: "",
    status: ContactStatus.NEW
  });

  useEffect(() => {
    if (!isNewContact) {
      const loadContact = async () => {
        try {
          const data = await getContactById(id);
          setContact(data);
        } catch (error) {
          setError('Failed to load contact');
          console.error('Error loading contact:', error);
        } finally {
          setLoading(false);
        }
      };
      loadContact();
    }
  }, [id, isNewContact]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setContact(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);
    
    try {
      if (isNewContact) {
        await createContact(contact);
      } else {
        await updateContact(id, contact);
      }
      setSuccess(true);
      setTimeout(() => {
        navigate('/contacts');
      }, 1500);
    } catch (error) {
      setError('Failed to save contact');
      console.error('Error saving contact:', error);
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            p: 3 
          }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
            {isNewContact ? 'Create New Contact' : 'Edit Contact'}
          </Typography>

          <Paper sx={{ p: 3 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Contact saved successfully!
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    name="fullname"
                    label="Full Name"
                    value={contact.fullname}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    name="email"
                    label="Email"
                    type="email"
                    value={contact.email}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    name="phoneNumber"
                    label="Phone Number"
                    value={contact.phoneNumber}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    name="content"
                    label="Content"
                    value={contact.content}
                    onChange={handleChange}
                    multiline
                    rows={4}
                    fullWidth
                    required
                  />
                </Grid>

                {!isNewContact && (
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={contact.status}
                        onChange={handleChange}
                        label="Status"
                      >
                        <MenuItem value={ContactStatus.NEW}>New</MenuItem>
                        <MenuItem value={ContactStatus.DONE}>Done</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate('/contacts')}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                    >
                      {isNewContact ? 'Create Contact' : 'Update Contact'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default ContactEditPage; 
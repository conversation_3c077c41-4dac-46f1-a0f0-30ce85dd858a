"use client"

import { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { Plus, Edit, Trash2 } from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  Pagination,
  Button,
  Box,
  Typography,
  Chip,
  Tooltip,
  IconButton
} from '@mui/material'
import { ContactStatus } from "../../types/contact"
import { getAllContacts, deleteContact } from "../../services/contactService"

function ContactsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("ALL")
  const [contacts, setContacts] = useState([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [rowsPerPage] = useState(10)

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  useEffect(() => {
    const loadContacts = async () => {
      try {
        const contactsData = await getAllContacts();
        setContacts(contactsData);
      } catch (error) {
        console.error('Failed to load contacts:', error);
      } finally {
        setLoading(false);
      }
    };
    loadContacts();
  }, []);

  const filteredContacts = contacts.filter((contact) => {
    const matchesSearch = 
      contact.fullname.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.phoneNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "ALL" || contact.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Pagination
  const indexOfLastContact = page * rowsPerPage;
  const indexOfFirstContact = indexOfLastContact - rowsPerPage;
  const currentContacts = filteredContacts.slice(indexOfFirstContact, indexOfLastContact);
  const pageCount = Math.ceil(filteredContacts.length / rowsPerPage);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleDeleteContact = async (id) => {
    if (window.confirm('Are you sure you want to delete this contact?')) {
      try {
        await deleteContact(id);
        setContacts(contacts.filter(contact => contact.id !== id));
      } catch (error) {
        console.error('Failed to delete contact:', error);
      }
    }
  };

  const getStatusChipColor = (status) => {
    switch (status) {
      case ContactStatus.DONE:
        return 'success';
      case ContactStatus.NEW:
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            p: 3 
          }}>
            <Typography variant="h6">Loading...</Typography>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Contacts Management
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Plus />}
              component={Link}
              to="/contacts/create"
            >
              Add New Contact
            </Button>
          </Box>

          <Paper sx={{ 
            width: '100%', 
            mb: 2,
            overflowX: 'auto'
          }}>
            <Box sx={{ display: 'flex', gap: 2, p: 2 }}>
              <TextField
                label="Search Contacts"
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                sx={{ flexGrow: 1 }}
              />
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                size="small"
                sx={{ minWidth: 200 }}
              >
                <MenuItem value="ALL">All Status</MenuItem>
                <MenuItem value={ContactStatus.NEW}>New</MenuItem>
                <MenuItem value={ContactStatus.DONE}>Done</MenuItem>
              </Select>
            </Box>

            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Full Name</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Phone Number</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Created Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentContacts.map((contact) => (
                  <TableRow key={contact.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            component={Link}
                            to={`/contacts/${contact.id}`}
                          >
                            <Edit size={16} />
                          </IconButton>
                        </Tooltip>
                        {contact.status !== ContactStatus.DONE && (
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteContact(contact.id)}
                            >
                              <Trash2 size={16} />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Link
                        to={`/contacts/${contact.id}/view`}
                        style={{
                          color: '#1976d2',
                          textDecoration: 'none',
                          '&:hover': {
                            textDecoration: 'underline'
                          }
                        }}
                      >
                        {contact.fullname}
                      </Link>
                    </TableCell>
                    <TableCell>{contact.email}</TableCell>
                    <TableCell>{contact.phoneNumber}</TableCell>
                    <TableCell>
                      <Chip
                        label={contact.status}
                        color={getStatusChipColor(contact.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{formatDate(contact.createdAt)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <Pagination
                count={pageCount}
                page={page}
                onChange={handleChangePage}
                color="primary"
              />
            </Box>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default ContactsPage; 
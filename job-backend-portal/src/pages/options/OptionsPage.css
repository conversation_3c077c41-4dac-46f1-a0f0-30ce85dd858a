.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f5f6fa;
}

.admin-content {
  flex: 1;
  padding: 1rem;
}

.options-table-container {
  overflow-x: auto;
}

.options-table {
  width: 100%;
  border-collapse: collapse;
}

.options-table th,
.options-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.options-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.options-table tr:hover {
  background-color: #f9f9f9;
}

.options-actions {
  display: flex;
  gap: 8px;
}

.search-filter-container {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.search-input {
  flex: 1;
}

.filter-select {
  min-width: 200px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.status-active {
  color: #4caf50;
}

.status-inactive {
  color: #f44336;
}

.option-link {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
}

.option-link:hover {
  text-decoration: underline;
}

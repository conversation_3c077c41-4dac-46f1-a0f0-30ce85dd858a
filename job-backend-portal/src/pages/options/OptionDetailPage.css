.option-detail-page {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.option-detail-page.cms-view {
  background-color: #f5f6fa;
  min-height: 100vh;
  padding: 1rem;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.left-content {
  flex: 1;
}

.left-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
}

.option-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.meta-item svg {
  color: #4f46e5;
}

.right-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
}

.back-link:hover {
  text-decoration: underline;
}

.detail-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.detail-section-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.detail-section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.detail-section-content {
  padding: 1.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.detail-value {
  font-size: 1rem;
  color: #111827;
}

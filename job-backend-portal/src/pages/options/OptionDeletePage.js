import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import { getOptionById, deleteOption } from '../../services/optionService';
import { AlertTriangle } from 'lucide-react';
import './OptionDeletePage.css';

function OptionDeletePage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [option, setOption] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  useEffect(() => {
    const fetchOption = async () => {
      try {
        const data = await getOptionById(id);
        setOption(data);
      } catch (err) {
        setError('Failed to load option details');
        console.error('Error loading option:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOption();
  }, [id]);

  const handleOpenConfirm = () => {
    setConfirmOpen(true);
  };

  const handleCloseConfirm = () => {
    setConfirmOpen(false);
  };

  const handleDelete = async () => {
    setDeleteLoading(true);
    try {
      await deleteOption(id);
      navigate('/options', { state: { message: 'Option deleted successfully' } });
    } catch (err) {
      setError('Failed to delete option');
      console.error('Error deleting option:', err);
      setDeleteLoading(false);
      handleCloseConfirm();
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          component={Link}
          to="/options"
          variant="outlined"
          sx={{ mt: 2 }}
        >
          Back to Options List
        </Button>
      </Box>
    );
  }

  if (!option) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Option not found</Alert>
        <Button
          component={Link}
          to="/options"
          variant="outlined"
          sx={{ mt: 2 }}
        >
          Back to Options List
        </Button>
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
            Delete Option
          </Typography>

          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <AlertTriangle color="#f44336" size={24} style={{ marginRight: '12px' }} />
              <Typography variant="h6">
                Are you sure you want to delete this option?
              </Typography>
            </Box>

            <Typography variant="body1" sx={{ mb: 3 }}>
              This action will mark the option as inactive. The option will no longer be visible in the system.
            </Typography>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                Option Details:
              </Typography>
              <Typography><strong>Option Name:</strong> {option.optionName}</Typography>
              <Typography><strong>Option Title:</strong> {option.optionTitle}</Typography>
              <Typography><strong>Option Code:</strong> {option.optionCode}</Typography>
              <Typography><strong>Index:</strong> {option.index}</Typography>
              <Typography><strong>Status:</strong> {option.active ? 'Active' : 'Inactive'}</Typography>
              <Typography><strong>Created At:</strong> {new Date(option.createdAt).toLocaleString()}</Typography>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                component={Link}
                to="/options"
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="error"
                onClick={handleOpenConfirm}
              >
                Delete Option
              </Button>
            </Box>
          </Paper>
        </Box>

        <Dialog
          open={confirmOpen}
          onClose={handleCloseConfirm}
        >
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you absolutely sure you want to delete the option "{option.optionTitle}"?
              This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseConfirm} disabled={deleteLoading}>
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              color="error"
              variant="contained"
              disabled={deleteLoading}
            >
              {deleteLoading ? <CircularProgress size={24} /> : 'Delete'}
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </div>
  );
}

export default OptionDeletePage;

.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f5f6fa;
}

.admin-content {
  flex: 1;
  padding: 1rem;
}

.option-edit-page {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.edit-form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-header {
  margin-bottom: 2rem;
}

.form-header h1 {
  color: #2d3748;
  font-size: 1.875rem;
  font-weight: 600;
}

.form-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* Drag and drop styles */
.draggable-row {
  transition: background-color 0.2s ease;
}

.draggable-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.draggable-row.dragging {
  background-color: rgba(25, 118, 210, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.draggable-row.current-option {
  background-color: rgba(25, 118, 210, 0.04);
  border-left: 3px solid #1976d2;
}

.draggable-row.current-option:hover {
  background-color: rgba(25, 118, 210, 0.08);
}

.drag-handle {
  cursor: grab;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease, color 0.2s ease;
}

.drag-handle:hover {
  color: #1976d2;
  transform: scale(1.2);
}

.drag-handle-disabled {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #bdbdbd;
  cursor: not-allowed;
}

.draggable-row.current-option .drag-handle {
  color: #1976d2;
}

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft, Edit, Tag, Code, Hash, ToggleLeft } from "lucide-react";
import "./OptionDetailPage.css";
import { getOptionById } from "../../services/optionService";
import { CircularProgress, Switch, Button, Alert, Box, Paper, Typography } from '@mui/material';

function OptionDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [option, setOption] = useState({
    optionName: '',
    optionTitle: '',
    optionCode: '',
    index: 0,
    active: true
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOptionDetail = async () => {
      try {
        const optionData = await getOptionById(id);
        setOption(optionData);
      } catch (error) {
        setError('Failed to load option details');
      } finally {
        setLoading(false);
      }
    };

    fetchOptionDetail();
  }, [id]);

  const handleEdit = () => {
    navigate(`/options/${id}`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          component={Link}
          to="/options"
          variant="outlined"
          sx={{ mt: 2 }}
        >
          Back to Options List
        </Button>
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="option-detail-page cms-view">
          <Paper sx={{ mb: 3 }}>
            <Box sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Button
                  component={Link}
                  to="/options"
                  startIcon={<ArrowLeft size={16} />}
                  sx={{ mr: 2 }}
                >
                  Back to Options List
                </Button>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
                    {option.optionTitle}
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Tag size={18} />
                      <Typography variant="body1">
                        <strong>Option Name:</strong> {option.optionName}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Code size={18} />
                      <Typography variant="body1">
                        <strong>Option Code:</strong> {option.optionCode}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Hash size={18} />
                      <Typography variant="body1">
                        <strong>Index:</strong> {option.index}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ToggleLeft size={18} />
                      <Typography variant="body1">
                        <strong>Status:</strong>
                        <Switch
                          checked={option.active}
                          disabled
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<Edit size={16} />}
                  onClick={handleEdit}
                >
                  Edit Option
                </Button>
              </Box>
            </Box>
          </Paper>

          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" component="h2" sx={{ mb: 2 }}>
                Option Details
              </Typography>

              <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 3 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Option Name</Typography>
                  <Typography variant="body1">{option.optionName}</Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">Option Title</Typography>
                  <Typography variant="body1">{option.optionTitle}</Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">Option Code</Typography>
                  <Typography variant="body1">{option.optionCode}</Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">Index</Typography>
                  <Typography variant="body1">{option.index}</Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">Status</Typography>
                  <Typography variant="body1">{option.active ? 'Active' : 'Inactive'}</Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">Created At</Typography>
                  <Typography variant="body1">{new Date(option.createdAt).toLocaleString()}</Typography>
                </Box>
              </Box>
            </Box>
          </Paper>
        </div>
      </div>
    </div>
  );
}

export default OptionDetailPage;

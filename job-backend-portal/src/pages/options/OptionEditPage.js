import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  AlertTitle,
  CircularProgress,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Tooltip
} from '@mui/material';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DragIndicator, Save as SaveIcon } from '@mui/icons-material';
import { getOptionById, createOption, updateOption, getOptions, updateOptionsIndexes } from '../../services/optionService';
import './OptionEditPage.css';

const OPTION_TYPES = [
  'JOB_TYPES',
  'JOB_CATEGORIES',
  'JOB_LOCATIONS',
  'JOB_SALARY_RANGES',
  'JOB_WORKING_HOURS'
];

function SortableItem({ item, index }) {
  const { id } = useParams();
  const isNewOption = id === 'create';
  const isCurrentOption = item.id === parseInt(id);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    backgroundColor: isDragging
      ? 'rgba(25, 118, 210, 0.08)'
      : isCurrentOption
        ? 'rgba(25, 118, 210, 0.04)'
        : undefined,
    '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
  };

  return (
    <TableRow
      ref={setNodeRef}
      style={style}
      className={
        isDragging
          ? 'draggable-row dragging'
          : isCurrentOption
            ? 'draggable-row current-option'
            : 'draggable-row'
      }
    >
      <TableCell sx={{ width: '40px' }}>
        {isNewOption ? (
          <Tooltip title="Cannot reorder in create mode">
            <DragIndicator
              className="drag-handle-disabled"
              color="disabled"
              sx={{ fontSize: '1.5rem' }}
            />
          </Tooltip>
        ) : (
          <div {...attributes} {...listeners} style={{ cursor: 'grab' }}>
            <Tooltip title="Drag to reorder">
              <DragIndicator
                className="drag-handle"
                color={isCurrentOption ? "primary" : "action"}
                sx={{ fontSize: '1.5rem' }}
              />
            </Tooltip>
          </div>
        )}
      </TableCell>
      <TableCell>
        {isCurrentOption ? <strong>{item.optionTitle}</strong> : item.optionTitle}
      </TableCell>
      <TableCell>{item.optionCode}</TableCell>
      <TableCell>
        <Chip
          label={`Index: ${index + 1}`}
          color={isCurrentOption ? "success" : "primary"}
          size="small"
          variant={isCurrentOption ? "filled" : "outlined"}
          sx={{ fontWeight: 'bold' }}
        />
      </TableCell>
      <TableCell>
        {item.active ? (
          <Typography variant="body2" sx={{ color: 'success.main' }}>
            Active
          </Typography>
        ) : (
          <Typography variant="body2" sx={{ color: 'error.main' }}>
            Inactive
          </Typography>
        )}
      </TableCell>
    </TableRow>
  );
}

function OptionEditPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewOption = id === 'create';
  const [loading, setLoading] = useState(!isNewOption);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [option, setOption] = useState({
    optionName: '',
    optionTitle: '',
    optionCode: '',
    index: 0,
    active: true
  });
  const [relatedOptions, setRelatedOptions] = useState([]);
  const [loadingRelated, setLoadingRelated] = useState(false);
  const [indexesChanged, setIndexesChanged] = useState(false);
  const [optionFetched, setOptionFetched] = useState(false);

  const fetchRelatedOptions = useCallback(async (optionName) => {
    if (loadingRelated) {
      return;
    }
    setLoadingRelated(true);
    try {
      const params = {
        page: -1,
        limit: -1,
        optionNames: optionName
      };
      const res = await getOptions(params);

      if (!res?.items || !Array.isArray(res?.items)) {
        console.error('Invalid options data received:', res);
        setRelatedOptions([]);
        return;
      }

      // Update the related options
      setRelatedOptions(res.items);

      // Update the current option's index in the form if it's in the list
      if (!isNewOption) {
        const currentOption = res.items.find(item => item.id === parseInt(id));
        if (currentOption) {
          setOption(prev => ({ ...prev, index: currentOption.index }));
        }
      }
    } catch (err) {
      console.error('Error loading related options:', err);
      setRelatedOptions([]);
    } finally {
      setLoadingRelated(false);
    }
  }, [id, isNewOption, loadingRelated]);

  useEffect(() => {
    const fetchOption = async () => {
      if (isNewOption || (optionFetched && id)) {
        setLoading(false);
        return;
      }

      try {
        const data = await getOptionById(id);
        setOption(data);
        setOptionFetched(true);

        if (data.optionName) {
          await fetchRelatedOptions(data.optionName);
        }
      } catch (err) {
        setError('Failed to load option details');
        console.error('Error loading option:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOption();
  }, [id, isNewOption, optionFetched, fetchRelatedOptions]);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;

    if (name === 'active') {
      setOption(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'optionName') {
      setOption(prev => ({ ...prev, [name]: value }));

      // Fetch related options for both new and existing options
      if (value) {
        fetchRelatedOptions(value);
      }
    } else {
      setOption(prev => ({ ...prev, [name]: value }));
    }
  };

  // Setup sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;
    // Return if no over (destination) or same position
    if (!over || active.id === over.id) {
      return;
    }

    try {
      // Find the indexes of the dragged item and the drop target
      const activeIndex = relatedOptions.findIndex(item => item.id === active.id);
      const overIndex = relatedOptions.findIndex(item => item.id === over.id);

      if (activeIndex === -1 || overIndex === -1) {
        console.error('Invalid indexes:', { activeIndex, overIndex });
        return;
      }

      // Create a new array with the updated order
      const newItems = arrayMove(relatedOptions, activeIndex, overIndex);

      // Update indexes based on new order
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index: index + 1  // Start indexes from 1
      }));

      setRelatedOptions(updatedItems);
      setIndexesChanged(true);

      // Update the current option's index in the form if it's in the list
      if (!isNewOption) {
        const currentOption = updatedItems.find(item => item.id === parseInt(id));
        if (currentOption) {
          setOption(prev => ({ ...prev, index: currentOption.index }));
        }
      }
    } catch (error) {
      console.error('Error in drag and drop:', error);
    }
  };

  const handleSaveIndexes = async () => {
    try {
      await updateOptionsIndexes(relatedOptions);

      setIndexesChanged(false);
      setSuccessMessage('Option order updated successfully!');
      setSuccess(true);
      setTimeout(() => {
        setSuccess(false);
      }, 3000);

    } catch (err) {
      setError('Failed to update option indexes: ' + (err.message || 'Unknown error'));
      console.error('Error updating option indexes:', err);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    try {
      if (isNewOption) {
        // For new options, calculate the next index based on related options
        let newOption = { ...option };

        // If we have related options of the same type, set the next index
        if (relatedOptions.length > 0) {
          // Find the highest index
          const highestIndex = Math.max(...relatedOptions.map(opt => opt.index));
          // Set the next index
          newOption.index = highestIndex + 1;
        } else {
          // If no related options, start with index 1
          newOption.index = 1;
        }

        // Create the new option with the calculated index
        const createdOption = await createOption(newOption);

        if (createdOption && createdOption.id) {
          setSuccessMessage('Option created successfully!');
          setSuccess(true);

          // Redirect to the view page for the new option
          setTimeout(() => {
            navigate(`/options/${createdOption.id}/view`);
          }, 1000);
        } else {
          throw new Error('Failed to get created option ID');
        }
      } else {
        if (indexesChanged) {
          await updateOption(id, option);
          await updateOptionsIndexes(relatedOptions);

          setIndexesChanged(false);
          setSuccessMessage('Option and order updated successfully!');
        } else {
          // Just update the option
          await updateOption(id, option);
          setSuccessMessage('Option updated successfully!');
        }

        setSuccess(true);
        setTimeout(() => {
          setSuccess(false);
        }, 3000);
      }
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to save option');
      console.error('Error saving option:', err);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
            {isNewOption ? 'Create New Option' : 'Edit Option'}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {successMessage || 'Operation completed successfully!'}
            </Alert>
          )}

          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Option Name</InputLabel>
                    <Select
                      name="optionName"
                      value={option.optionName}
                      onChange={handleChange}
                      label="Option Name"
                      required
                    >
                      {OPTION_TYPES.map((type) => (
                        <MenuItem key={type} value={type}>
                          {type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Option Title"
                    name="optionTitle"
                    value={option.optionTitle}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Option Code"
                    name="optionCode"
                    value={option.optionCode}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={option.active}
                        onChange={handleChange}
                        name="active"
                        color="primary"
                      />
                    }
                    label="Active"
                  />
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                <Button
                  variant="outlined"
                  component={Link}
                  to="/options"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                >
                  {isNewOption ? 'Create Option' : 'Save Changes'}
                </Button>
              </Box>
            </form>
          </Paper>

          {option.optionName && (
            <Paper sx={{ p: 3, mt: 3 }}>
              <Typography variant="h6" component="h2" sx={{ mb: 2 }}>
                {isNewOption ? 'Existing Options of Same Type' : 'Related Options'}
              </Typography>
              <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ color: 'info.contrastText' }}>
                  {isNewOption ? (
                    <>
                      <strong>Note:</strong> When you save, your new option will automatically get the next index value ({Math.max(...relatedOptions.map(opt => opt.index)) + 1}).
                    </>
                  ) : (
                    <>
                      <strong>Note:</strong> Drag and drop items to reorder them. Click "Save New Order" to save changes.
                    </>
                  )}
                </Typography>
              </Box>

              {loadingRelated ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress size={24} />
                </Box>
              ) : relatedOptions.length > 0 ? (
                <>
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                    modifiers={isNewOption ? [() => ({ x: 0, y: 0 })] : []}
                  >
                    <TableContainer component={Paper} elevation={0} variant="outlined">
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell width="10%">#</TableCell>
                            <TableCell width="30%">Title</TableCell>
                            <TableCell width="25%">Code</TableCell>
                            <TableCell width="20%">Index</TableCell>
                            <TableCell width="15%">Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          <SortableContext
                            items={relatedOptions.map(item => item.id)}
                            strategy={verticalListSortingStrategy}
                          >
                            {relatedOptions.map((item, index) => (
                              <SortableItem
                                key={item.id}
                                item={item}
                                index={index}
                              />
                            ))}
                          </SortableContext>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </DndContext>

                  {!isNewOption && (
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
                      <Button
                        variant="contained"
                        color="success"
                        onClick={handleSaveIndexes}
                        disabled={!indexesChanged}
                        startIcon={<SaveIcon />}
                        size="large"
                      >
                        Save New Order
                      </Button>
                    </Box>
                  )}
                </>
              ) : (
                <>
                  {isNewOption && (
                    <Alert severity="info" sx={{ mb: 3 }}>
                      <AlertTitle>Next Index</AlertTitle>
                      Your new option will be assigned index: <strong>1</strong> (first option of this type)
                    </Alert>
                  )}
                  <Typography variant="body1" sx={{ textAlign: 'center', py: 3 }}>
                    No existing options found for this type.
                  </Typography>
                </>
              )}
            </Paper>
          )}
        </Box>
      </div>
    </div>
  );
}

export default OptionEditPage;

/* Hero Section */
.hero-section {
  padding: 100px 0 80px;
  text-align: center;
  position: relative;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 30px;
  font-weight: 500;
}

.secondary-btn {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.secondary-btn:hover {
  background-color: var(--color-border);
}

/* Company Overview */
.company-overview {
  padding: 80px 0;
  background-color: var(--color-accent-light);
}

.section-header {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--color-text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-card {
  background-color: var(--color-card-bg);
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background-color: var(--color-accent-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.stat-icon svg {
  color: var(--color-accent);
}

.stat-card h3 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--color-accent);
}

.stat-card p {
  color: var(--color-text-secondary);
}

/* Services Section */
.services-section {
  padding: 80px 0;
  background-color: var(--color-background);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

.service-card {
  padding: 2.5rem;
  border-radius: 12px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-card h3 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  color: white;
  position: relative;
  z-index: 2;
}

.service-card p {
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 2;
}

.service-icon {
  background-color: rgba(255, 255, 255, 0.2);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.2), transparent 70%);
  z-index: 1;
}

.service-card-purple {
  background: linear-gradient(135deg, #8a7fff, #6a4fff);
}

.service-card-blue {
  background: linear-gradient(135deg, #4f9cf9, #2a7de1);
}

.service-card-green {
  background: linear-gradient(135deg, #34a853, #2d8a46);
}

.service-card-orange {
  background: linear-gradient(135deg, #ff7a59, #e25c3d);
}

/* Featured Jobs Section */
.featured-jobs {
  padding: 80px 0;
  background-color: var(--color-accent-light);
}

.jobs-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.job-card {
  background-color: var(--color-card-bg);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid var(--color-border);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.job-card-content {
  padding: 2rem;
  flex-grow: 1;
}

.job-badge {
  display: inline-block;
  background-color: var(--color-accent);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.job-card h3 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.job-card p {
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
}

.job-meta {
  display: flex;
  gap: 1rem;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  flex-wrap: wrap;
}

.job-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.job-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background-color: var(--color-accent-light);
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  border-top: 1px solid var(--color-border);
  transition: background-color 0.3s ease;
}

.job-link:hover {
  background-color: var(--color-accent);
  color: white;
}

.view-all-jobs {
  text-align: center;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  text-align: center;
  background-color: var(--color-card-bg);
  border-top: 1px solid var(--color-border);
  border-bottom: 1px solid var(--color-border);
}

.cta-content {
  max-width: 700px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta-content p {
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
  font-size: 1.1rem;
}

.cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 30px;
  font-weight: 500;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (min-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header h2 {
    font-size: 2.5rem;
  }
}

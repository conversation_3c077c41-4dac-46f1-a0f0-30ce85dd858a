import React from 'react';
import { CircularProgress, Typography, Button, TextField, Select, MenuItem, FormControl, InputAdornment, Checkbox, FormControlLabel } from '@mui/material';
import { Plus, Calendar, Users, CheckCircle, XCircle, Edit, Mail } from 'lucide-react';
import { RoleBasedButton } from '../../../utils/roleUtils';
import { validateEmail } from '../../../utils/validationUtils';
import { useUser } from '../../../contexts/UserContext';

const isScheduleTimeValid = (scheduleRound1, scheduleRound2) => {
  if (!scheduleRound1 || !scheduleRound2) return true;

  const round1Time = new Date(scheduleRound1).getTime();
  const round2Time = new Date(scheduleRound2).getTime();
  return round2Time > round1Time;
};

const InterviewSection = ({
  interview,
  interviewLoading,
  interviewError,
  application,
  formatDate,
  formatDateForInput,
  handleCreateInterview,
  showRound1Modal,
  setShowRound1Modal,
  showRound2Modal,
  setShowRound2Modal,
  round1Data,
  setRound1Data,
  round2Data,
  setRound2Data,
  handleUpdateRound1,
  handleUpdateRound2,
  handleOpenRound1Modal,
  handleOpenRound2Modal,
  handleCreateNewRound2
}) => {
  const { user } = useUser();
  const isAdmin = user?.role?.toLowerCase() === 'admin';

  return (
    <>
      <div className="card">
        <div className="card-header-with-actions">
          <h2>Interview Process</h2>
          <div className="card-actions">
            {!interview && (
              <RoleBasedButton
                allowedRoles={['admin', 'hr']}
                buttonProps={{
                  className: "btn secondary-btn",
                  onClick: handleCreateInterview
                }}
              >
                <Plus size={16} /> Create Interview Process
              </RoleBasedButton>
            )}
          </div>
        </div>

        {interviewError && (
          <div className="error-message">{interviewError}</div>
        )}

        {interviewLoading ? (
          <CircularProgress size={24} />
        ) : interview ? (
          <div className="interview-timeline">
            {/* Round 1 */}
            <div className="interview-round">
              <div className="round-header">
                <h3>Round 1</h3>

                  <RoleBasedButton
                    allowedRoles={['admin', 'hr']}
                    buttonProps={{
                      variant: "outlined",
                      size: "small",
                      color: "primary",
                      onClick: () => handleOpenRound1Modal()
                    }}
                  >
                    <Edit size={14} /> {interview.scheduleRound1 ? 'Edit' : 'Add'} Details
                  </RoleBasedButton>
              </div>
              <div className="info-grid">
                <div className="info-item">
                  <h4>Status</h4>
                  <div className={`status-badge ${interview.resultRound1 === true ? 'passed' : interview.resultRound1 === false ? 'failed' : 'pending'}`}>
                    {interview.resultRound1 === true ? (
                      <><CheckCircle size={16} /> Passed</>
                    ) : interview.resultRound1 === false ? (
                      <><XCircle size={16} /> Failed</>
                    ) : (
                      'Pending'
                    )}
                  </div>
                </div>
                <div className="info-item">
                  <h4>Schedule</h4>
                  <div className="interview-date">
                    <Calendar size={16} />
                    <span>{interview.scheduleRound1 ? formatDate(interview.scheduleRound1) : 'Not scheduled'}</span>
                  </div>
                </div>
                <div className="info-item">
                  <h4>Interviewer</h4>
                  <div className="interviewer">
                    <Users size={16} />
                    <span>{interview.interviewerRound1 || 'Not assigned'}</span>
                  </div>
                </div>
              </div>
              {interview.contentRound1 && (
                <div className="interview-details">
                  <h4>Interview Content</h4>
                  <Typography variant="body2">{interview.contentRound1}</Typography>
                </div>
              )}
              {interview.reviewRound1 && (
                <div className="interview-details">
                  <h4>Review Notes</h4>
                  <Typography variant="body2">{interview.reviewRound1}</Typography>
                </div>
              )}
            </div>

            {/* Round 2 */}
            {(interview.resultRound1 === true || interview.scheduleRound2) ? (
              <div className="interview-round">
                <div className="round-header">
                  <h3>Round 2</h3>
                    <RoleBasedButton
                      allowedRoles={['admin', 'hr']}
                      buttonProps={{
                        variant: "outlined",
                        size: "small",
                        color: "primary",
                        onClick: () => handleOpenRound2Modal()
                      }}
                    >
                      <Edit size={14} /> {interview.scheduleRound2 ? 'Edit' : 'Add'} Details
                    </RoleBasedButton>
                </div>
                <div className="info-grid">
                  <div className="info-item">
                    <h4>Status</h4>
                    <div className={`status-badge ${interview.resultRound2 === true ? 'passed' : interview.resultRound2 === false ? 'failed' : 'pending'}`}>
                      {interview.resultRound2 === true ? (
                        <><CheckCircle size={16} /> Passed</>
                      ) : interview.resultRound2 === false ? (
                        <><XCircle size={16} /> Failed</>
                      ) : (
                        'Pending'
                      )}
                    </div>
                  </div>
                  <div className="info-item">
                    <h4>Schedule</h4>
                    <div className="interview-date">
                      <Calendar size={16} />
                      <span>{interview.scheduleRound2 ? formatDate(interview.scheduleRound2) : 'Not scheduled'}</span>
                    </div>
                  </div>
                  <div className="info-item">
                    <h4>Interviewer</h4>
                    <div className="interviewer">
                      <Users size={16} />
                      <span>{interview.interviewerRound2 || 'Not assigned'}</span>
                    </div>
                  </div>
                </div>
                {interview.contentRound2 && (
                  <div className="interview-details">
                    <h4>Interview Content</h4>
                    <Typography variant="body2">{interview.contentRound2}</Typography>
                  </div>
                )}
                {interview.reviewRound2 && (
                  <div className="interview-details">
                    <h4>Review Notes</h4>
                    <Typography variant="body2">{interview.reviewRound2}</Typography>
                  </div>
                )}
              </div>
            ) : (
              <div className="add-round-container">
                <RoleBasedButton
                  allowedRoles={['admin', 'hr']}
                  buttonProps={{
                    variant: "outlined",
                    size: "small",
                    color: "primary",
                    onClick: () => handleCreateNewRound2(),
                    disabled: interview.resultRound1 !== true
                  }}
                >
                  <Plus size={16} /> Add Round 2
                </RoleBasedButton>
                {interview.resultRound1 !== true && (
                  <div className="helper-text">Round 1 must be passed before adding Round 2</div>
                )}
              </div>
            )}
          </div>
        ) : (
          application?.processStatus === 'INVITED' || application?.processStatus === 'INTERVIEWED' ? (
            <div className="no-interview">
              <Typography variant="body1">No interview data available yet.</Typography>
            </div>
          ) : (
            <div className="no-interview">
              <Typography variant="body1">This applicant has not been invited for an interview.</Typography>
            </div>
          )
        )}
      </div>

      {/* Round 1 Modal */}
      {showRound1Modal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>{interview?.scheduleRound1 ? 'Edit' : 'Add'} Round 1 Details</h2>
              <button className="close-btn" onClick={() => setShowRound1Modal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Schedule Date</label>
                <TextField
                  type="datetime-local"
                  value={round1Data.scheduleRound1}
                  onChange={(e) => setRound1Data({...round1Data, scheduleRound1: e.target.value})}
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </div>
              <div className="form-group">
                <label>Interviewer Email</label>
                <TextField
                  type="email"
                  value={round1Data.interviewerRound1}
                  onChange={(e) => setRound1Data({...round1Data, interviewerRound1: e.target.value})}
                  placeholder="Interviewer email address"
                  fullWidth
                  error={!!round1Data.interviewerRound1 && !validateEmail(round1Data.interviewerRound1)}
                  helperText={!!round1Data.interviewerRound1 && !validateEmail(round1Data.interviewerRound1) ? "Please enter a valid email address" : ""}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Mail size={16} />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div className="form-group">
                <label>Interview Content/Notes</label>
                <TextField
                  value={round1Data.contentRound1}
                  onChange={(e) => setRound1Data({...round1Data, contentRound1: e.target.value})}
                  placeholder="Details about the interview"
                  multiline
                  rows={4}
                  fullWidth
                />
              </div>
              <div className="form-group">
                <label>Result</label>
                <FormControl fullWidth>
                  <Select
                    value={round1Data.resultRound1 === true ? 'pass' : round1Data.resultRound1 === false ? 'fail' : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setRound1Data({
                        ...round1Data,
                        resultRound1: value === 'pass' ? true : value === 'fail' ? false : null
                      });
                    }}
                    displayEmpty
                  >
                    <MenuItem value="">
                      <em>Pending</em>
                    </MenuItem>
                    <MenuItem value="pass">Pass</MenuItem>
                    <MenuItem value="fail">Fail</MenuItem>
                  </Select>
                </FormControl>
              </div>
              <div className="form-group">
                <label>Review Notes</label>
                <TextField
                  value={round1Data.reviewRound1 || ''}
                  onChange={(e) => setRound1Data({...round1Data, reviewRound1: e.target.value})}
                  placeholder="Review notes"
                  multiline
                  rows={3}
                  fullWidth
                />
              </div>
              {isAdmin && (
                <div className="form-group">
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={round1Data.sendEmailRound1 || false}
                        onChange={(e) => setRound1Data({...round1Data, sendEmailRound1: e.target.checked})}
                      />
                    }
                    label="Send email notification"
                  />
                </div>
              )}
            </div>
            <div className="modal-footer">
              <Button variant="outlined" onClick={() => setShowRound1Modal(false)}>
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={handleUpdateRound1}
                disabled={!!round1Data.interviewerRound1 && !validateEmail(round1Data.interviewerRound1)}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Round 2 Modal */}
      {showRound2Modal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>{interview?.scheduleRound2 ? 'Edit' : 'Add'} Round 2 Details</h2>
              <button className="close-btn" onClick={() => setShowRound2Modal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>Schedule Date</label>
                <TextField
                  type="datetime-local"
                  value={round2Data.scheduleRound2}
                  onChange={(e) => setRound2Data({...round2Data, scheduleRound2: e.target.value})}
                  fullWidth
                  error={!isScheduleTimeValid(interview?.scheduleRound1, round2Data?.scheduleRound2)}
                  helperText={
                    !isScheduleTimeValid(interview?.scheduleRound1, round2Data?.scheduleRound2)
                      ? "The interview time for Round 2 must be later than Round 1."
                      : ""
                  }
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </div>
              <div className="form-group">
                <label>Interviewer Email</label>
                <TextField
                  type="email"
                  value={round2Data.interviewerRound2}
                  onChange={(e) => setRound2Data({...round2Data, interviewerRound2: e.target.value})}
                  placeholder="Interviewer email address"
                  fullWidth
                  error={!!round2Data.interviewerRound2 && !validateEmail(round2Data.interviewerRound2)}
                  helperText={!!round2Data.interviewerRound2 && !validateEmail(round2Data.interviewerRound2) ? "Please enter a valid email address" : ""}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Mail size={16} />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>
              <div className="form-group">
                <label>Interview Content/Notes</label>
                <TextField
                  value={round2Data.contentRound2}
                  onChange={(e) => setRound2Data({...round2Data, contentRound2: e.target.value})}
                  placeholder="Details about the interview"
                  multiline
                  rows={4}
                  fullWidth
                />
              </div>
              <div className="form-group">
                <label>Result</label>
                <FormControl fullWidth>
                  <Select
                    value={round2Data.resultRound2 === true ? 'pass' : round2Data.resultRound2 === false ? 'fail' : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setRound2Data({
                        ...round2Data,
                        resultRound2: value === 'pass' ? true : value === 'fail' ? false : null
                      });
                    }}
                    displayEmpty
                  >
                    <MenuItem value="">
                      <em>Pending</em>
                    </MenuItem>
                    <MenuItem value="pass">Pass</MenuItem>
                    <MenuItem value="fail">Fail</MenuItem>
                  </Select>
                </FormControl>
              </div>
              <div className="form-group">
                <label>Review Notes</label>
                <TextField
                  value={round2Data.reviewRound2 || ''}
                  onChange={(e) => setRound2Data({...round2Data, reviewRound2: e.target.value})}
                  placeholder="Review notes"
                  multiline
                  rows={3}
                  fullWidth
                />
              </div>
              {isAdmin && (
                <div className="form-group">
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={round2Data.sendEmailRound2 || false}
                        onChange={(e) => setRound2Data({...round2Data, sendEmailRound2: e.target.checked})}
                      />
                    }
                    label="Send email notification"
                  />
                </div>
              )}
            </div>
            <div className="modal-footer">
              <Button variant="outlined" onClick={() => setShowRound2Modal(false)}>
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={handleUpdateRound2}
                disabled={
                  (round2Data.interviewerRound2 && !validateEmail(round2Data.interviewerRound2))
                  || !isScheduleTimeValid(interview?.scheduleRound1, round2Data?.scheduleRound2)
                }
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default InterviewSection;
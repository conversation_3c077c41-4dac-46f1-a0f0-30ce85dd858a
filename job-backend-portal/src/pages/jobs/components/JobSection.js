import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Briefcase, Calendar, Clock } from 'lucide-react';

const JobSection = ({ job, application, formatDate, formatTimeAlive, calculateRemainTime }) => {
  return (
    <div className="card">
      <h2>Job Information</h2>
      <div className="info-grid">
        <div className="info-item">
          <h3>Job Title</h3>
          <div className="job-title">
            <Briefcase size={16} />
            {job ? (
              <Link to={`/admin/jobs/${job.id}/view`} className="job-link">
                {job.title}
              </Link>
            ) : (
              <span>{application?.jobTitle || job?.title || 'N/A'}</span>
            )}
          </div>
        </div>
        <div className="info-item">
          <h3>Job Location</h3>
          <div className="job-location">
            <MapPin size={16} />
            <span>{ application?.jobLocation || job?.location || 'N/A'}</span>
          </div>
        </div>
        <div className="info-item">
          <h3>Job Status</h3>
          <div className={`job-status-badge ${(job?.status || 'general').toLowerCase()}`}>
            {job?.status || 'General Application'}
          </div>
        </div>
        <div className="info-item">
          <h3>Job Code</h3>
          <span>{application?.jobCode || job?.code || 'N/A'}</span>
        </div>
        <div className="info-item">
          <h3>Posted Date</h3>
          <div className="job-date">
            <Calendar size={16} />
            <span>{formatDate(application?.jobCreatedAt || job?.createdAt)}</span>
          </div>
        </div>
        <div className="info-item">
          <h3>Time Alive</h3>
          <div className="time-alive">
            <Clock size={16} />
            <span>{formatTimeAlive(application?.jobTimeAlive || job?.timeAlive)}</span>
          </div>
        </div>
        <div className="info-item">
          <h3>Remaining Time</h3>
          <div className={`remain-time ${calculateRemainTime(application?.jobTimeAlive || job?.timeAlive, application?.jobCreatedAt || job?.createdAt) === 0 ? 'expired' : ''}`}>
            <Clock size={16} />
            <span>{calculateRemainTime(application?.jobTimeAlive || job?.timeAlive, application?.jobCreatedAt || job?.createdAt)} days</span>
          </div>
        </div>
        <div className="info-item">
          <h3>Salary Range</h3>
          <div className="salary-range">
            <span>{job?.salary || application?.jobSalary || 'N/A'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobSection;
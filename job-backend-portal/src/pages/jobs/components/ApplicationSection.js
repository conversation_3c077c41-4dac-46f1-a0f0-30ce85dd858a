import React from 'react';
import { FileText } from 'lucide-react';
import { Box, Button, Typography, Chip } from '@mui/material';
import { LEVEL_LABELS, ENGLISH_SKILL_LABELS } from '../../../constants/collaboratorPolicy';

const ApplicationSection = ({ 
  application, 
  getStatusLabel, 
  getStatusClass, 
  getProcessStatusLabel, 
  getProcessStatusClass, 
  formatDate,
  handleViewCV,
  cvLoading,
  cvError
}) => {
  return (
    <>
      <div className="card">
        <h2>Application Details</h2>
        <div className="info-grid">
          <div className="info-item">
            <h3>Status</h3>
            <div className={`status-badge ${getStatusClass(application?.status)}`}>
              {getStatusLabel(application?.status)}
            </div>
          </div>
          {application?.processStatus && (
            <div className="info-item">
              <h3>Process Status</h3>
              <div className={`process-badge ${getProcessStatusClass(application.processStatus)}`}>
                {getProcessStatusLabel(application.processStatus)}
              </div>
            </div>
          )}
          <div className="info-item">
            <h3>Applied Date</h3>
            <p>{application?.createdAt ? formatDate(application.createdAt) : 'N/A'}</p>
          </div>
          <div className="info-item">
            <h3>Last Updated</h3>
            <p>{application?.updatedAt ? formatDate(application.updatedAt) : 'N/A'}</p>
          </div>
          {application?.refPerson && (
            <div className="info-item">
              <h3>Reference Person</h3>
              <div className="ref-person">
                <div style={{ width: 16, height: 16, display: 'inline-flex', alignItems: 'center', justifyContent: 'center', marginRight: '4px' }}>👤</div>
                <span>{application.refPerson}</span>
              </div>
            </div>
          )}
          <div className="info-item">
            <h3>Candidate Level</h3>
            {application?.candidateLevel ? (
              <Chip
                label={LEVEL_LABELS[application.candidateLevel]}
                color="primary"
                size="small"
              />
            ) : (
              <Typography variant="body2" color="text.secondary">
                Not assessed
              </Typography>
            )}
          </div>
          <div className="info-item">
            <h3>English Skill</h3>
            {application?.englishSkill ? (
              <Chip
                label={ENGLISH_SKILL_LABELS[application.englishSkill]}
                color="secondary"
                size="small"
              />
            ) : (
              <Typography variant="body2" color="text.secondary">
                Not assessed
              </Typography>
            )}
          </div>
        </div>
      </div>

      <div className="card">
        <h2>Resume & Documents</h2>
        <div className="info-item">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FileText size={20} />
            {application?.cvFile ? (
              <Button
                onClick={handleViewCV}
                disabled={cvLoading}
                sx={{ textTransform: 'none' }}
              >
                {cvLoading ? 'Loading...' : 'View CV'}
              </Button>
            ) : (
              'No CV uploaded'
            )}
            {cvError && (
              <Typography color="error" variant="caption">
                {cvError}
              </Typography>
            )}
          </Box>
        </div>
      </div>

      {application?.notes && (
        <div className="card">
          <h2>Notes</h2>
          <Typography variant="body1">
            {application.notes}
          </Typography>
        </div>
      )}
    </>
  );
};

export default ApplicationSection; 
/* Jobs Hero Section */
.jobs-hero {
  padding: 80px 0 60px;
  text-align: center;
  background-color: var(--color-accent-light);
}

.jobs-hero-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.jobs-hero-content p {
  font-size: 1.25rem;
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Jobs Filter Section */
.jobs-filter {
  padding: 30px 0;
  border-bottom: 1px solid var(--color-border);
}

.filter-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 30px;
  padding: 0 20px;
  overflow: hidden;
}

.search-box svg {
  color: var(--color-text-secondary);
  margin-right: 10px;
}

.search-box input {
  flex: 1;
  border: none;
  padding: 12px 0;
  background-color: transparent;
  color: var(--color-text);
  font-size: 1rem;
  outline: none;
}

.search-box input::placeholder {
  color: var(--color-text-secondary);
}

.category-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.category-btn {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-text);
}

.category-btn:hover {
  background-color: var(--color-border);
}

.category-btn.active {
  background-color: var(--color-accent);
  color: white;
  border-color: var(--color-accent);
}

/* Jobs Listing Section */
.jobs-listing {
  padding: 60px 0;
}

.jobs-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.job-listing-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.job-listing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.job-listing-content {
  padding: 30px;
}

.job-listing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.job-listing-header h2 {
  font-size: 1.5rem;
  margin: 0;
}

.featured-badge {
  background-color: var(--color-accent);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.job-description {
  color: var(--color-text-secondary);
  margin-bottom: 20px;
}

.job-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.job-detail {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

.job-detail svg {
  color: var(--color-accent);
}

.job-listing-actions {
  display: flex;
  border-top: 1px solid var(--color-border);
  padding: 20px 30px;
  gap: 15px;
}

.view-btn,
.apply-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 30px;
  font-weight: 500;
  flex: 1;
  justify-content: center;
}

.no-jobs-found {
  text-align: center;
  padding: 60px 0;
}

.no-jobs-found h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.no-jobs-found p {
  color: var(--color-text-secondary);
}

/* Jobs CTA Section */
.jobs-cta {
  padding: 80px 0;
  text-align: center;
  background-color: var(--color-accent-light);
}

.jobs-cta-content {
  max-width: 700px;
  margin: 0 auto;
}

.jobs-cta-content h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.jobs-cta-content p {
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
}

.cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 30px;
  font-weight: 500;
}

/* Responsive Styles */
@media (min-width: 768px) {
  .filter-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .search-box {
    width: 400px;
  }

  .job-listing-actions {
    justify-content: flex-end;
  }

  .view-btn,
  .apply-btn {
    flex: 0 0 auto;
  }
}

.admin-jobs-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.MuiPaper-root {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.MuiTableCell-head {
  font-weight: 600;
  background-color: #f8f9fa;
}

.MuiTableRow-root:hover {
  background-color: #f8f9fa;
}

.MuiButton-root {
  text-transform: none;
}

.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-content {
  flex: 1;
  overflow-x: hidden;
}

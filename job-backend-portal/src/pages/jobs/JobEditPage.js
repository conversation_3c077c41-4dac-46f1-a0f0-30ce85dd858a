import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableRow,
  IconButton,
  CircularProgress
} from '@mui/material';
import { getJobById, updateJob, createJob } from '../../services/jobService';
import { getOptions } from '../../services/optionService';
import './JobEditPage.css';
import { Delete, Edit, Check, X } from 'lucide-react';
import { JOB_STATUS, DEFAULT_NEW_JOB, SAMPLE_ADDRESSES, OPTION_TYPES } from '../../constants/jobs';

function JobEditPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewJob = id === 'create';
  const [loading, setLoading] = useState(!isNewJob);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const [jobTypes, setJobTypes] = useState([]);
  const [jobCategories, setJobCategories] = useState([]);
  const [jobLocations, setJobLocations] = useState([]);
  const [jobSalaryRanges, setJobSalaryRanges] = useState([]);
  const [jobWorkingHours, setJobWorkingHours] = useState([]);

  // Initialize with empty values or default values based on isNewJob
  const [job, setJob] = useState(isNewJob ? {
    ...DEFAULT_NEW_JOB,
    // Generate a unique code for each new job
    code: `JOB-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, "0")}`,
    slug: ""
  } : {
    title: "",
    description: "",
    category: "",
    type: "",
    location: "",
    salary: "",
    responsibilities: [],
    technicalSkills: [],
    softSkills: [],
    benefits: [],
    status: JOB_STATUS.OPEN,
    featured: false,
    fullAddress: "",
    code: "",
    timeAlive: "",
    workingHours: "",
    slug: ""
  });

  const [newResponsibility, setNewResponsibility] = useState('');
  const [editingResponsibilityIndex, setEditingResponsibilityIndex] = useState(null);
  const [editingResponsibilityText, setEditingResponsibilityText] = useState('');
  const [newTechnicalSkill, setNewTechnicalSkill] = useState('');
  const [editingTechnicalSkillIndex, setEditingTechnicalSkillIndex] = useState(null);
  const [editingTechnicalSkillText, setEditingTechnicalSkillText] = useState('');
  const [newSoftSkill, setNewSoftSkill] = useState('');
  const [editingSoftSkillIndex, setEditingSoftSkillIndex] = useState(null);
  const [editingSoftSkillText, setEditingSoftSkillText] = useState('');
  const [newBenefit, setNewBenefit] = useState('');
  const [editingBenefitIndex, setEditingBenefitIndex] = useState(null);
  const [editingBenefitText, setEditingBenefitText] = useState('');

  useEffect(() => {
    const loadJob = async () => {
      try {
        const params = {
          page: -1,
          limit: -1,
          optionNames: Object.values(OPTION_TYPES)
        };
        const [options, jobData] = await Promise.all([
          getOptions(params),
          !isNewJob ? getJobById(id) : Promise.resolve(null)
        ]);

        const optionByTypes = options.items.reduce((result, option) => {
          if (!result[option.optionName]) {
            result[option.optionName] = [];
          }
          result[option.optionName].push(option.optionTitle);
          return result;
        }, {});

        // Set state for each option type
        setJobTypes(optionByTypes[OPTION_TYPES.JOB_TYPES] || []);
        setJobCategories(optionByTypes[OPTION_TYPES.JOB_CATEGORIES] || []);
        setJobLocations(optionByTypes[OPTION_TYPES.JOB_LOCATIONS] || []);
        setJobSalaryRanges(optionByTypes[OPTION_TYPES.JOB_SALARY_RANGES] || []);
        setJobWorkingHours(optionByTypes[OPTION_TYPES.JOB_WORKING_HOURS] || []);

        if (!isNewJob && jobData) {
          setJob(jobData);
        }
      } catch (err) {
        setError('Failed to load job details');
        console.error('Error loading job:', err);
      } finally {
        setLoading(false);
      }
    };

    loadJob();
  }, [id, isNewJob]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setJob(prevJob => {
      const updatedJob = {
        ...prevJob,
        [name]: value
      };
      return updatedJob;
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    try {
      if (isNewJob) {
        await createJob(job);
        setSuccess(true);
        setTimeout(() => {
          navigate('/jobs');
        }, 2000);
      } else {
        const updatedJob = await updateJob(id, job);
        if (!updatedJob) {
          throw new Error('Failed to update job');
        }
        setSuccess(true);
        setTimeout(() => {
          navigate('/jobs');
        }, 2000);
      }
    } catch (err) {
      setError(isNewJob ? 'Failed to create job' : 'Failed to update job');
      console.error('Error saving job:', err);
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
          flexDirection: 'column',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography variant="body1" color="text.secondary">
          Loading job details...
        </Typography>
      </Box>
    );
  }

  const handleAddResponsibility = () => {
    if (newResponsibility.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        responsibilities: [...(prevJob.responsibilities || []), newResponsibility.trim()]
      }));
      setNewResponsibility('');
    }
  };

  const handleDeleteResponsibility = (index) => {
    setJob(prevJob => ({
      ...prevJob,
      responsibilities: prevJob.responsibilities.filter((_, i) => i !== index)
    }));
  };

  const handleEditResponsibility = (index) => {
    setEditingResponsibilityIndex(index);
    setEditingResponsibilityText(job.responsibilities[index]);
  };

  const handleSaveResponsibility = (index) => {
    if (editingResponsibilityText.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        responsibilities: prevJob.responsibilities.map((resp, i) => 
          i === index ? editingResponsibilityText.trim() : resp
        )
      }));
      setEditingResponsibilityIndex(null);
      setEditingResponsibilityText('');
    }
  };

  const handleCancelEdit = () => {
    setEditingResponsibilityIndex(null);
    setEditingResponsibilityText('');
  };

  const handleAddTechnicalSkill = () => {
    if (newTechnicalSkill.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        technicalSkills: [...(prevJob.technicalSkills || []), newTechnicalSkill.trim()]
      }));
      setNewTechnicalSkill('');
    }
  };

  const handleDeleteTechnicalSkill = (index) => {
    setJob(prevJob => ({
      ...prevJob,
      technicalSkills: prevJob.technicalSkills.filter((_, i) => i !== index)
    }));
  };

  const handleEditTechnicalSkill = (index) => {
    setEditingTechnicalSkillIndex(index);
    setEditingTechnicalSkillText(job.technicalSkills[index]);
  };

  const handleSaveTechnicalSkill = (index) => {
    if (editingTechnicalSkillText.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        technicalSkills: prevJob.technicalSkills.map((skill, i) => 
          i === index ? editingTechnicalSkillText.trim() : skill
        )
      }));
      setEditingTechnicalSkillIndex(null);
      setEditingTechnicalSkillText('');
    }
  };

  const handleCancelTechnicalSkillEdit = () => {
    setEditingTechnicalSkillIndex(null);
    setEditingTechnicalSkillText('');
  };

  const handleAddSoftSkill = () => {
    if (newSoftSkill.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        softSkills: [...(prevJob.softSkills || []), newSoftSkill.trim()]
      }));
      setNewSoftSkill('');
    }
  };

  const handleDeleteSoftSkill = (index) => {
    setJob(prevJob => ({
      ...prevJob,
      softSkills: prevJob.softSkills.filter((_, i) => i !== index)
    }));
  };

  const handleEditSoftSkill = (index) => {
    setEditingSoftSkillIndex(index);
    setEditingSoftSkillText(job.softSkills[index]);
  };

  const handleSaveSoftSkill = (index) => {
    if (editingSoftSkillText.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        softSkills: prevJob.softSkills.map((skill, i) => 
          i === index ? editingSoftSkillText.trim() : skill
        )
      }));
      setEditingSoftSkillIndex(null);
      setEditingSoftSkillText('');
    }
  };

  const handleCancelSoftSkillEdit = () => {
    setEditingSoftSkillIndex(null);
    setEditingSoftSkillText('');
  };

  const handleAddBenefit = () => {
    if (newBenefit.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        benefits: [...(prevJob.benefits || []), newBenefit.trim()]
      }));
      setNewBenefit('');
    }
  };

  const handleDeleteBenefit = (index) => {
    setJob(prevJob => ({
      ...prevJob,
      benefits: prevJob.benefits.filter((_, i) => i !== index)
    }));
  };

  const handleEditBenefit = (index) => {
    setEditingBenefitIndex(index);
    setEditingBenefitText(job.benefits[index]);
  };

  const handleSaveBenefit = (index) => {
    if (editingBenefitText.trim()) {
      setJob(prevJob => ({
        ...prevJob,
        benefits: prevJob.benefits.map((benefit, i) => 
          i === index ? editingBenefitText.trim() : benefit
        )
      }));
      setEditingBenefitIndex(null);
      setEditingBenefitText('');
    }
  };

  const handleCancelBenefitEdit = () => {
    setEditingBenefitIndex(null);
    setEditingBenefitText('');
  };

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" component="h1" sx={{ mb: 3 }}>
              {isNewJob ? 'Create Job' : 'Edit Job'}
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Job {isNewJob ? 'created' : 'updated'} successfully! Redirecting...
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Job Title"
                    name="title"
                    value={job.title}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Slug"
                    name="slug"
                    value={job.slug}
                    InputProps={{
                      readOnly: true,
                    }}
                    helperText="This will be automatically generated from the job title"
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Category</InputLabel>
                    <Select
                      name="category"
                      value={job.category}
                      onChange={handleChange}
                      label="Category"
                      required
                    >
                      {jobCategories.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Type</InputLabel>
                    <Select
                      name="type"
                      value={job.type}
                      onChange={handleChange}
                      label="Type"
                      required
                    >
                      {jobTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          {type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Location</InputLabel>
                    <Select
                      name="location"
                      value={job.location}
                      onChange={handleChange}
                      label="Location"
                      required
                    >
                      {jobLocations.map((location) => (
                        <MenuItem key={location} value={location}>
                          {location}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Salary Range (Gross)</InputLabel>
                    <Select
                      name="salary"
                      value={job.salary}
                      onChange={handleChange}
                      label="Salary Range"
                      required
                    >
                      {jobSalaryRanges.map((range) => (
                        <MenuItem key={range} value={range}>
                          {range}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      name="status"
                      value={job.status}
                      onChange={handleChange}
                      label="Status"
                      required
                    >
                      <MenuItem value={JOB_STATUS.OPEN}>Open</MenuItem>
                      <MenuItem value={JOB_STATUS.CLOSED}>Closed</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                    <InputLabel>Featured</InputLabel>
                    <Select
                        name="featured"
                        value={job.featured}
                        onChange={handleChange}
                        label="Featured"
                    >
                        <MenuItem value={true}>Yes</MenuItem>
                        <MenuItem value={false}>No</MenuItem>
                    </Select>
                    </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                    <TextField
                    fullWidth
                    label="Job Code"
                    name="code"
                    value={job.code}
                    onChange={handleChange}
                    placeholder="e.g., JOB-2024-001"
                    />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Working Hours</InputLabel>
                    <Select
                      name="workingHours"
                      value={job.workingHours}
                      onChange={handleChange}
                      label="Working Hours"
                      required
                    >
                      {jobWorkingHours.map((hours) => (
                        <MenuItem key={hours} value={hours}>
                          {hours}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Full Address</InputLabel>
                    <Select
                      name="fullAddress"
                      value={job.fullAddress}
                      onChange={handleChange}
                      label="Full Address"
                      required
                    >
                      {SAMPLE_ADDRESSES.map((address) => (
                        <MenuItem key={address} value={address}>
                          {address}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Time Alive (days)"
                    name="timeAlive"
                    type="number"
                    value={job.timeAlive}
                    onChange={handleChange}
                    inputProps={{ min: 1 }}
                    placeholder="e.g., 30"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={job.description}
                    onChange={handleChange}
                    multiline
                    rows={4}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                    <Typography variant="h6" sx={{ mb: 2 }}>Responsibilities</Typography>
                    <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
                        <TextField
                        fullWidth
                        label="Add Responsibility"
                        value={newResponsibility}
                        onChange={(e) => setNewResponsibility(e.target.value)}
                        size="small"
                        />
                        <Button
                        onClick={handleAddResponsibility}
                        variant="contained"
                        size="small"
                        >
                        Add
                        </Button>
                    </Box>
                    <Table size="small">
                        <TableBody>
                        {(job.responsibilities || []).map((responsibility, index) => (
                            <TableRow key={index}>
                            <TableCell>
                                {editingResponsibilityIndex === index ? (
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                        <TextField
                                            size="small"
                                            value={editingResponsibilityText}
                                            onChange={(e) => setEditingResponsibilityText(e.target.value)}
                                            fullWidth
                                        />
                                        <IconButton
                                            size="small"
                                            color="primary"
                                            onClick={() => handleSaveResponsibility(index)}
                                        >
                                            <Check size={16} />
                                        </IconButton>
                                        <IconButton
                                            size="small"
                                            color="error"
                                            onClick={handleCancelEdit}
                                        >
                                            <X size={16} />
                                        </IconButton>
                                    </Box>
                                ) : (
                                    responsibility
                                )}
                            </TableCell>
                            <TableCell align="right">
                                {editingResponsibilityIndex !== index && (
                                    <>
                                        <IconButton
                                            size="small"
                                            color="primary"
                                            onClick={() => handleEditResponsibility(index)}
                                            sx={{ mr: 1 }}
                                        >
                                            <Edit size={16} />
                                        </IconButton>
                                        <IconButton
                                            size="small"
                                            color="error"
                                            onClick={() => handleDeleteResponsibility(index)}
                                        >
                                            <Delete size={16} />
                                        </IconButton>
                                    </>
                                )}
                            </TableCell>
                            </TableRow>
                        ))}
                        </TableBody>
                    </Table>
                    </Grid>


                {/* After Responsibilities Grid item */}

                    <Grid item xs={12}>
                        <Typography variant="h6" sx={{ mb: 2 }}>Technical Skills</Typography>
                        <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
                            <TextField
                            fullWidth
                            label="Add Technical Skill"
                            value={newTechnicalSkill}
                            onChange={(e) => setNewTechnicalSkill(e.target.value)}
                            size="small"
                            />
                            <Button
                            onClick={handleAddTechnicalSkill}
                            variant="contained"
                            size="small"
                            >
                            Add
                            </Button>
                        </Box>
                        <Table size="small">
                            <TableBody>
                            {(job.technicalSkills || []).map((skill, index) => (
                                <TableRow key={index}>
                                <TableCell>
                                    {editingTechnicalSkillIndex === index ? (
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <TextField
                                                size="small"
                                                value={editingTechnicalSkillText}
                                                onChange={(e) => setEditingTechnicalSkillText(e.target.value)}
                                                fullWidth
                                            />
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={() => handleSaveTechnicalSkill(index)}
                                            >
                                                <Check size={16} />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={handleCancelTechnicalSkillEdit}
                                            >
                                                <X size={16} />
                                            </IconButton>
                                        </Box>
                                    ) : (
                                        skill
                                    )}
                                </TableCell>
                                <TableCell align="right">
                                    {editingTechnicalSkillIndex !== index && (
                                        <>
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={() => handleEditTechnicalSkill(index)}
                                                sx={{ mr: 1 }}
                                            >
                                                <Edit size={16} />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => handleDeleteTechnicalSkill(index)}
                                            >
                                                <Delete size={16} />
                                            </IconButton>
                                        </>
                                    )}
                                </TableCell>
                                </TableRow>
                            ))}
                            </TableBody>
                        </Table>
                    </Grid>

                    <Grid item xs={12}>
                        <Typography variant="h6" sx={{ mb: 2 }}>Soft Skills</Typography>
                        <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
                            <TextField
                            fullWidth
                            label="Add Soft Skill"
                            value={newSoftSkill}
                            onChange={(e) => setNewSoftSkill(e.target.value)}
                            size="small"
                            />
                            <Button
                            onClick={handleAddSoftSkill}
                            variant="contained"
                            size="small"
                            >
                            Add
                            </Button>
                        </Box>
                        <Table size="small">
                            <TableBody>
                            {(job.softSkills || []).map((skill, index) => (
                                <TableRow key={index}>
                                <TableCell>
                                    {editingSoftSkillIndex === index ? (
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <TextField
                                                size="small"
                                                value={editingSoftSkillText}
                                                onChange={(e) => setEditingSoftSkillText(e.target.value)}
                                                fullWidth
                                            />
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={() => handleSaveSoftSkill(index)}
                                            >
                                                <Check size={16} />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={handleCancelSoftSkillEdit}
                                            >
                                                <X size={16} />
                                            </IconButton>
                                        </Box>
                                    ) : (
                                        skill
                                    )}
                                </TableCell>
                                <TableCell align="right">
                                    {editingSoftSkillIndex !== index && (
                                        <>
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={() => handleEditSoftSkill(index)}
                                                sx={{ mr: 1 }}
                                            >
                                                <Edit size={16} />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => handleDeleteSoftSkill(index)}
                                            >
                                                <Delete size={16} />
                                            </IconButton>
                                        </>
                                    )}
                                </TableCell>
                                </TableRow>
                            ))}
                            </TableBody>
                        </Table>
                    </Grid>

                    <Grid item xs={12}>
                        <Typography variant="h6" sx={{ mb: 2 }}>Benefits</Typography>
                        <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
                            <TextField
                            fullWidth
                            label="Add Benefit"
                            value={newBenefit}
                            onChange={(e) => setNewBenefit(e.target.value)}
                            size="small"
                            />
                            <Button
                            onClick={handleAddBenefit}
                            variant="contained"
                            size="small"
                            >
                            Add
                            </Button>
                        </Box>
                        <Table size="small">
                            <TableBody>
                            {(job.benefits || []).map((benefit, index) => (
                                <TableRow key={index}>
                                <TableCell>
                                    {editingBenefitIndex === index ? (
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <TextField
                                                size="small"
                                                value={editingBenefitText}
                                                onChange={(e) => setEditingBenefitText(e.target.value)}
                                                fullWidth
                                            />
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={() => handleSaveBenefit(index)}
                                            >
                                                <Check size={16} />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={handleCancelBenefitEdit}
                                            >
                                                <X size={16} />
                                            </IconButton>
                                        </Box>
                                    ) : (
                                        benefit
                                    )}
                                </TableCell>
                                <TableCell align="right">
                                    {editingBenefitIndex !== index && (
                                        <>
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={() => handleEditBenefit(index)}
                                                sx={{ mr: 1 }}
                                            >
                                                <Edit size={16} />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => handleDeleteBenefit(index)}
                                            >
                                                <Delete size={16} />
                                            </IconButton>
                                        </>
                                    )}
                                </TableCell>
                                </TableRow>
                            ))}
                            </TableBody>
                        </Table>
                    </Grid>

                    {/* Fix the form ending structure */}
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                        <Button
                          variant="outlined"
                          onClick={() => navigate('/jobs')}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          variant="contained"
                          color="primary"
                        >
                          {isNewJob ? 'Create Job' : 'Save Changes'}
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </form>
              </Paper>
            </Box>
            </div>
          </div>

  );
}

export default JobEditPage;
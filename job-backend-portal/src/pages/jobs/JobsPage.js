"use client"

import { useState, useEffect } from "react"
import { Link } from "react-router-dom"
import { Plus } from "lucide-react"
import "./JobsPage.css"
import { getOptions } from '../../services/optionService';
import { OPTION_TYPES } from '../../constants/jobs';
import { getAllJobs } from "../../services/jobService"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  Pagination,
  Box,
  Typography,
  Button,
  Grid,
  FormControl,
  InputLabel
} from '@mui/material'
import { RoleBasedButton } from "../../utils/roleUtils"

// Add after imports
const JobStatus = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED'
};

function JobsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [appliedSearchTerm, setAppliedSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState("All")
  const [jobs, setJobs] = useState([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [rowsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(0)
  const [categories, setCategories] = useState(["All"])

  const formatDate = (date) => {
    return new Date(date).toISOString().split('T')[0];
  };

  const loadJobs = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage,
        search: searchTerm,
        category: filterCategory,
      };

      const response = await getAllJobs(params);
      setJobs(response.items);
      setTotalPages(response.meta.totalPages);
    } catch (error) {
      console.error('Failed to load jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const params = {
            page: -1,
            limit: -1,
            optionNames: OPTION_TYPES.JOB_CATEGORIES
          };
        const categories = await getOptions(params);
        setCategories(["All",...categories.items.map(c => c.optionTitle)]); 
      }
      catch (error) {
        console.error('Failed to load categories:', error);
      }
    };
    
    loadCategories();
  }, [])

  useEffect(() => {
    loadJobs();
  }, [page, rowsPerPage, appliedSearchTerm, filterCategory]);

  useEffect(() => {
    setPage(1);
  }, [appliedSearchTerm, filterCategory]);

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            p: 3
          }}>
            <Typography variant="h6">Loading...</Typography>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Jobs Management
            </Typography>
            <RoleBasedButton
              allowedRoles={['admin']}
              buttonProps={{
                variant: "contained",
                color: "primary",
                startIcon: <Plus />,
                component: Link,
                to: "/jobs/create"
              }}
            >
              Add New Job
            </RoleBasedButton>
          </Box>

          <Paper sx={{ p: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                label="Search Jobs"
                variant="outlined"
                size="small"
                sx={{ flexGrow: 1 }}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    setAppliedSearchTerm(searchTerm);
                  }
                }}
              />
              <Button
                onClick={() => setAppliedSearchTerm(searchTerm)}
                variant="contained"
                size="small"
              >
                Search
              </Button>
            </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Category Filter</InputLabel>
                  <Select
                    value={filterCategory}
                    onChange={(e) => setFilterCategory(e.target.value)}
                    label="Category Filter"
                  >
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Table sx={{ minWidth: 800 }}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Category</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Location</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Type</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Created Date</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Duration (d)</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {jobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell>
                        <Link
                          to={`/jobs/${job.id}/view`}
                          style={{
                            color: '#1976d2',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          {job.title}
                        </Link>
                      </TableCell>
                      <TableCell>{job.category}</TableCell>
                      <TableCell>{job.location}</TableCell>
                      <TableCell>{job.type}</TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            backgroundColor: job.status === JobStatus.OPEN ? '#e8f5e9' : '#ffebee',
                            color: job.status === JobStatus.OPEN ? '#2e7d32' : '#c62828',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            display: 'inline-block',
                            fontSize: '0.875rem'
                          }}
                        >
                          {job.status || JobStatus.OPEN}
                        </Box>
                      </TableCell>
                      <TableCell>{formatDate(job.createdAt)}</TableCell>
                      <TableCell>{job.timeAlive} days</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <RoleBasedButton
                            allowedRoles={['admin', 'hr']}
                            buttonProps={{
                              variant: "outlined",
                              size: "small",
                              component: Link,
                              to: `/jobs/${job.id}`
                            }}
                          >
                            Edit
                          </RoleBasedButton>
                          <RoleBasedButton
                            allowedRoles={['admin']}
                            buttonProps={{
                              variant: "outlined",
                              color: "error",
                              size: "small"
                            }}
                          >
                            Delete
                          </RoleBasedButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handleChangePage}
                color="primary"
                shape="rounded"
                type="first"
                showFirstButton
                showLastButton
                siblingCount={3}
              />
            </Box>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default JobsPage

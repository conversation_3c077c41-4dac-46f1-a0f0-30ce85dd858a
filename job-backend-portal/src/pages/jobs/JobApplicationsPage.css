.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-content {
  flex: 1;
  overflow-x: hidden;
}

/* Table Styles */
.MuiPaper-root {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.MuiTableCell-head {
  font-weight: 600;
  background-color: #f8f9fa;
}

.MuiTableRow-root:hover {
  background-color: #f8f9fa;
}

.MuiButton-root {
  text-transform: none;
}

/* Status Badge Styles */
.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-block;
}

.status-badge.pending {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-badge.reviewing {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-badge.shortlisted {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.interviewed {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.status-badge.offered {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.rejected {
  background-color: #ffebee;
  color: #f44336;
}

.status-badge.withdrawn {
  background-color: #f5f5f5;
  color: #757575;
}

/* Search and Filter Section */
.filter-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
}

.search-field {
  flex-grow: 1;
}

.status-filter {
  min-width: 200px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
  }

  .status-filter {
    width: 100%;
  }
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 1rem;
  margin-top: 1rem;
} 
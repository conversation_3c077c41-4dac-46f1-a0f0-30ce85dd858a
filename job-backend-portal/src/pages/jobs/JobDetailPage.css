/* Banner Section */
.job-banner {
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url("../../../public/images/office-banner.jpg");
  background-size: cover;
  background-position: center;
  height: 300px;
  display: flex;
  align-items: center;
  position: relative;
  color: white;
}

.banner-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: rgba(138, 127, 255, 0.2); /* Slight accent color overlay */
}

.banner-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.banner-subtitle {
  font-size: 1.25rem;
  max-width: 600px;
  color: rgba(255, 255, 255, 0.9);
}

.job-detail-page {
  min-height: 100vh;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
  margin-bottom: 20px;
  text-decoration: none;
  transition: color 0.3s;
}

.back-link:hover {
  color: var(--color-accent);
}

/* Update the job-title-section to work well with the banner */
.job-title-section {
  padding: 40px 0;
  border-bottom: 1px solid var(--color-border);
}

.job-title-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 30px;
}

.job-title-section h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  color: var(--color-text);
  transition: color 0.3s;
}

.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-secondary);
  transition: color 0.3s;
}

.meta-item svg {
  font-size: 0.9rem;
  color: var(--color-accent);
}

.apply-btn {
  border-radius: 30px;
  padding: 12px 28px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.arrow {
  margin-left: 8px;
  transition: transform 0.3s;
}

.apply-btn:hover .arrow {
  transform: translateX(4px);
}

@media (min-width: 768px) {
  .job-title-content {
    flex-direction: row;
    align-items: flex-start;
  }

  .job-title-section h1 {
    font-size: 3.5rem;
  }
}


/* CMS-specific styles */
/* CMS Layout */
.job-detail-page.cms-view {
  background-color: #f5f6fa;
  min-height: 100vh;
}

/* Admin Header */
.cms-view .admin-header {
  background: white;
  padding: 1.5rem 0;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.04);
}

.admin-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.back-link {
  color: #5a5c69;
  font-weight: 500;
  transition: all 0.2s;
}

.back-link:hover {
  color: #4e73df;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.action-buttons button {
  padding: 0.625rem 1.25rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
}

.secondary-btn {
  background: #4e73df;
  color: white;
}

.secondary-btn:hover {
  background: #2e59d9;
}

.danger-btn {
  background: #e74a3b;
}

.danger-btn:hover {
  background: #be2617;
}

/* Status Controls */
.status-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #ffffff;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.04);
}

.status-select {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #e3e6f0;
  font-size: 0.875rem;
  color: #5a5c69;
  background: #fff;
  cursor: pointer;
}

/* Content Grid */
.content-grid.cms-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  padding: 1.5rem;
}

/* Cards */
.card {
  background-color: var(--color-card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid var(--color-border);
  transition: background-color 0.3s, border-color 0.3s;
}

.card h2 {
  color: #5a5c69;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e3e6f0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stats-card {
  background: linear-gradient(145deg, #4f46e5, #4338ca);
}

.stats-card h2 {
  color: #e2e8f0;
  border-bottom-color: rgba(226, 232, 240, 0.2);
}

.stat-item {
  background: rgba(226, 232, 240, 0.15);
  border-radius: 8px;
  padding: 1rem;
}

.stat-item h4 {
  color: #37393b;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stat-item p {
  color: #37393b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

/* Badge Styles */
.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  margin: 2px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #6b7280;
  transition: all 0.2s ease;
}

.badge.active {
  background-color: #4f46e5;
  color: white;
}

.badge.green {
  background-color: #10b981;
  color: white;
}

.badge.red {
  background-color: #ef4444;
  color: white;
}

/* Details List */
.details-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.detail-item h3 {
  color: #5a5c69;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.detail-item p {
  color: #2d3748;
  font-size: 1rem;
}

/* Benefits List */
.benefits-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.benefits-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.benefits-list li:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.benefits-list li span {
  color: #334155;
  font-size: 0.95rem;
}

.benefits-list li::before {
  content: "*";
  display: inline-block;
  color: #4f46e5;
  font-weight: bold;
}

/* Sidebar Styles */
.cms-sidebar {
  position: sticky;
  top: 20px;
}

.stats-card {
  background: linear-gradient(to right, #4e73df, #224abe);
  color: white;
}

.stats-card h2 {
  color: white;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.stats-card .stat-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-card .stat-item h4 {
  color: rgba(15, 15, 15, 0.8);
}

.stats-card .stat-item p {
  color: rgb(39, 39, 39);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.full-width {
  width: 100%;
}

.outline-btn {
  background: transparent;
  border: 1px solid #4e73df;
  color: #4e73df;
  padding: 0.625rem 1.25rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.outline-btn:hover {
  background: #4e73df;
  color: white;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.left-content {
  flex: 1;
}

.left-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
}

.job-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.meta-item svg {
  color: #4f46e5;
}

.right-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.secondary-btn {
  background: #4f46e5;
  color: white;
}

.secondary-btn:hover {
  background: #4338ca;
}

.danger-btn {
  background: #ef4444;
  color: white;
}

.danger-btn:hover {
  background: #dc2626;
}

.status-select {
  padding: 0.5rem 2rem 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
}

/* Update existing card styles */
.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card h2 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.page-layout {
  display: flex;
  gap: 2rem;
  padding: 0 2rem;
}

.content-area {
  flex: 1;
  min-width: 0;
}

.page-sidebar {
  width: 320px;
  position: sticky;
  top: 1.5rem;
  height: fit-content;
}

.page-sidebar .card {
  margin-bottom: 1rem;
}

@media (max-width: 1024px) {
  .page-layout {
    flex-direction: column;
  }

  .page-sidebar {
    width: 100%;
    position: static;
  }
}
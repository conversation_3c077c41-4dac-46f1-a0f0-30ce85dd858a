"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"
import { ArrowLeft, Mail, Phone, Edit, X } from "lucide-react"
import { getJobApplicationById, updateJobApplication, downloadCV, rejectJobApplication, receivedJobApplication } from "../../services/jobApplicationService"
import { getJobById } from "../../services/jobService"
import { getInterviewByJobApplicationId, createInterview, updateInterviewRound1, updateInterviewRound2 } from "../../services/interviewService"
import CVViewerModal from "../../components/CVViewerModal"
import {
  Box,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography
} from '@mui/material'
import { APPLICATION_STATUS, PROCESS_STATUS } from "../../constants/jobApplications"
import { LEVEL_LABELS, ENGLISH_SKILL_LABELS, EMPLOYMENT_TYPE } from "../../constants/collaboratorPolicy"
import "./JobApplicationDetailPage.css"
import { useToast } from "../../contexts/ToastContext"
import { formatDate, formatDateTimeForInput } from "../../utils/dateUtils"

// Import our component sections
import { JobSection, ApplicationSection, InterviewSection } from './components'
import { Email } from "@mui/icons-material"

function JobApplicationDetailPage() {
  const { id } = useParams()
  const { showToast } = useToast()
  const [application, setApplication] = useState(null)
  const [job, setJob] = useState(null)
  const [interview, setInterview] = useState(null)
  const [interviewLoading, setInterviewLoading] = useState(false)
  const [interviewError, setInterviewError] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [editMode, setEditMode] = useState(false)
  const [editedStatus, setEditedStatus] = useState("")
  const [editedProcessStatus, setEditedProcessStatus] = useState("")
  const [editedNotes, setEditedNotes] = useState("")
  const [editedRefPerson, setEditedRefPerson] = useState("")
  const [editedCandidateLevel, setEditedCandidateLevel] = useState("")
  const [editedEnglishSkill, setEditedEnglishSkill] = useState("")
  const [updateError, setUpdateError] = useState(null)
  const [cvModalOpen, setCvModalOpen] = useState(false)
  const [cvUrl, setCvUrl] = useState(null)
  const [cvLoading, setCvLoading] = useState(false)
  const [cvError, setCvError] = useState(null)
  const [showRound1Modal, setShowRound1Modal] = useState(false)
  const [showRound2Modal, setShowRound2Modal] = useState(false)
  const [showRejectConfirmation, setShowRejectConfirmation] = useState(false)
  const [round1Data, setRound1Data] = useState({
    scheduleRound1: '',
    interviewerRound1: '',
    contentRound1: '',
    reviewRound1: '',
    resultRound1: null,
    sendEmailRound1: true
  })
  const [round2Data, setRound2Data] = useState({
    scheduleRound2: '',
    interviewerRound2: '',
    contentRound2: '',
    reviewRound2: '',
    resultRound2: null,
    sendEmailRound2: true
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        const applicationData = await getJobApplicationById(id)
        setApplication(applicationData)
        setEditedStatus(applicationData.status)
        setEditedProcessStatus(applicationData.processStatus)
        setEditedNotes(applicationData.notes || "")
        setEditedRefPerson(applicationData.refPerson || "")
        setEditedCandidateLevel(applicationData.candidateLevel || "")
        setEditedEnglishSkill(applicationData.englishSkill || "")

        // Only fetch job details if we have a valid jobId (not "general") and no job details in the DTO
        if (applicationData.jobId && applicationData.jobId !== "general" && !applicationData.jobCode) {
          try {
            const jobData = await getJobById(applicationData.jobId)
            setJob(jobData)
          } catch (jobError) {
            console.error('Error loading job:', jobError)
            // Don't set error state here, just log it
          }
        }

        // Fetch interview details if application exists
        if (applicationData.id) {
          try {
            setInterviewLoading(true)
            const interviewData = await getInterviewByJobApplicationId(applicationData.id)
            setInterview(interviewData)
          } catch (interviewError) {
            // Don't set error state here, just log it - interview might not exist yet
            console.error('Error loading interview:', interviewError)
          } finally {
            setInterviewLoading(false)
          }
        }
      } catch (error) {
        setError('Failed to load application details')
        console.error('Error loading application:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [id])

  // Handler for interview interactions
  const handleCreateInterview = async () => {
    try {
      if (!application?.id) return;

      setInterviewError(null);

      const interviewData = {
        jobApplicationId: application.id
      };

      const newInterview = await createInterview(interviewData);
      setInterview(newInterview);
      showToast('Interview process created successfully', 'success')
      handleOpenRound1Modal();
    } catch (error) {
      console.error('Error creating interview:', error);
      showToast('Failed to create interview process', 'error')
    }
  };

  const handleUpdateRound1 = async () => {
    try {
      if (!interview?.id) return;

      setInterviewError(null);

      const updatedInterview = await updateInterviewRound1(interview.id, round1Data);
      setInterview(updatedInterview);
      setShowRound1Modal(false);
      showToast('Round 1 details updated successfully', 'success')
    } catch (error) {
      console.error('Error updating round 1:', error);
      showToast('Failed to update round 1', 'error')
    }
  };

  const handleUpdateRound2 = async () => {
    try {
      if (!interview?.id) return;

      setInterviewError(null);

      const updatedInterview = await updateInterviewRound2(interview.id, round2Data);
      setInterview(updatedInterview);
      setShowRound2Modal(false);
      showToast('Round 2 details updated successfully', 'success')
    } catch (error) {
      console.error('Error updating round 2:', error);
      showToast('Failed to update round 2', 'error')
    }
  };

  const handleOpenRound1Modal = () => {
    if (!interview) return;

    setRound1Data({
      scheduleRound1: formatDateTimeForInput(interview.scheduleRound1),
      interviewerRound1: interview.interviewerRound1 || '',
      contentRound1: interview.contentRound1 || '',
      reviewRound1: interview.reviewRound1 || '',
      resultRound1: interview.resultRound1,
      sendEmailRound1: interview.sendEmailRound1 ?? true
    });

    setShowRound1Modal(true);
  };

  const handleOpenRound2Modal = () => {
    if (!interview) return;

    setRound2Data({
      scheduleRound2: formatDateTimeForInput(interview.scheduleRound2),
      interviewerRound2: interview.interviewerRound2 || '',
      contentRound2: interview.contentRound2 || '',
      reviewRound2: interview.reviewRound2 || '',
      resultRound2: interview.resultRound2,
      sendEmailRound2: interview.sendEmailRound2 ?? true
    });

    setShowRound2Modal(true);
  };

  const handleCreateNewRound2 = () => {
    setRound2Data({
      scheduleRound2: '',
      interviewerRound2: '',
      contentRound2: '',
      reviewRound2: '',
      resultRound2: null,
      sendEmailRound2: true
    });

    setShowRound2Modal(true);
  };

  // Application status handlers
  const validateStatusUpdate = () => {
    // Reset previous errors
    setUpdateError(null)

    // Basic validation rules
    if (!editedStatus) {
      setUpdateError('Application status is required')
      return false
    }

    // Process status validation based on application status
    if (editedStatus === APPLICATION_STATUS.ACCEPT && !editedProcessStatus) {
      setUpdateError('Process status is required when application is accepted')
      return false
    }

    const empType = application.jobType && ['full-time', 'internship'].includes(application.jobType.toLowerCase()) 
      ? EMPLOYMENT_TYPE.FULLTIME 
      : EMPLOYMENT_TYPE.FREELANCER;

    if (editedRefPerson && empType === EMPLOYMENT_TYPE.FULLTIME && editedProcessStatus === PROCESS_STATUS.ONBOARD && (!editedCandidateLevel || !editedEnglishSkill)) {
      setUpdateError('Candidate level and English skill must be provided to auto-create commission for referrer')
      return false
    }

    return true
  }

  const handleStatusUpdate = async () => {
    if (!validateStatusUpdate()) {
      return
    }

    try {
      const updatedData = {
        ...application,
        status: editedStatus,
        processStatus: editedProcessStatus,
        notes: editedNotes,
        refPerson: editedRefPerson,
        candidateLevel: editedCandidateLevel || null,
        englishSkill: editedEnglishSkill || null,
        lastUpdated: new Date().toISOString()
      }

      await updateJobApplication(id, updatedData)
      setApplication(prev => ({
        ...prev,
        ...updatedData
      }))
      showToast('Application status updated successfully', 'success')
      setEditMode(false)
    } catch (error) {
      console.error('Error updating application:', error)
      const errorMessage = error.response?.data?.message || 'Failed to update application status'
      setUpdateError(errorMessage)
      showToast(errorMessage, 'error')
    }
  }

  const handleRejectConfirm = async () => {
    try {
      setUpdateError(null)
      await rejectJobApplication(id)
      //reload application
      const applicationData = await getJobApplicationById(id)
      setApplication(applicationData)
      setShowRejectConfirmation(false)
      showToast('Application rejected successfully', 'success')
    } catch (error) {
      console.error('Error rejecting application:', error)
      showToast('Failed to reject application', 'error')
    }
  }

  const sendReceivedEmail = async () => {
    try {
      await receivedJobApplication(id)
      //load application again
      const applicationData = await getJobApplicationById(id)
      setApplication(applicationData)
      showToast('Received email sent successfully', 'success')
    } catch (error) {
      console.error('Error sending received email:', error)
      showToast('Failed to send received email', 'error')
    }
  }
  // Helper functions for formatting and display
  const getStatusLabel = (status) => {
    const labels = {
      [APPLICATION_STATUS.NEW]: 'New Application',
      [APPLICATION_STATUS.ACCEPT]: 'Accepted',
      [APPLICATION_STATUS.REJECT]: 'Rejected'
    }
    return labels[status] || status
  }

  const getProcessStatusLabel = (status) => {
    return status ? status.charAt(0) + status.slice(1).toLowerCase() : '';
  }

  const formatDateSafe = (date) => {
    const formatted = formatDate(date);
    return formatted || 'N/A';
  };

  const getStatusClass = (status) => {
    if (!status) return 'new';
    return status.toLowerCase();
  }

  const getProcessStatusClass = (status) => {
    if (!status) return '';
    return status.toLowerCase();
  }

  const formatTimeAlive = (timeAlive) => {
    if (!timeAlive) return 'N/A';
    return `${timeAlive} days`;
  }

  const calculateRemainTime = (timeAlive, createdAt) => {
    if (!timeAlive || !createdAt) return 0;

    const createdDate = new Date(createdAt);
    const now = new Date();
    const daysSinceCreated = Math.floor((now - createdDate) / (1000 * 60 * 60 * 24));
    const remainingDays = timeAlive - daysSinceCreated;

    return Math.max(0, remainingDays);
  }

  // CV handling
  const handleViewCV = async () => {
    if (!application?.id) return;

    setCvLoading(true);
    setCvError(null);

    try {
      const url = await downloadCV(application.id);
      setCvUrl(url);
      setCvModalOpen(true);
    } catch (error) {
      setCvError('Failed to load CV. Please try again.');
      console.error('Error loading CV:', error);
    } finally {
      setCvLoading(false);
    }
  };

  const handleCloseModal = () => {
    setCvModalOpen(false);
    if (cvUrl) {
      URL.revokeObjectURL(cvUrl);
      setCvUrl(null);
    }
  };

  // Loading and error states
  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <div className="job-application-detail-page cms-view">
            <div className="loading-container">
              <CircularProgress />
              <p>Loading application details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <div className="job-application-detail-page cms-view">
            <Typography color="error" sx={{ p: 3 }}>{error}</Typography>
          </div>
        </div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <div className="job-application-detail-page cms-view">
            <Box sx={{ p: 3 }}>Application not found</Box>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="job-application-detail-page cms-view">
          {/* Page Header */}
          <div className="page-header">
            <div className="header-content">
              <div className="left-content">
                <Link to="/applicants" className="back-link">
                  <ArrowLeft size={16} /> Back to Applications
                </Link>
                <h1>{application?.name || 'Application Details'}</h1>
                <div className="applicant-meta">
                  <div className="meta-item">
                    <Mail size={16} />
                    <span>{application?.email || 'N/A'}</span>
                  </div>
                  <div className="meta-item">
                    <Phone size={16} />
                    <span>{application?.phoneNumber || 'N/A'}</span>
                  </div>
                  {application?.refPerson && (
                  <div className="meta-item">
                    <div style={{ width: 16, height: 16, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>👤</div>
                    <span>Ref: {application.refPerson}</span>
                  </div>
                  )}
                </div>
              </div>
              <div className="right-content">
                <div className="action-buttons">
                  {!editMode && (
                    <>
                      {application.processStatus === PROCESS_STATUS.NONE && (
                        <button className="btn secondary-btn" onClick={() => sendReceivedEmail()}>
                          <Email size={16} /> Send Received
                        </button>
                      )}
                      {application.status === APPLICATION_STATUS.NEW && (
                        <button 
                          className="btn danger-btn"
                          onClick={() => setShowRejectConfirmation(true)}
                        >
                          <X size={16} /> Reject
                        </button>
                      )}
                      <button className="btn secondary-btn" onClick={() => setEditMode(true)}>
                        <Edit size={16} /> Edit
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="page-layout">
            <div className="content-area">
              {/* Job Section */}
              <JobSection
                job={job}
                application={application}
                formatDate={formatDateSafe}
                formatTimeAlive={formatTimeAlive}
                calculateRemainTime={calculateRemainTime}
              />

              {/* Application Section */}
              <ApplicationSection
                application={application}
                getStatusLabel={getStatusLabel}
                getStatusClass={getStatusClass}
                getProcessStatusLabel={getProcessStatusLabel}
                getProcessStatusClass={getProcessStatusClass}
                formatDate={formatDateSafe}
                handleViewCV={handleViewCV}
                cvLoading={cvLoading}
                cvError={cvError}
              />

              {/* Interview Section */}
              <InterviewSection
                interview={interview}
                interviewLoading={interviewLoading}
                interviewError={interviewError}
                application={application}
                formatDate={formatDateSafe}
                formatDateForInput={formatDateTimeForInput}
                handleCreateInterview={handleCreateInterview}
                showRound1Modal={showRound1Modal}
                setShowRound1Modal={setShowRound1Modal}
                showRound2Modal={showRound2Modal}
                setShowRound2Modal={setShowRound2Modal}
                round1Data={round1Data}
                setRound1Data={setRound1Data}
                round2Data={round2Data}
                setRound2Data={setRound2Data}
                handleUpdateRound1={handleUpdateRound1}
                handleUpdateRound2={handleUpdateRound2}
                handleOpenRound1Modal={handleOpenRound1Modal}
                handleOpenRound2Modal={handleOpenRound2Modal}
                handleCreateNewRound2={handleCreateNewRound2}
              />
            </div>

            {/* Edit Sidebar */}
            {editMode && (
              <div className="page-sidebar">
                <div className="card">
                  <h2>Edit Application</h2>
                  {updateError && (
                    <Typography color="error" sx={{ mb: 2 }}>
                      {updateError}
                    </Typography>
                  )}
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={editedStatus || ''}
                      onChange={(e) => {
                        setEditedStatus(e.target.value);
                        if (e.target.value === APPLICATION_STATUS.REJECT) {
                          setEditedProcessStatus(PROCESS_STATUS.CANCELLED);
                        }
                      }}
                      label="Status"
                      error={!editedStatus}
                    >
                      {Object.values(APPLICATION_STATUS).map((status) => (
                        <MenuItem key={status} value={status}>
                          {getStatusLabel(status)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    <InputLabel>Process Status</InputLabel>
                    <Select
                      value={editedProcessStatus || ''}
                      onChange={(e) => setEditedProcessStatus(e.target.value)}
                      label="Process Status"
                      error={editedStatus === APPLICATION_STATUS.ACCEPT && !editedProcessStatus}
                    >
                      
                      {Object.values(PROCESS_STATUS).map((status) => (
                        <MenuItem key={status} value={status}>
                          {status}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Notes"
                    value={editedNotes || ''}
                    onChange={(e) => setEditedNotes(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    label="Reference Person"
                    value={editedRefPerson || ''}
                    onChange={(e) => setEditedRefPerson(e.target.value)}
                    sx={{ mb: 2 }}
                    placeholder="Who referred this applicant"
                  />
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Candidate Level</InputLabel>
                    <Select
                      value={editedCandidateLevel || ''}
                      onChange={(e) => setEditedCandidateLevel(e.target.value)}
                      label="Candidate Level"
                    >
                      <MenuItem value="">
                        <em>Not assessed</em>
                      </MenuItem>
                      {Object.entries(LEVEL_LABELS).map(([key, label]) => (
                        <MenuItem key={key} value={key}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>English Skill</InputLabel>
                    <Select
                      value={editedEnglishSkill || ''}
                      onChange={(e) => setEditedEnglishSkill(e.target.value)}
                      label="English Skill"
                    >
                      <MenuItem value="">
                        <em>Not assessed</em>
                      </MenuItem>
                      {Object.entries(ENGLISH_SKILL_LABELS).map(([key, label]) => (
                        <MenuItem key={key} value={key}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      onClick={handleStatusUpdate}
                    >
                      Save Changes
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setEditMode(false)}
                    >
                      Cancel
                    </Button>
                  </Box>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CV Viewer Modal */}
      <CVViewerModal
        open={cvModalOpen}
        onClose={handleCloseModal}
        cvUrl={cvUrl}
        fileName={`${application?.name}'s CV`}
      />

      <Dialog
        open={showRejectConfirmation}
        onClose={() => setShowRejectConfirmation(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"Reject Application"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to reject this application? This will send a rejection email to the applicant.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRejectConfirmation(false)}>Cancel</Button>
          <Button onClick={handleRejectConfirm} autoFocus color="error">
            Reject
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  )
}

export default JobApplicationDetailPage
"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom"
import { ArrowLeft, MapPin, Clock, Briefcase, DollarSign, Edit } from "lucide-react"
import "./JobDetailPage.css"
import { getJobById, getJobStatistics } from "../../services/jobService"
import { CircularProgress } from '@mui/material';
import { RoleBasedButton } from "../../utils/roleUtils"
// Add after imports
const JobStatus = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED'
};

// Remove this duplicate import
// import { CircularProgress } from '@mui/material';

function JobDetailPage() {
  const { id } = useParams()
  const navigate = useNavigate();
  const [job, setJob] = useState({
    title: '',
    location: '',
    type: '',
    code: '',
    salary: '',
    description: '',
    responsibilities: [],
    technicalSkills: [],
    softSkills: [],
    fullAddress: '',
    workingHours: '',
    benefits: [],
    slug: ''
  })
  const [jobStatistics, setJobStatistics] = useState(null);
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchJobDetail = async () => {
      try {
        const jobData = await getJobById(id);
        setJob(jobData);
      } catch (error) {
        setError('Failed to load job details');
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetail();
  }, [id])

  useEffect(() => {
    const fetchJobStatistics = async () => {
      try {
        const jobStats = await getJobStatistics(id);
        setJobStatistics(jobStats);
      } catch (error) {
        console.error('Failed to fetch job statistics', error);
      }
    };

    fetchJobStatistics();
  }, [id]);

  const handleEdit = () => {
    navigate(`/jobs/${id}`);
  }


  // Update the loading return statement
  if (loading) return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="job-detail-page cms-view">
          <div className="loading-container" style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <CircularProgress />
            <p>Loading job details...</p>
          </div>
        </div>
      </div>
    </div>
  );

  if (error) return <div className="error">{error}</div>
  if (!job) return <div>Job not found</div>

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <div className="job-detail-page cms-view">
          <div className="page-header">
            <div className="header-content">
              <div className="left-content">
                <Link to="/jobs" className="back-link">
                  <ArrowLeft size={16} /> Back to Jobs List
                </Link>
                <h1>{job.title}</h1>
                <div className="job-meta">
                  <div className="meta-item">
                    <MapPin size={16} />
                    <span>{job.location}</span>
                  </div>
                  <div className="meta-item">
                    <Clock size={16} />
                    <span>{job.type}</span>
                  </div>
                  <div className="meta-item">
                    <Briefcase size={16} />
                    <span>Code: {job.code}</span>
                  </div>
                  <div className="meta-item">
                    <DollarSign size={16} />
                    <span>{job.salary}</span>
                  </div>
                  <div className="meta-item">
                    <span>Slug: {job.slug}</span>
                  </div>
                </div>
              </div>
              <div className="right-content">
                <div className="action-buttons">
                  <RoleBasedButton
                    allowedRoles={['admin', 'employer', 'hr']}
                    buttonProps={{
                      className: "btn secondary-btn",
                      onClick: handleEdit
                    }}
                  >
                    <Edit size={16} /> Edit Job
                  </RoleBasedButton>
                  
                </div>
              </div>
            </div>
          </div>

          <main className="main-content">
            <div className="page-layout">
              {/* Left Column - Job Details */}
              <div className="content-area">
                <div className="container">
                  {/* Job Overview */}
                  <div className="card">
                    <h2>Job Overview</h2>
                    <p>{job.description}</p>
                  </div>

                  {/* Key Responsibilities */}
                  <div className="card">
                    <h2>Key Responsibilities</h2>
                    <ul>
                      {job.responsibilities.map((responsibility, index) => (
                        <li key={index}>{responsibility}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Job Requirements */}
                  <div className="card">
                    <h2>Job Requirements</h2>
                    <h3>Technical Skills:</h3>
                    <ul>
                      {job.technicalSkills.map((skill, index) => (
                        <li key={index}>{skill}</li>
                      ))}
                    </ul>
                    <h3>Soft Skills:</h3>
                    <ul>
                      {job.softSkills.map((skill, index) => (
                        <li key={index}>{skill}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Right Sidebar */}
              <div className="page-sidebar">
                {/* Job Statistics Card with example data */}
                <div className="card stats-card">
                  <h2>Job Statistics</h2>
                  <div className="stats-grid">
                    <div className="stat-item">
                      <h4>Total Views</h4>
                      <p>{jobStatistics?.totalViews ?? '-'}</p>
                    </div>
                    <div className="stat-item">
                      <h4>Applications</h4>
                      <p>{jobStatistics?.applications ?? '-'}</p>
                    </div>
                  </div>
                </div>

                {/* Position Details Card */}
                <div className="card">
                  <h2>Position Details</h2>
                  <div className="details-list">
                    <div className="detail-item">
                      <h3>Location</h3>
                      <p>{job.fullAddress}</p>
                    </div>

                    <div className="detail-item">
                      <h3>Working Hours</h3>
                      <p>{job.workingHours}</p>
                    </div>

                    <div className="detail-item">
                      <h3>Employment Type</h3>
                      <div className="badge-container">
                        <span className={`badge ${job.type === "Full-time" ? "active" : ""}`}>Full-time</span>
                        <span className={`badge ${job.type === "Part-time" ? "active" : ""}`}>Part-time</span>
                        <span className={`badge ${job.type === "Remote" ? "active" : ""}`}>Remote</span>
                        <span className={`badge ${job.type === "Contractor" ? "active" : ""}`}>Contractor</span>
                        <span className={`badge ${job.type === "Intern" ? "active" : ""}`}>Intern</span>
                      </div>
                    </div>
                    <div className="detail-item">
                      <h3>Status</h3>
                      <div className="badge-container">
                        <span className={`badge ${job.status === JobStatus.OPEN ? 'green' : 'red'}`}>
                          {job.status || JobStatus.OPEN}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Benefits Card */}
                <div className="card">
                    <h2>Benefits & Welfare</h2>
                    <ul className="benefits-list">
                      {job?.benefits?.map((benefit, index) => (
                        <li key={index}>
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

export default JobDetailPage

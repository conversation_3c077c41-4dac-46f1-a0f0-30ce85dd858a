.job-application-detail-page {
  min-height: 100vh;
}

.job-application-detail-page.cms-view {
  background-color: #f5f6fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.left-content {
  flex: 1;
}

.left-content h1 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
}

.applicant-meta {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.meta-item svg {
  color: #4f46e5;
}

.right-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.back-link {
  color: #5a5c69;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.back-link:hover {
  color: #4e73df;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.secondary-btn {
  background: #4f46e5;
  color: white;
}

.secondary-btn:hover {
  background: #4338ca;
}

.page-layout {
  display: flex;
  gap: 2rem;
  padding: 0 2rem;
}

.content-area {
  flex: 1;
  min-width: 0;
}

.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card h2 {
  color: #111827;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item h3 {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.info-item p {
  color: #111827;
  font-size: 1rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-badge.new {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-badge.accepted {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.rejected {
  background-color: #ffebee;
  color: #f44336;
}

.process-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.process-badge.invited {
  background-color: #fff3e0;
  color: #ff9800;
}

.process-badge.interviewed {
  background-color: #e3f2fd;
  color: #2196f3;
}

.process-badge.failed {
  background-color: #ffebee;
  color: #f44336;
}

.process-badge.passed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.process-badge.onboard {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.process-badge.cancelled {
  background-color: #f5f5f5;
  color: #757575;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

@media (max-width: 1024px) {
  .page-layout {
    flex-direction: column;
  }

  .page-sidebar {
    width: 100%;
    position: static;
  }
}

.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-content {
  flex: 1;
  overflow-x: hidden;
}

.job-title, .job-location, .time-alive, .salary-range, .job-date, .remain-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #111827;
}

.job-link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.job-link:hover {
  color: #4338ca;
  text-decoration: underline;
}

.job-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.job-status-badge.open {
  background-color: #e8f5e9;
  color: #4caf50;
}

.job-status-badge.closed {
  background-color: #ffebee;
  color: #f44336;
}

.time-alive, .salary-range, .job-date, .remain-time {
  font-weight: 500;
  color: #4b5563;
}

.time-alive span, .salary-range span, .job-date span, .remain-time span {
  padding: 0.25rem 0.5rem;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.remain-time.expired span {
  background-color: #ffebee;
  color: #f44336;
}

.job-date svg, .time-alive svg, .remain-time svg {
  color: #4f46e5;
  min-width: 16px;
}

/* Interview Process */
.interview-timeline {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.interview-round {
  border-left: 3px solid #e0e0e0;
  padding-left: 20px;
  position: relative;
}

.interview-round::before {
  content: "";
  position: absolute;
  left: -8px;
  top: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e0e0e0;
}

.interview-round h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.round-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.round-header h3 {
  margin: 0;
}

.card-header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header-with-actions h2 {
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.interview-round h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.interview-details {
  margin-top: 16px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.interviewer, .interview-date {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Status colors */
.status-badge.passed {
  background-color: #e6f7e6;
  color: #2e7d32;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-badge.failed {
  background-color: #fbe9e7;
  color: #d32f2f;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #ff8f00;
}

.no-interview {
  padding: 20px 0;
  color: #666;
}

/* Add Round 2 container */
.add-round-container {
  padding: 20px 0;
  border-left: 3px solid #e0e0e0;
  padding-left: 20px;
  position: relative;
}

.add-round-container::before {
  content: "";
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e0e0e0;
}

.helper-text {
  font-size: 13px;
  color: #666;
  margin-top: 8px;
  font-style: italic;
}

.error-message {
  color: #d32f2f;
  background-color: #fbe9e7;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}

.success-message {
  color: #2e7d32;
  background-color: #e6f7e6;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
}

.close-btn {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

/* Button styles */
.outline-btn {
  background-color: transparent;
  border: 1px solid #4f46e5;
  color: #4f46e5;
}

.outline-btn:hover {
  background-color: #f0f0ff;
} 
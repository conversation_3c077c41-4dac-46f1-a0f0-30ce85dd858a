import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  Button,
  TextField,
  Pagination,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  Tooltip,
  IconButton
} from '@mui/material';
import { Plus, Eye, Edit, Trash2, CheckCircle, DollarSign } from 'lucide-react';
import {
  getAllCommissionReports,
  deleteCommissionReport,
  approveCommissionReport,
  markCommissionReportAsPaid
} from '../../services/commissionReportService';
import { formatDate } from '../../utils/dateUtils';
import { useToast } from "../../contexts/ToastContext";
import CommissionReportCreateDialog from './CommissionReportCreateDialog';
import { formatCurrency } from '../../utils/currencyUtils';
import {
  REPORT_STATUS,
  REPORT_STATUS_LABELS,
  PAYMENT_STATUS,
  PAYMENT_STATUS_LABELS
} from '../../constants/collaboratorPolicy';

const CommissionReportListPage = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [monthFilter, setMonthFilter] = useState('');
  const [yearFilter, setYearFilter] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reportToDelete, setReportToDelete] = useState(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const rowsPerPage = 10;

  const loadReports = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage,
        search: appliedSearchTerm,
        status: statusFilter || undefined,
        month: monthFilter || undefined,
        year: yearFilter || undefined
      };

      const response = await getAllCommissionReports(params);
      setReports(response.items);
      setTotalPages(response.meta.totalPages);
      setError(null);
    } catch (error) {
      console.error('Failed to load commission reports:', error);
      setError('Failed to load commission reports');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReports();
  }, [page, appliedSearchTerm, statusFilter, monthFilter, yearFilter]);

  const handleSearch = () => {
    setAppliedSearchTerm(searchTerm);
    setPage(1);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleDeleteClick = (report) => {
    setReportToDelete(report);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!reportToDelete) return;

    try {
      await deleteCommissionReport(reportToDelete.id);
      showToast('Commission report deleted successfully', 'success');
      loadReports();
    } catch (error) {
      console.error('Failed to delete commission report:', error);
      showToast('Failed to delete commission report', 'error');
    } finally {
      setDeleteDialogOpen(false);
      setReportToDelete(null);
    }
  };

  const handleApprove = async (reportId) => {
    try {
      await approveCommissionReport(reportId);
      showToast('Commission report approved successfully', 'success');
      loadReports();
    } catch (error) {
      console.error('Failed to approve commission report:', error);
      showToast('Failed to approve commission report', 'error');
    }
  };

  const handleMarkAsPaid = async (reportId) => {
    try {
      await markCommissionReportAsPaid(reportId);
      showToast('Commission report marked as paid successfully', 'success');
      loadReports();
    } catch (error) {
      console.error('Failed to mark commission report as paid:', error);
      showToast('Failed to mark commission report as paid', 'error');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case REPORT_STATUS.PENDING:
        return 'warning';
      case REPORT_STATUS.APPROVED:
        return 'success';
      case REPORT_STATUS.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const handleRowDoubleClick = (reportId) => {
    navigate(`/commission-reports/${reportId}`);
  };

  const handleCreateSuccess = (createdReport) => {
    setCreateDialogOpen(false);
    showToast('Commission report created successfully', 'success');
    // Redirect to view page
    navigate(`/commission-reports/${createdReport.id}`);
  };

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Commission Report Management
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Plus />}
              onClick={() => setCreateDialogOpen(true)}
            >
              Create report
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Filters */}
          <Paper sx={{ p: 2, mb: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Search"
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="">All</MenuItem>
                    {Object.entries(REPORT_STATUS_LABELS).map(([key, label]) => (
                      <MenuItem key={key} value={key}>
                        {label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Month</InputLabel>
                  <Select
                    value={monthFilter}
                    onChange={(e) => setMonthFilter(e.target.value)}
                    label="Month"
                  >
                    <MenuItem value="">All</MenuItem>
                    {Array.from({ length: 12 }, (_, i) => (
                      <MenuItem key={i + 1} value={i + 1}>
                        {i + 1}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Year</InputLabel>
                  <Select
                    value={yearFilter}
                    onChange={(e) => setYearFilter(e.target.value)}
                    label="Year"
                  >
                    <MenuItem value="">All</MenuItem>
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - i;
                      return (
                        <MenuItem key={year} value={year}>
                          {year}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  variant="contained"
                  onClick={handleSearch}
                  sx={{ mr: 1 }}
                >
                  Search
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    setSearchTerm('');
                    setAppliedSearchTerm('');
                    setStatusFilter('');
                    setMonthFilter('');
                    setYearFilter('');
                    setPage(1);
                  }}
                >
                  Clear
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Paper>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Actions</TableCell>
                      <TableCell>Report Name</TableCell>
                      <TableCell>Employer Email</TableCell>
                      <TableCell>Month/Year</TableCell>
                      <TableCell>Total Amount</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Payment Status</TableCell>
                      <TableCell>Created Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reports.map((report) => (
                      <TableRow
                        key={report.id}
                        onDoubleClick={() => handleRowDoubleClick(report.id)}
                        sx={{ cursor: 'pointer', '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' } }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => navigate(`/commission-reports/${report.id}/edit`)}
                              >
                                <Edit size={16} />
                              </IconButton>
                            </Tooltip>
                            {report.status === REPORT_STATUS.PENDING && (
                              <Tooltip title="Approve">
                                <IconButton
                                  size="small"
                                  color="success"
                                  onClick={() => handleApprove(report.id)}
                                >
                                  <CheckCircle size={16} />
                                </IconButton>
                              </Tooltip>
                            )}
                            {report.status === REPORT_STATUS.APPROVED && report.paymentStatus !== PAYMENT_STATUS.PAID && (
                              <Tooltip title="Mark as Paid">
                                <IconButton
                                  size="small"
                                  color="info"
                                  onClick={() => handleMarkAsPaid(report.id)}
                                >
                                  <DollarSign size={16} />
                                </IconButton>
                              </Tooltip>
                            )}
                            {report.status !== REPORT_STATUS.APPROVED && (
                              <Tooltip title="Delete">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDeleteClick(report)}
                                >
                                  <Trash2 size={16} />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>{report.name || '-'}</TableCell>
                        <TableCell>
                          <Button
                            variant="text"
                            color="primary"
                            onClick={() => navigate(`/commission-reports/${report.id}`)}
                            sx={{ textTransform: 'none', p: 0, minWidth: 'auto' }}
                          >
                            {report.employerEmail}
                          </Button>
                        </TableCell>
                        <TableCell>{report.month}/{report.year}</TableCell>
                        <TableCell>{formatCurrency(report.totalAmount)}</TableCell>
                        <TableCell>
                          <Chip
                            label={REPORT_STATUS_LABELS[report.status]}
                            color={getStatusColor(report.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={PAYMENT_STATUS_LABELS[report.paymentStatus]}
                            size="small"
                            color={report.paymentStatus === 'Paid' ? 'success' : 'warning'}
                          />
                        </TableCell>
                        <TableCell>{formatDate(report.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Paper>

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={(event, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            </>
          )}
        </Box>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this commission report? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDeleteConfirm} color="error">
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create Report Dialog */}
        <CommissionReportCreateDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onSuccess={handleCreateSuccess}
        />
      </div>
    </div>
  );
};

export default CommissionReportListPage;

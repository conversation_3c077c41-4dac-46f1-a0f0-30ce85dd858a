import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert
} from '@mui/material';
import { ArrowLeft, Save } from 'lucide-react';
import {
  getCommissionReportById,
  updateCommissionReport
} from '../../services/commissionReportService';
import {
  PAYMENT_STATUS_LABELS,
  REPORT_STATUS,
  REPORT_STATUS_LABELS
} from '../../constants/collaboratorPolicy';
import { useToast } from "../../contexts/ToastContext";
import { formatCurrency } from '../../utils/currencyUtils';
import { formatDateForInput, formatDateForAPI } from '../../utils/dateUtils';

const CommissionReportEditPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showToast } = useToast();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState({
    employerEmail: '',
    name: '',
    month: '',
    year: '',
    totalAmount: '',
    status: '',
    notes: ''
  });

  const loadReportData = async () => {
    setLoading(true);
    try {
      const report = await getCommissionReportById(id);
      setFormData({
        employerEmail: report.employerEmail || '',
        name: report.name || '',
        month: report.month || '',
        year: report.year || '',
        totalAmount: report.totalAmount || '',
        status: report.status || '',
        paymentStatus: report.paymentStatus || '',
        paidDate: formatDateForInput(report.paidDate),
        notes: report.notes || ''
      });
      setError(null);
    } catch (error) {
      console.error('Failed to load commission report:', error);
      setError('Failed to load commission report');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadReportData();
    }
  }, [id]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      const updateData = {
        name: formData.name,
        status: formData.status,
        paymentStatus: formData.paymentStatus,
        paidDate: formatDateForAPI(formData.paidDate),
        notes: formData.notes
      };

      await updateCommissionReport(id, updateData);
      setSuccess(true);
      showToast('Commission report updated successfully', 'success');
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate(`/commission-reports/${id}`);
      }, 1500);
    } catch (error) {
      console.error('Failed to update commission report:', error);
      setError('Failed to update commission report');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Edit Commission Report
            </Typography>
            <Button
              variant="outlined"
              startIcon={<ArrowLeft />}
              onClick={() => navigate(`/commission-reports/${id}`)}
            >
              Back to Details
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Commission report updated successfully! Redirecting...
            </Alert>
          )}

          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Editable fields */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Report Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    variant="outlined"
                  />
                </Grid>

                {/* Read-only fields */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Employer Email"
                    value={formData.employerEmail}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Month"
                    value={formData.month}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Year"
                    value={formData.year}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Total Amount"
                    value={formatCurrency(formData.totalAmount)}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                {/* Editable fields */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      label="Status"
                      disabled={saving}
                    >
                      {Object.entries(REPORT_STATUS_LABELS).map(([key, label]) => (
                        <MenuItem key={key} value={key}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Payment Status</InputLabel>
                    <Select
                      value={formData.paymentStatus}
                      onChange={(e) => handleInputChange('paymentStatus', e.target.value)}
                      label="Payment Status"
                      disabled={saving}
                    >
                      {Object.entries(PAYMENT_STATUS_LABELS).map(([key, label]) => (
                        <MenuItem key={key} value={key}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    label="Paid Date"
                    type="date"
                    value={formData.paidDate}
                    onChange={(e) => handleInputChange('paidDate', e.target.value)}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Notes"
                    multiline
                    rows={4}
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    disabled={saving}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate(`/commission-reports/${id}`)}
                      disabled={saving}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<Save />}
                      disabled={saving}
                    >
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>

          {/* Information Note */}
          <Paper sx={{ p: 2, mt: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <Typography variant="body2">
              <strong>Note:</strong> Only the status and notes fields can be edited. 
              The employer email, month/year, and total amount are read-only to maintain data integrity.
            </Typography>
          </Paper>
        </Box>
      </div>
    </div>
  );
};

export default CommissionReportEditPage;

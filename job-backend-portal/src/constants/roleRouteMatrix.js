/**
 * Matrix defining which routes are accessible by which roles
 * Each route can be accessed by roles listed in its array
 */

export const ROLE_ROUTE_MATRIX = {
  // Admin routes
  '/admin-dashboard': ['admin'],
  '/employer-dashboard': ['employer'],
  '/user-dashboard': ['user'],
  '/hr-dashboard': ['hr'],
  '/jobs': ['admin', 'user', 'hr'],
 

  '/jobs/:id': ['admin', 'user', 'hr'],
  '/jobs/:id/view': ['admin', 'user', 'hr'],
  '/jobs/:id/edit': ['admin', 'hr'],
  '/applicants': ['admin', 'hr'],
  '/applicants/:id': ['admin', 'hr'],
  '/my-applicants': ['user'],
  '/users': ['admin', 'hr'],
  '/users/:id': ['admin', 'hr'],
  '/users/:id/view': ['admin', 'hr'],
  '/users/:id/edit': ['admin', 'hr'],
  '/options': ['admin'],
  '/options/:id': ['admin'],
  '/options/:id/view': ['admin'],
  '/options/:id/delete': ['admin'],

  // Employer Commission routes (admin only)
  '/employer-commissions': ['admin'],
  '/commission-reports': ['admin'],
  '/commission-reports/:id': ['admin'],
  '/commission-reports/:id/edit': ['admin'],
  '/employer-commissions/create': ['admin'],
  '/employer-commissions/:id': ['admin'],
  '/employer-commissions/:id/edit': ['admin'],
  '/contacts': ['admin', 'hr'],
  '/contacts/:id': ['admin', 'hr'],
  '/contacts/:id/view': ['admin', 'hr'],
  '/contacts/:id/edit': ['admin', 'hr'],
  '/categories': ['admin', 'hr'],
  '/categories/:id': ['admin', 'hr'],
  '/categories/:id/view': ['admin', 'hr'],
  '/categories/:id/edit': ['admin', 'hr'],
  '/blogs': ['admin', 'hr'],
  '/blogs/:id': ['admin', 'hr'],
  '/blogs/:id/view': ['admin', 'hr'],
  '/blogs/:id/edit': ['admin', 'hr'],
  '/company': ['employer'],
  '/profile': ['admin', 'employer', 'user', 'hr'],
  '/profile/edit': ['admin', 'employer', 'user', 'hr'],

  //employer routes
  '/employer/jobs': ['employer'],
  '/employer/jobs/:id/view': ['employer'],
  '/employer/applicants': ['employer'],
  '/employer/applicants/:id/view': ['employer'],
  '/employer/collaborator-policy': ['employer', 'admin'],

  // Public routes (accessible by all authenticated users)
  '/login': ['admin', 'employer', 'user', 'hr'],
  '/': ['admin', 'employer', 'user', 'hr']


};

/**
 * Helper function to check if a user with a given role can access a specific route
 * @param {string} path - The route path to check
 * @param {string} role - The user's role
 * @returns {boolean} - Whether the user can access the route
 */
export const canAccessRoute = (path, role) => {
  // console.log('Checking route access:', { path, role });

  // Find the matching route pattern
  const matchingRoute = Object.keys(ROLE_ROUTE_MATRIX).find(routePattern => {
    // Convert route pattern to regex
    const pattern = routePattern.replace(/:[^/]+/g, '[^/]+');
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(path);
  });

  // console.log('Matching route pattern:', matchingRoute);

  if (!matchingRoute) {
    // console.log('No matching route pattern found');
    return false;
  }

  // Get allowed roles for the route
  const allowedRoles = ROLE_ROUTE_MATRIX[matchingRoute];
  // console.log('Allowed roles for route:', allowedRoles);

  // Make role comparison case-insensitive
  const hasAccess = allowedRoles.some(
    allowedRole => allowedRole.toLowerCase() === role?.toLowerCase()
  );

  // console.log('User has access:', hasAccess);
  return hasAccess;
};
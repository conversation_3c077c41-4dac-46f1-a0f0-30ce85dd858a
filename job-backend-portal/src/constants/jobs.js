export const OPTION_TYPES = {
  JOB_TYPES: 'JOB_TYPES',
  JOB_CATEGORIES: 'JOB_CATEGORIES',
  JOB_LOCATIONS: 'JOB_LOCATIONS',
  JOB_SALARY_RANGES: 'JOB_SALARY_RANGES',
  JOB_WORKING_HOURS: 'JOB_WORKING_HOURS'
};

export const JOB_STATUS = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED'
};

export const SAMPLE_ADDRESSES = [
  '17A, Ngõ 603 Lạc Long <PERSON>ân, Xuân La, Tây <PERSON>, Hà <PERSON>ội',
  'Espoo, Finland'
];

export const DEFAULT_NEW_JOB = {
  title: "Software Engineer",
  description: "Join our team as a Software Engineer and work on cutting-edge projects...",
  category: "Engineering",
  type: "Full-time",
  location: "Hanoi, Vietnam",
  salary: "15,000,000 - 25,000,000 VND",
  responsibilities: [
    "Develop and maintain web applications",
    "Write clean, efficient code",
  ],
  technicalSkills: [
    "JavaScript",
    "React",
  ],
  softSkills: [
    "Strong problem-solving and analytical skills",
    "Excellent communication skills in English (preferred TOEIC 750+ or IELTS 6.0+)",
    "Ability to lead test automation strategies and mentor junior team members",
    "Proactive, self-motivated, and detail-oriented"
  ],
  benefits: [
    "13th-month salary bonus, business performance-based bonus, and holiday bonuses",
    "Health insurance card",
    "Annual HR orientation program, professional skill development, and course subsidies",
    "Quarterly team-building activities and annual company trips",
    "Opportunities to travel abroad for training and experience",
    "Participation in an English club taught by native speakers"
  ],
  status: "OPEN",
  featured: true,
  fullAddress: "17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội",
  code: `JOB-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, "0")}`,
  timeAlive: 30,
  workingHours: "9:00 AM - 6:00 PM"
};

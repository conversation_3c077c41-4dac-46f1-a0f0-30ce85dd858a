/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --color-background: #ffffff;
  --color-text: #000000;
  --color-text-secondary: #555555;
  --color-accent: #8a7fff;
  --color-accent-light: #e6e3ff;
  --color-border: #eeeeee;
  --color-button: #000000;
  --color-button-text: #ffffff;
  --color-button-hover: #333333;
  --color-card-bg: #ffffff;
  --color-success: #34a853;
}

.dark-theme {
  --color-background: #121212;
  --color-text: #ffffff;
  --color-text-secondary: #aaaaaa;
  --color-accent: #8a7fff;
  --color-accent-light: #2a2544;
  --color-border: #333333;
  --color-button: #ffffff;
  --color-button-text: #000000;
  --color-button-hover: #dddddd;
  --color-card-bg: #1e1e1e;
  --color-success: #34a853;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  color: var(--color-text);
  background-color: var(--color-background);
  transition: background-color 0.3s, color 0.3s;
  margin: 0;
  padding: 0;
  overflow-x: hidden; /* prevent horizontal scroll */
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4 {
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.3;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 1.5rem;
  color: var(--color-text);
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.2rem;
  color: var(--color-text);
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1rem;
  color: var(--color-text-secondary);
}

ul {
  list-style-position: inside;
  margin-bottom: 1rem;
  padding-left: 1rem;
}

li {
  margin-bottom: 0.5rem;
  color: var(--color-text-secondary);
}

a {
  color: var(--color-text);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--color-accent);
}

.text-accent {
  color: var(--color-accent);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-btn {
  background-color: var(--color-button);
  color: var(--color-button-text);
}

.primary-btn:hover {
  background-color: var(--color-button-hover);
}

.light-btn {
  background-color: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.light-btn:hover {
  background-color: var(--color-border);
}

.full-width {
  width: 100%;
}

/* Main Content */
.main-content {
  padding: 40px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.card {
  background-color: var(--color-card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid var(--color-border);
  transition: background-color 0.3s, border-color 0.3s;
}

.highlight-card {
  background-color: var(--color-accent-light);
}

/* Responsive styles */
@media (min-width: 768px) {
  h1 {
    font-size: 3rem;
  }

  .content-grid {
    grid-template-columns: 2fr 1fr;
  }
}

.app {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.background-tk {
  position: fixed; /* change from absolute to fixed */
  top: 0;
  right: -10%;
  width: 80%;
  height: 100vh;
  z-index: -1;
  opacity: 0.05;
  pointer-events: none;
}

.background-tk svg {
  width: 100%;
  height: 100%;
}

.logo-image {
  height: 30px;
  width: auto;
  transition: filter 0.3s ease;
  filter: invert(var(--logo-invert, 0));
}

.dark-theme .logo-image {
  
  --logo-invert: 0;
}

.logo-image.footer-logo {
  --logo-invert: 0;
}
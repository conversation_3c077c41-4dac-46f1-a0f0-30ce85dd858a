import { Routes, Route, Navigate } from 'react-router-dom';
import { UserProvider } from './contexts/UserContext';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useUser } from './contexts/UserContext';
import { CircularProgress, Box } from '@mui/material';
import Login from './pages/Login';
import AdminDashboard from './pages/AdminDashboard';
import JobsPage from './pages/jobs/JobsPage';
import JobDetailPage from './pages/jobs/JobDetailPage';
import JobEditPage from './pages/jobs/JobEditPage';
import JobApplicationsPage from './pages/jobs/JobApplicationsPage';
import JobApplicationDetailPage from './pages/jobs/JobApplicationDetailPage';
import ContactsPage from './pages/contacts/ContactsPage';
import ContactDetailPage from './pages/contacts/ContactDetailPage';
import ContactEditPage from './pages/contacts/ContactEditPage';
import CategoryPage from './pages/category/CategoryPage';
import CategoryDetailPage from './pages/category/CategoryDetailPage';
import CategoryEditPage from './pages/category/CategoryEditPage';
import BlogsPage from './pages/blogs/BlogsPage';
import BlogDetailPage from './pages/blogs/BlogDetailPage';
import BlogEditPage from './pages/blogs/BlogEditPage';
import OptionsPage from './pages/options/OptionsPage';
import OptionDetailPage from './pages/options/OptionDetailPage';
import OptionEditPage from './pages/options/OptionEditPage';
import OptionDeletePage from './pages/options/OptionDeletePage';
import EmployerJobPage from './pages/employer/EmployerJobPage';
import EmployerJobDetailPage from './pages/employer/EmployerJobDetailPage';
import EmployerJobApplicationsPage from './pages/employer/EmployerJobApplicationsPage';
import CollaboratorPolicyPage from './pages/employer/CollaboratorPolicyPage';
import EmployerApplicationDetailPage from './pages/employer/EmployerApplicationDetailPage';
import EmployerDashboard from './pages/employer/EmployerDashboard';
import EmployerCommissionListPage from './pages/employer-commissions/EmployerCommissionListPage';
import EmployerCommissionViewPage from './pages/employer-commissions/EmployerCommissionViewPage';
import EmployerCommissionEditPage from './pages/employer-commissions/EmployerCommissionEditPage';
import CommissionReportListPage from './pages/commission-reports/CommissionReportListPage';
import CommissionReportViewPage from './pages/commission-reports/CommissionReportViewPage';
import CommissionReportEditPage from './pages/commission-reports/CommissionReportEditPage';

import PrivateRoute from './components/PrivateRoute';
import Layout from './components/Layout';
import UsersPage from './pages/users/UsersPage';
import UserEditPage from './pages/users/UserEditPage';
import UserDetailPage from './pages/users/UserDetailPage';
import { getDashboardRoute } from './utils/routeUtils';
import { ToastProvider } from './contexts/ToastContext';

// PathTracker component to save the current path
function PathTracker() {
  const location = useLocation();
  const { user } = useUser();

  useEffect(() => {
    // Only save paths that are not login or root and user is authenticated
    if (location.pathname !== '/login' &&
        location.pathname !== '/' &&
        user.isAuthenticated) {
      localStorage.setItem('lastPath', location.pathname);
    }
  }, [location, user.isAuthenticated]);

  return null;
}

// Root redirect component
function RootRedirect() {
  const { user, loading } = useUser();

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!user.isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  const lastPath = localStorage.getItem('lastPath');
  if (lastPath) {
    return <Navigate to={lastPath} replace />;
  }

  // Redirect to role-specific dashboard
  const dashboardRoute = getDashboardRoute(user.role);
  return <Navigate to={dashboardRoute} replace />;
}

// AppContent component that uses the UserContext
function AppContent() {
  const { loading } = useUser();

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <PathTracker />
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/" element={<RootRedirect />} />

        {/* Admin Routes */}
        <Route
          path="/admin-dashboard"
          element={
            <PrivateRoute>
              <Layout>
                <AdminDashboard />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Category Routes */}
        <Route
          path="/categories"
          element={
            <PrivateRoute>
              <Layout>
                <CategoryPage />
              </Layout>
            </PrivateRoute>
          }
        />

        <Route
          path="/categories/:id"
          element={
            <PrivateRoute>
              <Layout>
                <CategoryEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/categories/:id/view"
          element={
            <PrivateRoute>
              <Layout>
                <CategoryDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Job Routes */}
        <Route
          path="/jobs"
          element={
            <PrivateRoute>
              <Layout>
                <JobsPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/jobs/:id'
          element={
            <PrivateRoute>
              <Layout>
                <JobEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/jobs/:id/view'
          element={
            <PrivateRoute>
              <Layout>
                <JobDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Applicant Routes */}
        <Route
          path='/applicants'
          element={
            <PrivateRoute>
              <Layout>
                <JobApplicationsPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/applicants/:id'
          element={
            <PrivateRoute>
              <Layout>
                <JobApplicationDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Contact Routes */}
        <Route
          path='/contacts'
          element={
            <PrivateRoute>
              <Layout>
                <ContactsPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/contacts/:id'
          element={
            <PrivateRoute>
              <Layout>
                <ContactEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/contacts/:id/view'
          element={
            <PrivateRoute>
              <Layout>
                <ContactDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* User Routes */}
        <Route
          path='/users'
          element={
            <PrivateRoute>
              <Layout>
                <UsersPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/users/:id'
          element={
            <PrivateRoute>
              <Layout>
                <UserEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/users/:id/view'
          element={
            <PrivateRoute>
              <Layout>
                <UserDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Options Routes */}
        <Route
          path='/options'
          element={
            <PrivateRoute>
              <Layout>
                <OptionsPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/options/:id'
          element={
            <PrivateRoute>
              <Layout>
                <OptionEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/options/:id/view'
          element={
            <PrivateRoute>
              <Layout>
                <OptionDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path='/options/:id/delete'
          element={
            <PrivateRoute>
              <Layout>
                <OptionDeletePage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Blog Routes */}
        <Route
          path="/blogs"
          element={
            <PrivateRoute>
              <Layout>
                <BlogsPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/blogs/:id"
          element={
            <PrivateRoute>
              <Layout>
                <BlogEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/blogs/:id/view"
          element={
            <PrivateRoute>
              <Layout>
                <BlogDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Employer Commission Routes */}
        <Route
          path="/employer-commissions"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerCommissionListPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer-commissions/create"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerCommissionEditPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer-commissions/:id"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerCommissionViewPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer-commissions/:id/edit"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerCommissionEditPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Commission Report Routes */}
        <Route
          path="/commission-reports"
          element={
            <PrivateRoute>
              <Layout>
                <CommissionReportListPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/commission-reports/:id"
          element={
            <PrivateRoute>
              <Layout>
                <CommissionReportViewPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/commission-reports/:id/edit"
          element={
            <PrivateRoute>
              <Layout>
                <CommissionReportEditPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Employer Routes */}
        <Route
          path="/employer-dashboard"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerDashboard />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer/jobs"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerJobPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer/jobs/:id/view"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerJobDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer/applicants"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerJobApplicationsPage />
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/employer/applicants/:id/view"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerApplicationDetailPage />
              </Layout>
            </PrivateRoute>
          }
        />

        <Route
          path="/employer/collaborator-policy"
          element={
            <PrivateRoute>
              <Layout>
                <CollaboratorPolicyPage />
              </Layout>
            </PrivateRoute>
          }
        />



        {/* User Routes */}
        <Route
          path="/user-dashboard"
          element={
            <PrivateRoute>
              <Layout>
                <div>User Dashboard</div>
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/jobs"
          element={
            <PrivateRoute>
              <Layout>
                <div>Find Jobs</div>
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/my-applicants"
          element={
            <PrivateRoute>
              <Layout>
                <div>My Applications</div>
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <PrivateRoute>
              <Layout>
                <div>My Profile</div>
              </Layout>
            </PrivateRoute>
          }
        />

        {/* HR Routes */}
        <Route
          path="/hr-dashboard"
          element={
            <PrivateRoute>
              <Layout>
                <div>HR Dashboard</div>
              </Layout>
            </PrivateRoute>
          }
        />
        <Route
          path="/jobs"
          element={
            <PrivateRoute>
              <Layout>
                <div>Find Jobs</div>
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Employer Job Routes */}
        <Route
          path="/employer/jobs"
          element={
            <PrivateRoute>
              <Layout>
                <EmployerJobPage />
              </Layout>
            </PrivateRoute>
          }
        />

        {/* Catch-all route for non-existent paths */}
        <Route
          path="*"
          element={
            <PrivateRoute>
              <Layout>
                <RootRedirect />
              </Layout>
            </PrivateRoute>
          }
        />
      </Routes>
    </>
  );
}

// Main App component that provides the UserContext
function App() {
  return (
    <ToastProvider>
      <UserProvider>
        <AppContent />
      </UserProvider>
    </ToastProvider>
  );
}

export default App;

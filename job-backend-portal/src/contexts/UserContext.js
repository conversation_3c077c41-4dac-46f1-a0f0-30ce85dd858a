import React, { createContext, useContext, useState, useEffect } from 'react';
import { getUserFromToken } from '../utils/jwtUtils';
import { getDashboardRoute } from '../utils/routeUtils';

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState({
    isAuthenticated: false,
    role: null,
    id: null,
    email: null,
    token: null
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for token in localStorage on initial load
    const token = localStorage.getItem('access_token');
    const role = localStorage.getItem('userRole');
    
    if (token && role) {
      const userData = getUserFromToken(token);
      if (userData) {
        setUser({
          isAuthenticated: true,
          role: userData.role,
          id: userData.id,
          email: userData.email,
          token: token
        });
      } else {
        // If token is invalid, clear it
        localStorage.removeItem('access_token');
        localStorage.removeItem('userRole');
      }
    }
    setLoading(false);
  }, []);

  const login = async (token) => {
    try {
      // Validate and decode the token
      const userData = getUserFromToken(token);
      
      if (!userData) {
        throw new Error('Invalid or expired token');
      }

      // Store the token and role
      localStorage.setItem('access_token', token);
      localStorage.setItem('userRole', userData.role);

      // Update user state
      setUser({
        isAuthenticated: true,
        role: userData.role,
        id: userData.id,
        email: userData.email,
        token: token
      });

      // Get the last attempted path or default to dashboard
      const lastPath = localStorage.getItem('lastPath');
      const dashboardRoute = getDashboardRoute(userData.role);
      
      // Clear the last path after using it
      localStorage.removeItem('lastPath');
      
      return lastPath || dashboardRoute;
    } catch (error) {
      console.error('Login error:', error);
      // Clear any invalid token
      localStorage.removeItem('access_token');
      localStorage.removeItem('userRole');
      localStorage.removeItem('lastPath');
      setUser({
        isAuthenticated: false,
        role: null,
        id: null,
        email: null,
        token: null
      });
      throw error;
    }
  };

  const logout = () => {
    // Clear all auth-related storage
    localStorage.removeItem('access_token');
    localStorage.removeItem('userRole');
    localStorage.removeItem('lastPath');
    setUser({
      isAuthenticated: false,
      role: null,
      id: null,
      email: null,
      token: null
    });
  };

  return (
    <UserContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}; 
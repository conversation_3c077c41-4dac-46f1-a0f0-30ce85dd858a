export interface Interview {
  id: number;
  scheduleRound1?: Date | null;
  interviewerRound1?: string | null;
  contentRound1?: string | null;
  reviewRound1?: string | null;
  resultRound1?: boolean | null;
  scheduleRound2?: Date | null;
  interviewerRound2?: string | null;
  contentRound2?: string | null;
  reviewRound2?: string | null;
  resultRound2?: boolean | null;
  jobApplicationId: number;
}

export interface Round1Data {
  scheduleRound1?: Date;
  interviewerRound1?: string;
  contentRound1?: string;
  reviewRound1?: string;
  resultRound1?: boolean;
  sendEmailRound1?: boolean;
}

export interface Round2Data {
  scheduleRound2?: Date;
  interviewerRound2?: string;
  contentRound2?: string;
  reviewRound2?: string;
  resultRound2?: boolean;
  sendEmailRound2?: boolean;
}

export type CreateInterviewDto = Omit<Interview, 'id'>;
export type UpdateInterviewDto = Partial<CreateInterviewDto>; 
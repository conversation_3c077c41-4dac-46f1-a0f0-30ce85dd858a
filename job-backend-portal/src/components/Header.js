import React from 'react';
import { Box, Typography, IconButton, useMediaQuery, useTheme } from '@mui/material';
import { User, Menu } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import './Header.css';

function Header({ onMobileMenuToggle }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useUser();

  const capitalizeFirstLetter = (string) => {
    return string?.charAt(0).toUpperCase() + string?.slice(1).toLowerCase() || '';
  };

  return (
    <header className="app-header">
      {isMobile && (
        <IconButton 
          edge="start" 
          color="inherit" 
          aria-label="menu"
          onClick={onMobileMenuToggle}
          sx={{ mr: 2 }}
        >
          <Menu size={24} />
        </IconButton>
      )}
      <Typography 
        variant="h5" 
        sx={{ 
          fontWeight: 600,
          fontSize: { xs: '1.2rem', sm: '1.5rem' }
        }}
      >
        Job Portal CMS
      </Typography>
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: { xs: 1, sm: 2 }
        }}
      >
        <User size={20} />
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: { xs: 'flex-start', sm: 'flex-end' } }}>
          <Typography 
            variant="subtitle2"
            sx={{ 
              display: { xs: 'none', sm: 'block' }
            }}
          >
            {capitalizeFirstLetter(user.role)} Account
          </Typography>
        </Box>
      </Box>
    </header>
  );
}

export default Header;

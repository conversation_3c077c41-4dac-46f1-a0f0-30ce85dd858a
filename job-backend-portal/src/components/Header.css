.header {
  background-color: var(--color-background);
  padding: 20px 0;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.3s, border-color 0.3s;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--color-text);
}

.logo-link {
  text-decoration: none;
}

.main-nav {
  display: none;
}

.main-nav a {
  margin-left: 30px;
  color: var(--color-text);
  font-weight: 500;
  position: relative;
  text-decoration: none;
}

.main-nav a:hover {
  color: var(--color-text);
}

.main-nav a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-text);
  transition: width 0.3s;
}

.main-nav a:hover::after,
.main-nav a.active::after {
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.talk-btn {
  border-radius: 30px;
  padding: 10px 24px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.arrow {
  margin-left: 8px;
  transition: transform 0.3s;
}

.talk-btn:hover .arrow {
  transform: translateX(4px);
}

@media (min-width: 768px) {
  .main-nav {
    display: flex;
  }
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1100;
  transition: all 0.3s ease;
}

@media (max-width: 600px) {
  .app-header {
    padding: 0 16px;
  }
}

.app-header svg {
  color: #2C3E50;
}

/* When sidebar is collapsed */
.sidebar.collapsed + div .app-header {
  left: 0px;
}

/* Mobile styles */
@media (max-width: 768px) {
  .app-header {
    left: 0px;
  }
}

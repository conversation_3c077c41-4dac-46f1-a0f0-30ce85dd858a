import { useState } from 'react';
import {
  Modal,
  Box,
  Typography,
  IconButton,
  CircularProgress,
  Alert
} from '@mui/material';
import { X } from 'lucide-react';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  height: '80%',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 4,
  borderRadius: 1,
  display: 'flex',
  flexDirection: 'column'
};

const CVViewerModal = ({ open, onClose, cvUrl, fileName }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const handleIframeError = () => {
    setError(true);
    setLoading(false);
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="cv-viewer-modal"
      aria-describedby="modal-to-view-cv"
    >
      <Box sx={style}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            {fileName || 'CV Document'}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <X size={20} />
          </IconButton>
        </Box>
        
        <Box sx={{ 
          flexGrow: 1, 
          position: 'relative',
          bgcolor: '#f5f5f5',
          borderRadius: 1,
          overflow: 'hidden'
        }}>
          {loading && !error && (
            <Box sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)'
            }}>
              <CircularProgress />
            </Box>
          )}
          
          {error ? (
            <Box sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              p: 2
            }}>
              <Alert severity="error">
                Failed to load the CV. Please try again or contact support if the problem persists.
              </Alert>
            </Box>
          ) : (
            <iframe
              src={cvUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none'
              }}
              onLoad={() => setLoading(false)}
              onError={handleIframeError}
              title="CV Viewer"
            />
          )}
        </Box>
      </Box>
    </Modal>
  );
};

export default CVViewerModal; 
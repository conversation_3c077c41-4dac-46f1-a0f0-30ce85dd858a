"use client"

import { useState } from "react"
import "./SimpleDatePicker.css"

function SimpleDatePicker({ onChange, value, label }) {
  const [selectedDate, setSelectedDate] = useState(value || "")

  const handleChange = (e) => {
    const newDate = e.target.value
    setSelectedDate(newDate)
    if (onChange) {
      onChange(newDate)
    }
  }

  return (
    <div className="date-picker-container">
      {label && <label className="date-picker-label">{label}</label>}
      <input type="date" className="date-picker-input" value={selectedDate} onChange={handleChange} />
    </div>
  )
}

export default SimpleDatePicker

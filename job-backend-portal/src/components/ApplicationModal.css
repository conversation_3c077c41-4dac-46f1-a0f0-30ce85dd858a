.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  padding: 20px;
  backdrop-filter: blur(4px);
  overflow: hidden;
}

.modal-content {
  background-color: var(--color-card-bg, #ffffff);
  border-radius: 12px;
  width: 100%;
  max-width: 520px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  animation: modalFadeIn 0.3s ease-out;
  position: relative;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid var(--color-border, #eaeaea);
  background-color: var(--color-card-bg, #ffffff);
  border-radius: 12px 12px 0 0;
  width: 100%;
  box-sizing: border-box;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--color-text, #111827);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--color-text-secondary, #6b7280);
  padding: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: var(--color-border, #f3f4f6);
  color: var(--color-text, #111827);
}

.application-form {
  padding: 24px 28px;
  flex: 1;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.form-group {
  margin-bottom: 24px;
  width: 100%;
  box-sizing: border-box;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  color: var(--color-text, #111827);
  width: 100%;
  box-sizing: border-box;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group select.select-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--color-border, #e5e7eb);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  background-color: var(--color-card-bg, #ffffff);
  color: var(--color-text, #111827);
  box-sizing: border-box;
}

.form-group input:hover,
.form-group select.select-input:hover {
  border-color: var(--color-border-hover, #d1d5db);
}

.form-group input:focus,
.form-group select.select-input:focus {
  border-color: var(--color-accent, #2563eb);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.form-group select.select-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.form-group select.select-input:invalid {
  color: var(--color-text-secondary, #6b7280);
}

.form-group select.select-input option {
  color: var(--color-text, #111827);
  padding: 8px;
}

.file-input-container {
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 8px;
  width: 100%;
  box-sizing: border-box;
}

.file-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-button {
  background-color: var(--color-accent-light, #eff6ff);
  border: 1px solid var(--color-accent, #2563eb);
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-accent, #2563eb);
}

.file-name {
  margin-left: 12px;
  color: var(--color-text-secondary, #6b7280);
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.file-input-button:hover {
  background-color: var(--color-accent-light-hover, #dbeafe);
}

.form-actions {
  position: sticky;
  bottom: 0;
  background-color: var(--color-card-bg, #ffffff);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  border-top: 1px solid var(--color-border, #eaeaea);
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
}

.cancel-btn {
  background: none;
  border: 1px solid var(--color-border, #e5e7eb);
  color: var(--color-text, #111827);
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background-color: var(--color-border, #f3f4f6);
  border-color: var(--color-border-hover, #d1d5db);
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-accent, #2563eb);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.submit-btn:hover {
  background-color: var(--color-accent-hover, #1d4ed8);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.success-message {
  padding: 48px 28px;
  text-align: center;
}

.success-icon {
  width: 64px;
  height: 64px;
  background-color: var(--color-success, #22c55e);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin: 0 auto 24px;
  box-shadow: 0 0 0 8px var(--color-success-light, #dcfce7);
}

.success-message h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text, #111827);
  margin-bottom: 12px;
}

.success-message p {
  color: var(--color-text-secondary, #6b7280);
  font-size: 1rem;
  line-height: 1.5;
}

.error-message {
  color: var(--color-error, #dc2626);
  font-size: 0.875rem;
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-message::before {
  content: "⚠";
  font-size: 1rem;
}

.form-group input:invalid {
  border-color: var(--color-error, #dc2626);
}

@media (max-width: 480px) {
  .modal-content {
    max-height: 90vh;
  }

  .modal-header {
    padding: 20px 24px;
  }

  .application-form {
    padding: 20px 24px;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 8px;
  }

  .form-actions button {
    width: 100%;
  }
}

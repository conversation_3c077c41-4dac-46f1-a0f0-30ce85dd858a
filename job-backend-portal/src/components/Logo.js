"use client"

import { useEffect, useState } from "react"

function Logo({ className, type }) {
  const [isDarkMode, setIsDarkMode] = useState(false)

  useEffect(() => {
    // Initial check
    setIsDarkMode(document.documentElement.classList.contains("dark-theme"))

    // Create a MutationObserver to watch for class changes on documentElement
    const observer = new MutationObserver(() => {
      setIsDarkMode(document.documentElement.classList.contains("dark-theme"))
    })

    observer.observe(document.documentElement, { attributes: true })

    return () => observer.disconnect()
  }, [])

  return (
    <div className={`logo ${className || ""}`}>
      {type === 'header' ? (
      <img
        src={isDarkMode ? "/images/tekai-logo-white.png" : "/images/tekai-logo-black.png"}
        alt="Tekai"
        className="logo-image"
      />
      ) : (<img
        src={isDarkMode ? "/images/tekai-logo-black.png" : "/images/tekai-logo-white.png"}
        alt="Tekai"
        className="logo-image"
      />)}
    </div>
  )
}

export default Logo

"use client"

import { useState } from "react"
import "./ApplicationModal.css"
import { submitJobApplication } from '../services/jobApplicationService'
import { LEVEL_LABELS, ENGLISH_SKILL_LABELS } from "../constants/collaboratorPolicy"

function ApplicationModal({ showRefPerson = true, isOpen, onClose, openJobs = [] }) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phoneNumber: "",
    refPerson: "",
    cv: null,
    jobId: openJobs.length === 1 ? openJobs[0].id : "",
    candidateLevel: "",
    englishSkill: "",
  })
  const [fileName, setFileName] = useState("No file chosen")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [phoneError, setPhoneError] = useState("")

  if (!isOpen) return null

  const handleChange = (e) => {
    const { name, value } = e.target

    if (name === 'phoneNumber') {
      // Only allow numbers
      const numbersOnly = value.replace(/[^\d]/g, '')
      // Limit to 10 digits
      const truncated = numbersOnly.slice(0, 10)

      setFormData({
        ...formData,
        [name]: truncated,
      })

      // Validate phone number
      if (truncated.length > 0 && truncated.length !== 10) {
        setPhoneError("Phone number must be 10 digits")
      } else {
        setPhoneError("")
      }
    } else {
      setFormData({
        ...formData,
        [name]: value,
      })
    }
  }

  const handleFileChange = (e) => {
    if (e.target.files[0]) {
      setFormData({
        ...formData,
        cv: e.target.files[0],
      })
      setFileName(e.target.files[0].name)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Additional validation before submission
    if (formData.phoneNumber.length !== 10) {
      setPhoneError("Phone number must be 10 digits")
      return
    }

    setIsSubmitting(true)

    const selectedJob = openJobs.find(job => job.id === formData.jobId)
    if (!selectedJob) {
      console.error('No job selected')
      setIsSubmitting(false)
      return
    }

    const formDataToSend = new FormData()
    formDataToSend.append('name', formData.name)
    formDataToSend.append('email', formData.email)
    formDataToSend.append('phoneNumber', formData.phoneNumber)
    formDataToSend.append('refPerson', formData.refPerson)
    formDataToSend.append('cv', formData.cv)
    formDataToSend.append('jobId', selectedJob.id)
    formDataToSend.append('jobTitle', selectedJob.title)
    formDataToSend.append('timestamp', new Date().toISOString())
    formData.candidateLevel && formDataToSend.append('candidateLevel', formData.candidateLevel)
    formData.englishSkill && formDataToSend.append('englishSkill', formData.englishSkill)

    try {
      await submitJobApplication(formDataToSend)
      setIsSubmitting(false)
      setSubmitSuccess(true)

      setTimeout(() => {
        setSubmitSuccess(false)
        onClose()
        setFormData({
          name: "",
          email: "",
          phoneNumber: "",
          refPerson: "",
          cv: null,
          jobId: "",
          candidateLevel: "",
          englishSkill: "",
        })
        setFileName("No file chosen")
        setPhoneError("")
      }, 2000)
    } catch (error) {
      console.error('Error submitting form:', error)
      setIsSubmitting(false)
    }
  }

  const selectedJob = openJobs.find(job => job.id === formData.jobId)

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Apply for {selectedJob ? selectedJob.title : 'Job Position'}</h2>
          <button className="close-button" onClick={onClose}>
            &times;
          </button>
        </div>

        {submitSuccess ? (
          <div className="success-message">
            <div className="success-icon">✓</div>
            <h3>Application Submitted!</h3>
            <p>Thank you for your interest. We'll be in touch soon.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="application-form">
            <div className="form-group">
              <label htmlFor="jobId">Select Position *</label>
              <select
                id="jobId"
                name="jobId"
                value={formData.jobId}
                onChange={handleChange}
                required
                className="select-input"
              >
                <option value="">Select a position</option>
                {openJobs.map((job) => (
                  <option key={job.id} value={job.id}>
                    {job.title} - {job.location}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder="Enter your full name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email Address *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder="Enter your email address"
              />
            </div>

            <div className="form-group">
              <label htmlFor="phoneNumber">Phone Number * (10 digits)</label>
              <input
                type="tel"
                id="phoneNumber"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                required
                placeholder="Enter your phone number"
                pattern="[0-9]{10}"
                maxLength={10}
              />
              {phoneError && <div className="error-message">{phoneError}</div>}
            </div>

            { showRefPerson && <div className="form-group">
              <label htmlFor="refPerson">Reference Person</label>
              <input
                type="text"
                id="refPerson"
                name="refPerson"
                value={formData.refPerson}
                onChange={handleChange}
                placeholder="Who referred you to this position? (optional)"
              />
            </div> }

            <div className="form-group">
              <label htmlFor="cv">Upload CV (PDF, DOC, DOCX) *</label>
              <div className="file-input-container">
                <div className="file-input-button">Choose File</div>
                <span className="file-name">{fileName}</span>
                <input
                  type="file"
                  id="cv"
                  name="cv"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  required
                  className="file-input"
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="candidateLevel">Candidate Level</label>
              <select
                id="candidateLevel"
                name="candidateLevel"
                value={formData.candidateLevel}
                onChange={handleChange}
                className="select-input"
              >
                <option value="">Select a candidate level (optional)</option>
                {Object.entries(LEVEL_LABELS).map(([key, label]) => (
                  <option key={key} value={key}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="englishSkill">English Skill</label>
              <select
                id="englishSkill"
                name="englishSkill"
                value={formData.englishSkill}
                onChange={handleChange}
                className="select-input">
                <option value="">Select an english skill (optional)</option>
                {Object.entries(ENGLISH_SKILL_LABELS).map(([key, label]) => (
                  <option key={key} value={key}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-actions">
              <button type="button" className="btn cancel-btn" onClick={onClose}>
                Cancel
              </button>
              <button
                type="submit"
                className="btn primary-btn submit-btn"
                disabled={isSubmitting || phoneError}
              >
                {isSubmitting ? "Submitting..." : "Submit Application"}{" "}
                {!isSubmitting && <span className="arrow">→</span>}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}

export default ApplicationModal

import { Navigate, useLocation } from 'react-router-dom';
import { useUser } from '../contexts/UserContext';
import { canAccessRoute } from '../constants/roleRouteMatrix';
import { getDashboardRoute, shouldRedirectToDashboard } from '../utils/routeUtils';

function PrivateRoute({ children }) {
  const { user } = useUser();
  const location = useLocation();
  const currentPath = location.pathname;
  
  // console.log('PrivateRoute - User:', user);
  // console.log('PrivateRoute - Current Path:', currentPath);
  
  if (!user.isAuthenticated) {
    // console.log('PrivateRoute - Not authenticated, redirecting to login');
    // Save the attempted path before redirecting to login
    if (currentPath !== '/login') {
      localStorage.setItem('lastPath', currentPath);
    }
    return <Navigate to="/login" replace />;
  }

  // Check if user has access to the current route
  const hasAccess = canAccessRoute(currentPath, user.role);
  // console.log('PrivateRoute - Has access to route:', hasAccess);
  
  if (!hasAccess) {
    const dashboardRoute = getDashboardRoute(user.role);
    console.log('PrivateRoute - No access, redirecting to dashboard:', dashboardRoute);
    localStorage.setItem('lastPath', dashboardRoute);
    return <Navigate to={dashboardRoute} replace />;
  }

  // Check if user should be redirected to their dashboard
  if (shouldRedirectToDashboard(currentPath, user.role)) {
    const dashboardRoute = getDashboardRoute(user.role);
    //  console.log('PrivateRoute - Redirecting to dashboard:', dashboardRoute);
    localStorage.setItem('lastPath', dashboardRoute);
    return <Navigate to={dashboardRoute} replace />;
  }

  return children;
}

export default PrivateRoute;
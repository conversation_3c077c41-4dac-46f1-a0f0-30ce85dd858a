import { useUser } from '../../contexts/UserContext';
import { hasPermission } from '../../utils/roleUtils';

/**
 * Higher-order component to conditionally render content based on user role
 * @param {Object} props - Component props
 * @param {string|Array<string>} props.allowedRoles - The role(s) that are allowed to see the content
 * @param {React.ReactNode} props.children - The content to render if user has permission
 * @param {React.ReactNode} [props.fallback] - Optional content to render if user doesn't have permission
 * @returns {React.ReactNode} - The rendered content based on user's role
 */
const RoleBasedContent = ({ allowedRoles, children, fallback = null }) => {
  const { user } = useUser();
  
  if (hasPermission(user.role, allowedRoles)) {
    return children;
  }
  
  return fallback;
};

export default RoleBasedContent; 
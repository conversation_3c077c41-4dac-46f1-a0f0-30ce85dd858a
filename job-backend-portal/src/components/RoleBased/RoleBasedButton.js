import { Button } from '@mui/material';
import { useUser } from '../../contexts/UserContext';
import { hasPermission } from '../../utils/roleUtils';

/**
 * Higher-order component to conditionally render a button based on user role
 * @param {Object} props - Component props
 * @param {string|Array<string>} props.allowedRoles - The role(s) that are allowed to see the button
 * @param {Object} props.buttonProps - Props to pass to the button component
 * @param {React.ReactNode} props.children - The button content
 * @returns {React.ReactNode} - The rendered button or null
 */
const RoleBasedButton = ({ allowedRoles, buttonProps, children }) => {
  const { user } = useUser();
  
  if (!hasPermission(user.role, allowedRoles)) {
    return null;
  }
  
  return (
    <Button {...buttonProps}>
      {children}
    </Button>
  );
};

export default RoleBasedButton; 
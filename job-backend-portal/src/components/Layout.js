import React, { useState } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';
import { Box, CircularProgress } from '@mui/material';

function Layout({ children }) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleSidebarCollapse = (collapsed) => {
    setSidebarCollapsed(collapsed);
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar onCollapse={handleSidebarCollapse} />
      <Box sx={{ 
        flexGrow: 1, 
        display: 'flex', 
        flexDirection: 'column',
        minHeight: '100vh',
        overflow: 'hidden',
        marginLeft: { xs: 0, sm: sidebarCollapsed ? '80px' : '280px' },
        transition: 'margin-left 0.3s ease'
      }}>
        <Header />
        <Box 
          component="main" 
          sx={{ 
            flexGrow: 1, 
            p: 3, 
            overflow: 'auto',
            backgroundColor: 'background.default',
            marginTop: '64px', // Match header height
            position: 'relative'
          }}
        >
          <React.Suspense 
            fallback={
              <Box 
                sx={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  alignItems: 'center',
                  height: '100%',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255,255,255,0.8)'
                }}
              >
                <CircularProgress />
              </Box>
            }
          >
            {children}
          </React.Suspense>
        </Box>
        <Footer />
      </Box>
    </Box>
  );
}

export default Layout;
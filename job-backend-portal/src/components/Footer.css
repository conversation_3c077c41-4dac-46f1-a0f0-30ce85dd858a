.footer {
  background-color: var(--color-text);
  color: var(--color-background);
  padding: 60px 0 30px;
  width: 100%;
  transition: width 0.3s ease;
}

.sidebar.collapsed + .admin-content .footer {
  width: calc(100% - 80px);
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-column h3 {
  color: var(--color-background);
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.footer-column p {
  color: #bbb;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #bbb;
}

.footer-links a:hover {
  color: var(--color-background);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-background);
  transition: background-color 0.3s;
}

.social-icons a:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #999;
  font-size: 0.9rem;
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.cms-footer {
  background-color: var(--color-card-bg);
  border-top: 1px solid var(--color-border);
  padding: 12px 24px;
  width: 100%;
  transition: width 0.3s ease;
  margin-left: 250px;
}

.sidebar.collapsed + .admin-content .cms-footer {
  width: calc(100% - 80px);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-right a {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-right a:hover {
  color: var(--color-accent);
}

.separator {
  color: var(--color-border);
}

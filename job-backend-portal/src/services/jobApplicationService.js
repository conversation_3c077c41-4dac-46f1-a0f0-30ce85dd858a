import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllJobApplications = async (params = {}) => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.JOB_APPLICATIONS, { params });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getJobApplicationById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.JOB_APPLICATIONS}/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const createJobApplication = async (applicationData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.JOB_APPLICATIONS, applicationData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateJobApplication = async (id, applicationData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.JOB_APPLICATIONS}/${id}`, applicationData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const rejectJobApplication = async (id) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.JOB_APPLICATIONS}/${id}/reject`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const receivedJobApplication = async (id) => {
  try {
    const response = await axiosInstance.post(`${API_ENDPOINTS.JOB_APPLICATIONS}/${id}/send-received-email`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteJobApplication = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_ENDPOINTS.JOB_APPLICATIONS}/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const submitJobApplication = async (formData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.JOB_APPLICATIONS, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const downloadCV = async (applicationId) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.JOB_APPLICATIONS}/${applicationId}/download-cv`, {
      headers: {
        'Accept': 'application/pdf',
      },
      responseType: 'blob'
    });

    // Axios automatically returns response.data as the blob
    const blob = new Blob([response.data], { type: 'application/pdf' });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error downloading CV:', error);
    throw new Error(error.response?.data?.message || 'Failed to download CV');
  }
};

export const getApplicantStats = async () => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.JOB_APPLICATIONS}/statistics/monthly`);
    return response.data;
  } catch (error) {
    console.error('Error fetching monthly applicant stats:', error);
    throw error;
  }
};

export const getJobApplicationsForCommission = async (refPerson = '') => {
  try {
    const params = refPerson ? { refPerson } : {};
    const response = await axiosInstance.get(`${API_ENDPOINTS.JOB_APPLICATIONS}/for-commission`, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching job applications for commission:', error);
    throw error;
  }
};
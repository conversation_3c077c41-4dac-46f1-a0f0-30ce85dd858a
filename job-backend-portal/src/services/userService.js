import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllUsers = async (params = {}) => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.USERS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

export const getUserById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.USERS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

export const createUser = async (userData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.USERS, userData);
    return response.data;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

export const updateUser = async (id, userData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.USERS}/${id}`, userData);
    return response.data;
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

export const updatePassword = async (id, passwordData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.USERS}/${id}/change-password`, {
      currentPassword: passwordData.currentPassword,
      newPassword: passwordData.newPassword
    });
    return response.data;
  } catch (error) {
    console.error('Error updating password:', error);
    throw error;
  }
};

export const deleteUser = async (id) => {
  try {
    await axiosInstance.delete(`${API_ENDPOINTS.USERS}/${id}`);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};
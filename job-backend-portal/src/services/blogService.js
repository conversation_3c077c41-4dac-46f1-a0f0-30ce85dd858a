import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllBlogs = async () => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOGS);
    return response.data;
  } catch (error) {
    console.error('Error fetching blogs:', error);
    throw error;
  }
};

export const getBlogById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOGS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching blog:', error);
    throw error;
  }
};

export const createBlog = async (blogData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOGS, blogData);
    return response.data;
  } catch (error) {
    console.error('Error creating blog:', error);
    throw error;
  }
};

export const updateBlog = async (id, blogData) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.BLOGS}/${id}`, blogData);
    return response.data;
  } catch (error) {
    console.error('Error updating blog:', error);
    throw error;
  }
};

export const deleteBlog = async (id) => {
  try {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOGS}/${id}`);
  } catch (error) {
    console.error('Error deleting blog:', error);
    throw error;
  }
}; 
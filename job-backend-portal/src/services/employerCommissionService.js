import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllEmployerCommissions = async (params = {}) => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.EMPLOYER_COMMISSIONS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching employer commissions:', error);
    throw error;
  }
};

export const getEmployerCommissionById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.EMPLOYER_COMMISSIONS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching employer commission by id:', error);
    throw error;
  }
};

export const getCommissionsByEmployerAndMonth = async (employerEmail, month, year) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.EMPLOYER_COMMISSIONS}/commissions-by-employer`, {
      params: { employerEmail, month, year }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching commissions by employer and month:', error);
    throw error;
  }
};

export const createEmployerCommission = async (commissionData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.EMPLOYER_COMMISSIONS, commissionData);
    return response.data;
  } catch (error) {
    console.error('Error creating employer commission:', error);
    throw error;
  }
};

export const updateEmployerCommission = async (id, commissionData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.EMPLOYER_COMMISSIONS}/${id}`, commissionData);
    return response.data;
  } catch (error) {
    console.error('Error updating employer commission:', error);
    throw error;
  }
};

export const deleteEmployerCommission = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_ENDPOINTS.EMPLOYER_COMMISSIONS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting employer commission:', error);
    throw error;
  }
};

export const copyCommissionToOfficial = async (id) => {
  try {
    const response = await axiosInstance.post(`${API_ENDPOINTS.EMPLOYER_COMMISSIONS}/${id}/copy-to-official`);
    return response.data;
  } catch (error) {
    console.error('Error copying commission to official:', error);
    throw error;
  }
};

export const approveCommission = async (id, approveData = {}) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.EMPLOYER_COMMISSIONS}/${id}/approve`, approveData);
    return response.data;
  } catch (error) {
    console.error('Error approving commission:', error);
    throw error;
  }
};

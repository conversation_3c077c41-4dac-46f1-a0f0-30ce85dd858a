import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllCategories = async () => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.CATEGORIES);
    return response.data;
  } catch (error) {
    console.error('Error fetching all categories:', error);
    throw error;
  }
};

export const getCategoryById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.CATEGORIES}/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const createCategory = async (categoryData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.CATEGORIES, categoryData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateCategory = async (id, categoryData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.CATEGORIES}/${id}`, categoryData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteCategory = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_ENDPOINTS.CATEGORIES}/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
}; 
import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllJobs = async (params = {}) => {
  try {
    const { page, limit, search, category, status, sortBy, sortOrder } = params;
    const queryParams = new URLSearchParams();

    if (page) queryParams.append('page', page);
    if (limit) queryParams.append('limit', limit);
    if (search) queryParams.append('search', search);
    if (category && category !== 'All') queryParams.append('category', category);
    if (status) queryParams.append('status', status);
    if (sortBy) queryParams.append('sortBy', sortBy);
    if (sortOrder) queryParams.append('sortOrder', sortOrder);

    const url = `${API_ENDPOINTS.JOBS}?${queryParams.toString()}`;
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getJobById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.JOBS}/${id}`)
    return response.data
  } catch (error) {
    throw error
  }
}

export const createJob = async (jobData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.JOBS, jobData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateJob = async (id, jobData) => {
  console.log("jobData", jobData);
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.JOBS}/${id}`, jobData);
    return response.data;
  } catch (error) {
    console.error('Error updating job:', error);
    throw error;
  }
};

export const getJobStatistics = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.JOBS}/${id}/statistics`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';
import { EMPLOYMENT_TYPE } from '../constants/collaboratorPolicy';

export const getAllPaymentRates = async (params = {}) => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.PAYMENT_RATES, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching payment rates:', error);
    throw error;
  }
};

export const getPaymentRateById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.PAYMENT_RATES}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching payment rate by id:', error);
    throw error;
  }
};

export const createPaymentRate = async (rateData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.PAYMENT_RATES, rateData);
    return response.data;
  } catch (error) {
    console.error('Error creating payment rate:', error);
    throw error;
  }
};

export const updatePaymentRate = async (id, rateData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.PAYMENT_RATES}/${id}`, rateData);
    return response.data;
  } catch (error) {
    console.error('Error updating payment rate:', error);
    throw error;
  }
};

export const deletePaymentRate = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_ENDPOINTS.PAYMENT_RATES}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting payment rate:', error);
    throw error;
  }
};

export const getPaymentRateByCriteria = async (employmentType, candidateLevel, englishSkill, commissionPhase) => {
  try {
    const params = {
      employmentType,
      commissionPhase
    };

    if (employmentType == EMPLOYMENT_TYPE.FULLTIME) {
      params.candidateLevel = candidateLevel;
      params.englishSkill = englishSkill;
    }

    const response = await axiosInstance.get(`${API_ENDPOINTS.PAYMENT_RATES}/by-criteria`, { params });

    return response.data || null;
  } catch (error) {
    console.error('Error fetching payment rate by criteria:', error);
    throw error;
  }
};

export const seedPaymentRates = async () => {
  try {
    const response = await axiosInstance.post(`${API_ENDPOINTS.PAYMENT_RATES}/seed`);
    return response.data;
  } catch (error) {
    console.error('Error seeding payment rates:', error);
    throw error;
  }
};
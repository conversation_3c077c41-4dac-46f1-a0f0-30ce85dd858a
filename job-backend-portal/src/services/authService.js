import axios from 'axios';
import { API_ENDPOINTS } from '../constants/api';
import { getUserFromToken } from '../utils/jwtUtils';

export const login = async (loginData) => {
  try {
    const response = await axios.post(`${API_ENDPOINTS.AUTH}/login`, loginData);
    
    // Ensure we have a token in the response
    if (!response.data.access_token) {
      throw new Error('No token received from server');
    }

    // Get user data from token
    const token = response.data.access_token;
    const userData = getUserFromToken(token);

    if (!userData) {
      throw new Error('Invalid token or missing user data');
    }

    const result = {
      token: token,
      user: {
        isAuthenticated: true,
        role: userData.role,
        id: userData.id,
        email: userData.email,
        token: token
      }
    };
    
    // Store the token in localStorage
    localStorage.setItem('access_token', token);
    localStorage.setItem('userRole', userData.role);
    
    return result;
  } catch (error) {
    // Clear any stored tokens on error
    localStorage.removeItem('access_token');
    localStorage.removeItem('userRole');
    throw error;
  }
};

export const logout = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('userRole');
  localStorage.removeItem('lastPath');
};

export const getCurrentUser = async () => {
  try {
    const token = localStorage.getItem('token');
    if (!token) return null;

    const response = await axios.get(`${API_ENDPOINTS.AUTH}/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return {
      ...response.data,
      role: response.data.role || 'admin' // Default to 'admin' if role is not provided
    };
  } catch (error) {
    console.error('Error fetching current user:', error);
    return null;
  }
};
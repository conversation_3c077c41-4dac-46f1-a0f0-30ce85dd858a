import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllContacts = async () => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.CONTACT);
    return response.data;
  } catch (error) {
    console.error('Error fetching contacts:', error);
    throw error;
  }
};

export const getContactById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.CONTACT}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching contact:', error);
    throw error;
  }
};

export const createContact = async (contactData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.CONTACT, contactData);
    return response.data;
  } catch (error) {
    console.error('Error creating contact:', error);
    throw error;
  }
};

export const updateContact = async (id, contactData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.CONTACT}/${id}`, contactData);
    return response.data;
  } catch (error) {
    console.error('Error updating contact:', error);
    throw error;
  }
};

export const deleteContact = async (id) => {
  try {
    await axiosInstance.delete(`${API_ENDPOINTS.CONTACT}/${id}`);
  } catch (error) {
    console.error('Error deleting contact:', error);
    throw error;
  }
}; 
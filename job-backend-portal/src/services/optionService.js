import { API_ENDPOINTS } from '../constants/api';
import axiosInstance from '../utils/axios';

export const getOptions = async (params) => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.OPTIONS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching options:', error);
    throw error;
  }
};

export const getOptionById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.OPTIONS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching option:', error);
    throw error;
  }
};

export const createOption = async (optionData) => {
  try {
    const response = await axiosInstance.post(`${API_ENDPOINTS.OPTIONS}`, optionData);
    return response.data;
  } catch (error) {
    console.error('Error creating option:', error);
    throw error;
  }
};

export const updateOption = async (id, optionData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.OPTIONS}/${id}`, optionData);
    return response.data;
  } catch (error) {
    console.error('Error updating option:', error);
    throw error;
  }
};

export const updateOptionsIndexes = async (options) => {
  try {
    const indexUpdates = options.map(option => ({
      id: option.id,
      index: option.index
    }));

    const response = await axiosInstance.post(`${API_ENDPOINTS.OPTIONS}/batch-update-indexes`, indexUpdates);
    return response.data;
  } catch (error) {
    console.error('Error updating options indexes:', error);
    throw error;
  }
};

export const deleteOption = async (id) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.OPTIONS}/${id}`, { active: false });
    return response.data;
  } catch (error) {
    console.error('Error deleting option:', error);
    throw error;
  }
};

export const seedOptions = async () => {
  try {
    const response = await axiosInstance.post(`${API_ENDPOINTS.OPTIONS}/seed`);
    return response.data;
  } catch (error) {
    console.error('Error seeding options:', error);
    throw error;
  }
};

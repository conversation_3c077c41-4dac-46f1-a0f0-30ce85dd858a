import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';
import { 
  Interview, 
  CreateInterviewDto, 
  UpdateInterviewDto,
  Round1Data,
  Round2Data
} from '../types/interview';

/**
 * Get all interviews
 */
export const getAllInterviews = async (): Promise<Interview[]> => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.INTERVIEWS);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get interview by ID
 */
export const getInterviewById = async (id: number): Promise<Interview> => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.INTERVIEWS}/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get interview by job application ID
 */
export const getInterviewByJobApplicationId = async (jobApplicationId: number): Promise<Interview> => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.INTERVIEWS}/by-job-application/${jobApplicationId}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Create a new interview
 */
export const createInterview = async (interviewData: CreateInterviewDto): Promise<Interview> => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.INTERVIEWS, interviewData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Update an interview
 */
export const updateInterview = async (id: number, interviewData: UpdateInterviewDto): Promise<Interview> => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.INTERVIEWS}/${id}`, interviewData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Update interview round 1
 */
export const updateInterviewRound1 = async (id: number, round1Data: Round1Data): Promise<Interview> => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.INTERVIEWS}/${id}/round1`, round1Data);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Update interview round 2
 */
export const updateInterviewRound2 = async (id: number, round2Data: Round2Data): Promise<Interview> => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.INTERVIEWS}/${id}/round2`, round2Data);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Delete an interview
 */
export const deleteInterview = async (id: number): Promise<void> => {
  try {
    const response = await axiosInstance.delete(`${API_ENDPOINTS.INTERVIEWS}/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
}; 
import CryptoJS from 'crypto-js';

const SECRET_KEY = process.env.REACT_APP_AUTH_SECRET || 'TekaiJobPortalSecretKey2025';

export const encrypt = (data) => {
  try {
    const dataString = typeof data === 'object' ? JSON.stringify(data) : data;
    return CryptoJS.AES.encrypt(dataString, SECRET_KEY).toString();
  } catch (error) {
    console.error('Encryption error:', error);
    return null;
  }
};

export const decrypt = (encryptedData) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    try {
      return JSON.parse(decryptedString);
    } catch {
      return decryptedString;
    }
  } catch (error) {
    console.error('Decryption error:', error);
    return null;
  }
};

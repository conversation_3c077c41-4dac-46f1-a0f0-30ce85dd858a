/**
 * Formats a date for display purposes
 * @param {string|Date} date - The date to format
 * @param {Object} options - Formatting options
 * @param {string} options.locale - Locale for formatting (default: 'en-US')
 * @param {Object} options.dateStyle - Date style options
 * @returns {string} Formatted date string
 */
export const formatDate = (date, options = {}) => {
  if (!date) return '';
  
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDate:', date);
      return '';
    }

    const {
      locale = 'en-US',
      dateStyle = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }
    } = options;

    return dateObj.toLocaleDateString(locale, dateStyle);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Formats a date for display in Vietnamese locale
 * @param {string|Date} date - The date to format
 * @returns {string} Formatted date string in Vietnamese
 */
export const formatDateVN = (date) => {
  return formatDate(date, {
    locale: 'vi-VN',
    dateStyle: {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }
  });
};

export const formatDateShort = (date) => {
  if (!date) return '-';
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '-';

    const day = String(dateObj.getDate()).padStart(2, '0');
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    return '-';
  }
};

/**
 * Formats a date for HTML date input (YYYY-MM-DD format)
 * Handles timezone properly to avoid date shifting issues
 * @param {string|Date} date - The date to format
 * @returns {string} Date string in YYYY-MM-DD format
 */
export const formatDateForInput = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDateForInput:', date);
      return '';
    }

    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

/**
 * Formats a datetime for HTML datetime-local input (YYYY-MM-DDTHH:mm format)
 * @param {string|Date} date - The date to format
 * @returns {string} Datetime string in YYYY-MM-DDTHH:mm format
 */
export const formatDateTimeForInput = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDateTimeForInput:', date);
      return '';
    }

    // Use local timezone to avoid date shifting
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.error('Error formatting datetime for input:', error);
    return '';
  }
};

/**
 * Parses a date input value and returns a proper Date object
 * Handles timezone issues when parsing date strings from HTML inputs
 * @param {string} dateString - Date string from HTML input (YYYY-MM-DD)
 * @returns {Date|null} Date object or null if invalid
 */
export const parseDateFromInput = (dateString) => {
  if (!dateString) return null;
  
  try {
    const [year, month, day] = dateString.split('-').map(Number);
    if (!year || !month || !day) return null;
    
    // Month is 0-indexed in Date constructor
    const dateObj = new Date(year, month - 1, day);
    
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date string provided to parseDateFromInput:', dateString);
      return null;
    }
    
    return dateObj;
  } catch (error) {
    console.error('Error parsing date from input:', error);
    return null;
  }
};

/**
 * Converts a date to ISO string for API submission
 * Ensures proper timezone handling
 * @param {string|Date} date - The date to convert
 * @param {boolean} dateOnly - If true, returns only date part (YYYY-MM-DD)
 * @returns {string|null} ISO string or date-only string, null if invalid
 */
export const formatDateForAPI = (date, dateOnly = true) => {
  if (!date) return null;
  
  try {
    let dateObj;
    
    if (typeof date === 'string') {
      // If it's a date string from input (YYYY-MM-DD), parse it properly
      if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        dateObj = parseDateFromInput(date);
      } else {
        dateObj = new Date(date);
      }
    } else {
      dateObj = new Date(date);
    }
    
    if (!dateObj || isNaN(dateObj.getTime())) {
      console.warn('Invalid date provided to formatDateForAPI:', date);
      return null;
    }
    
    if (dateOnly) {
      // Return YYYY-MM-DD format for date-only fields
      return formatDateForInput(dateObj);
    } else {
      // Return full ISO string for datetime fields
      return dateObj.toISOString();
    }
  } catch (error) {
    console.error('Error formatting date for API:', error);
    return null;
  }
};

/**
 * Checks if a date string or Date object is valid
 * @param {string|Date} date - The date to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const isValidDate = (date) => {
  if (!date) return false;
  
  try {
    const dateObj = new Date(date);
    return !isNaN(dateObj.getTime());
  } catch (error) {
    return false;
  }
};

/**
 * Gets the current date in YYYY-MM-DD format
 * @returns {string} Current date string
 */
export const getCurrentDateString = () => {
  return formatDateForInput(new Date());
};

/**
 * Gets the current datetime in YYYY-MM-DDTHH:mm format
 * @returns {string} Current datetime string
 */
export const getCurrentDateTimeString = () => {
  return formatDateTimeForInput(new Date());
};

/**
 * Adds days to a date
 * @param {string|Date} date - The base date
 * @param {number} days - Number of days to add (can be negative)
 * @returns {Date|null} New date object or null if invalid
 */
export const addDays = (date, days) => {
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return null;
    
    dateObj.setDate(dateObj.getDate() + days);
    return dateObj;
  } catch (error) {
    console.error('Error adding days to date:', error);
    return null;
  }
};

/**
 * Adds months to a date
 * @param {string|Date} date - The base date
 * @param {number} months - Number of months to add (can be negative)
 * @returns {Date|null} New date object or null if invalid
 */
export const addMonths = (date, months) => {
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return null;

    dateObj.setMonth(dateObj.getMonth() + months);
    return dateObj;
  } catch (error) {
    console.error('Error adding months to date:', error);
    return null;
  }
};

/**
 * Calculates the difference between two dates in days
 * @param {string|Date} date1 - First date
 * @param {string|Date} date2 - Second date
 * @returns {number|null} Difference in days (positive if date1 > date2), null if invalid
 */
export const diffInDays = (date1, date2) => {
  try {
    const d1 = new Date(date1);
    const d2 = new Date(date2);

    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return null;

    const diffTime = d1.getTime() - d2.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } catch (error) {
    console.error('Error calculating date difference:', error);
    return null;
  }
};

/**
 * Formats a date range for display
 * @param {string|Date} startDate - Start date
 * @param {string|Date} endDate - End date
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date range string
 */
export const formatDateRange = (startDate, endDate, options = {}) => {
  const { locale = 'en-US', separator = ' - ' } = options;

  const start = formatDate(startDate, { locale });
  const end = formatDate(endDate, { locale });

  if (!start && !end) return '';
  if (!start) return `Until ${end}`;
  if (!end) return `From ${start}`;

  return `${start}${separator}${end}`;
};

/**
 * Gets the start and end of a month for a given date
 * @param {string|Date} date - The date
 * @returns {Object|null} Object with startOfMonth and endOfMonth Date objects
 */
export const getMonthBounds = (date) => {
  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return null;

    const startOfMonth = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);
    const endOfMonth = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0);

    return { startOfMonth, endOfMonth };
  } catch (error) {
    console.error('Error getting month bounds:', error);
    return null;
  }
};

/**
 * Checks if a date is within a specific range
 * @param {string|Date} date - Date to check
 * @param {string|Date} startDate - Range start date
 * @param {string|Date} endDate - Range end date
 * @returns {boolean} True if date is within range
 */
export const isDateInRange = (date, startDate, endDate) => {
  try {
    const d = new Date(date);
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(d.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
      return false;
    }

    return d >= start && d <= end;
  } catch (error) {
    console.error('Error checking date range:', error);
    return false;
  }
};

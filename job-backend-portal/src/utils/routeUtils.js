/**
 * Utility functions for handling route navigation based on user roles
 */

import { canAccessRoute } from '../constants/roleRouteMatrix';

/**
 * Get the default dashboard route based on user role
 * @param {string} role - The user's role
 * @returns {string} - The dashboard route for the role
 */
export const getDashboardRoute = (role) => {
  if (!role) return '/login';
  
  switch (role.toLowerCase()) {
    case 'admin':
      return '/admin-dashboard';
    case 'employer':
      return '/employer-dashboard';
    case 'user':
      return '/user-dashboard';
    case 'hr':
      return '/hr-dashboard';
    default:
      return '/login';
  }
};

/**
 * Check if a user should be redirected to their dashboard
 * @param {string} currentPath - The current route path
 * @param {string} role - The user's role
 * @returns {boolean} - Whether the user should be redirected
 */
export const shouldRedirectToDashboard = (currentPath, role) => {
  // Don't redirect if on login page
  if (currentPath === '/login') return false;
  
  // Don't redirect if on root path
  if (currentPath === '/') return false;

  // If no role (not authenticated), redirect to login
  if (!role) return true;
  
  // Check if user has access to the current path
  const hasAccess = canAccessRoute(currentPath, role);
  if (hasAccess) return false;
  
  const dashboardRoute = getDashboardRoute(role);
  
  // Don't redirect if already on dashboard
  if (currentPath === dashboardRoute) return false;
  
  // Don't redirect if the current path is a subpath of the dashboard
  if (currentPath.startsWith(dashboardRoute + '/')) return false;
  
  return true;
}; 
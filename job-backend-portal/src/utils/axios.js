import axios from 'axios';
import qs from 'qs';

const axiosInstance = axios.create({
  paramsSerializer: (params) =>
    qs.stringify(params, { arrayFormat: 'repeat' })
});

// Request interceptor - adds token to requests
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor - handles 401 errors
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and user data
      localStorage.removeItem('access_token');
      localStorage.removeItem('userRole');
      
      // Get the current path to redirect back after login
      const currentPath = window.location.pathname;
      if (currentPath !== '/login') {
        // Store the current path for redirect after login
        sessionStorage.setItem('redirectPath', currentPath);
        // Redirect to login
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
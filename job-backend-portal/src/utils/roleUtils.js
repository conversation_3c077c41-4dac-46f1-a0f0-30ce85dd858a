/**
 * Utility functions for handling role-based access control
 */

import { useUser } from '../contexts/UserContext';
import { Button } from '@mui/material';

/**
 * Check if a user has permission to perform an action based on their role
 * @param {string} userRole - The user's role
 * @param {string|Array<string>} allowedRoles - The role(s) that are allowed to perform the action
 * @returns {boolean} - Whether the user has permission
 */
export const hasPermission = (userRole, allowedRoles) => {
  if (!userRole) return false;
  
  // Convert allowedRoles to array if it's a string
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  
  // console.log(roles);
  // console.log(userRole);
  // console.log(allowedRoles);
  // Filter out any undefined or null roles and make comparison case-insensitive
  return roles
    .filter(role => role != null)
    .some(role => role.toLowerCase() === userRole.toLowerCase());
};

/**
 * Higher-order component to conditionally render content based on user role
 * @param {Object} props - Component props
 * @param {string|Array<string>} props.allowedRoles - The role(s) that are allowed to see the content
 * @param {React.ReactNode} props.children - The content to render if user has permission
 * @param {React.ReactNode} [props.fallback] - Optional content to render if user doesn't have permission
 * @returns {React.ReactNode} - The rendered content based on user's role
 */
export const RoleBasedContent = ({ allowedRoles, children, fallback = null }) => {
  const { user } = useUser();
  
  if (hasPermission(user.role, allowedRoles)) {
    return children;
  }
  
  return fallback;
};

/**
 * Higher-order component to conditionally render a button based on user role
 * @param {Object} props - Component props
 * @param {string|Array<string>} props.allowedRoles - The role(s) that are allowed to see the button
 * @param {Object} props.buttonProps - Props to pass to the button component
 * @param {React.ReactNode} props.children - The button content
 * @returns {React.ReactNode} - The rendered button or null
 */
export const RoleBasedButton = ({ allowedRoles, buttonProps, children }) => {
  const { user } = useUser();
  
  if (!hasPermission(user.role, allowedRoles)) {
    return null;
  }
  
  return (
    <Button {...buttonProps}>
      {children}
    </Button>
  );
}; 
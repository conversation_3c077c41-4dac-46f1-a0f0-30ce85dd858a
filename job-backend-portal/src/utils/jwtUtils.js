/**
 * Utility functions for handling JWT tokens
 */

/**
 * Decodes a JWT token and returns the payload
 * @param {string} token - The JWT token to decode
 * @returns {Object|null} - The decoded payload or null if invalid
 */
export const decodeJWT = (token) => {
  try {
    if (!token) return null;
    
    // Split the token into parts
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    // Decode the payload (second part)
    const payload = JSON.parse(atob(parts[1]));
    
    // Check if token is expired
    if (payload.exp && payload.exp < Date.now() / 1000) {
      return null;
    }
    
    return payload;
  } catch (error) {
    return null;
  }
};

/**
 * Extracts user information from a JWT token
 * @param {string} token - The JWT token
 * @returns {Object|null} - User information or null if invalid
 */
export const getUserFromToken = (token) => {
  const payload = decodeJWT(token);
  if (!payload) return null;

  // Validate required fields
  if (!payload.sub || !payload.role || !payload.email) {
    return null;
  }

  // Validate role
  const validRoles = ['Admin', 'Employer', 'User', 'HR', 'Content Creator'];
  if (!validRoles.includes(payload.role)) {
    return null;
  }

  return {
    id: payload.sub,
    role: payload.role,
    email: payload.email,
    exp: payload.exp
  };
}; 
import { ChevronR<PERSON>, MapPin, Clock, Briefcase, DollarSign } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"

export default function JobDescription() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-800">TEKAI VIETNAM JSC</h1>
          </div>
          <nav className="hidden md:flex space-x-6">
            <a href="#" className="text-gray-600 hover:text-gray-900">
              Home
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900">
              Jobs
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900">
              About
            </a>
            <a href="#" className="text-gray-600 hover:text-gray-900">
              Contact
            </a>
          </nav>
          <Button className="bg-blue-600 hover:bg-blue-700">Apply Now</Button>
        </div>
      </header>

      {/* Job Title Section */}
      <section className="bg-blue-700 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">Delivery Lead/ Senior Automation Test</h1>
              <div className="flex flex-wrap items-center gap-4 mt-4">
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span>Hanoi, Vietnam</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>Full-time</span>
                </div>
                <div className="flex items-center">
                  <Briefcase className="h-5 w-5 mr-2" />
                  <span>Code: HR09</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  <span>Negotiable</span>
                </div>
              </div>
            </div>
            <div className="mt-6 md:mt-0">
              <Button size="lg" className="bg-white text-blue-700 hover:bg-gray-100">
                Apply
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Job Details */}
          <div className="lg:col-span-2 space-y-8">
            {/* Job Overview */}
            <Card className="p-6 shadow-sm">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Job Overview</h2>
              <p className="text-gray-700 mb-4">
                Tekai Vietnam is looking for a Senior Automation Test Engineer to lead our testing efforts and ensure
                the quality of our software solutions. You will be responsible for designing, implementing, and
                maintaining automated test frameworks, guiding junior testers, and collaborating with developers to
                improve software reliability.
              </p>
              <p className="text-gray-700">
                Participate in meetings with customers and leaders to understand the requirements.
              </p>
            </Card>

            {/* Key Responsibilities */}
            <Card className="p-6 shadow-sm">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Key Responsibilities</h2>
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                <li>
                  Design, develop, and maintain robust automation test frameworks for web, API, and mobile applications.
                </li>
                <li>
                  Develop and execute automated test scripts using tools like Selenium, Cypress, Playwright, or Appium.
                </li>
                <li>Perform end-to-end, regression, performance, and security testing to validate software quality.</li>
                <li>Analyze test results, report defects, and work closely with developers to resolve issues.</li>
                <li>Define and implement test strategies, plans, and documentation for automation testing.</li>
                <li>Optimize automation test suites for better efficiency and coverage.</li>
                <li>Mentor junior testers and improve automation best practices within the team.</li>
                <li>
                  Participate in requirement clarification, test planning, test scenario development, and test
                  automation scripting.
                </li>
              </ul>
            </Card>

            {/* Job Requirements */}
            <Card className="p-6 shadow-sm">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Job Requirements</h2>

              <h3 className="text-xl font-semibold text-gray-800 mb-3">Technical Skills:</h3>
              <ul className="list-disc pl-5 space-y-2 text-gray-700 mb-6">
                <li>4+ years of experience in automation testing.</li>
                <li>Proficient in testing methodologies and writing comprehensive test cases.</li>
                <li>Strong proficiency in Selenium, Cypress, Playwright, or Appium.</li>
                <li>Experience with at least one programming language (e.g., Java, Python, JavaScript, C#).</li>
                <li>Strong understanding of API testing using Postman, RestAssured, or similar tools.</li>
                <li>Familiarity with test management tools (Jira, TestRail, Xray, etc.).</li>
                <li>Knowledge of performance testing tools like JMeter, k6, or Gatling is a plus.</li>
              </ul>

              <h3 className="text-xl font-semibold text-gray-800 mb-3">Soft Skills:</h3>
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                <li>Strong problem-solving and analytical skills.</li>
                <li>Excellent communication skills in English (preferred TOEIC 750+ or IELTS 6.0+).</li>
                <li>Ability to lead test automation strategies and mentor junior team members.</li>
                <li>Proactive, self-motivated, and detail-oriented.</li>
              </ul>
            </Card>
          </div>

          {/* Right Column - Additional Info */}
          <div className="space-y-8">
            {/* Job Details Card */}
            <Card className="p-6 shadow-sm">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Position Details</h2>

              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-700">Location</h3>
                  <p className="text-gray-600">17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội</p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-700">Working Hours</h3>
                  <p className="text-gray-600">
                    5 days per week (Monday to Friday, from 9:00 AM to 6:00 PM, with a 1.5-hour lunch break)
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-700">Employment Type</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Badge className="bg-blue-600">FULL-TIME</Badge>
                    <Badge variant="outline" className="text-gray-500">
                      PART-TIME
                    </Badge>
                    <Badge variant="outline" className="text-gray-500">
                      CONTRACTOR
                    </Badge>
                    <Badge variant="outline" className="text-gray-500">
                      INTERN
                    </Badge>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-700">Salary</h3>
                  <p className="text-gray-600">Negotiate (Gross)</p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-700">Status</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Badge className="bg-green-600">OPEN</Badge>
                  </div>
                </div>
              </div>
            </Card>

            {/* Benefits Card */}
            <Card className="p-6 shadow-sm">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Benefits & Welfare</h2>

              <ul className="space-y-3">
                <li className="flex">
                  <ChevronRight className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">
                    13th-month salary bonus, business performance-based bonus, and holiday bonuses
                  </span>
                </li>
                <li className="flex">
                  <ChevronRight className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Health insurance card</span>
                </li>
                <li className="flex">
                  <ChevronRight className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">
                    Annual HR orientation program, professional skill development, and course subsidies
                  </span>
                </li>
                <li className="flex">
                  <ChevronRight className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Quarterly team-building activities and annual company trips</span>
                </li>
                <li className="flex">
                  <ChevronRight className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Opportunities to travel abroad for training and experience</span>
                </li>
                <li className="flex">
                  <ChevronRight className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                  <span className="text-gray-700">Participation in an English club taught by native speakers</span>
                </li>
              </ul>
            </Card>

            {/* Apply Now Card */}
            <Card className="p-6 shadow-sm bg-blue-50">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Apply Now</h2>
              <p className="text-gray-700 mb-4">Interested in this position? Submit your application today!</p>
              <Button className="w-full bg-blue-600 hover:bg-blue-700">Apply for this position</Button>
            </Card>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">TEKAI VIETNAM JSC</h3>
              <p className="text-gray-300">17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Home
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Jobs
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    About Us
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-300 hover:text-white">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Connect With Us</h3>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-300 hover:text-white">
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path
                      fillRule="evenodd"
                      d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
                <a href="#" className="text-gray-300 hover:text-white">
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-gray-300 hover:text-white">
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path
                      fillRule="evenodd"
                      d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
                <a href="#" className="text-gray-300 hover:text-white">
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path
                      fillRule="evenodd"
                      d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
              </div>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>© {new Date().getFullYear()} TEKAI VIETNAM JSC. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

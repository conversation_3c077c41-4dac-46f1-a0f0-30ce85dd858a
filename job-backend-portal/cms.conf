server {
    listen 80;
    server_name job-cms.tekai.vn;

    # Root directory for static files
    root /var/www/job-cms.tekai.vn/build;
    index index.html;

    # Client body size limit
    client_max_body_size 10M;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Try serving static files first, then fallback to React app
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Serve static files with caching
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }

    # Enable gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;
    gzip_proxied any;
} 
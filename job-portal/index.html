<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tekai Vietnam - Senior Automation Test Engineer</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Font Awesome for icons (CDN) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container header-container">
            <div class="logo">
                <h1>TEKAI VIETNAM JSC</h1>
            </div>
            <nav class="main-nav">
                <a href="#">Home</a>
                <a href="#">Jobs</a>
                <a href="#">About</a>
                <a href="#">Contact</a>
            </nav>
            <button class="btn primary-btn">Apply Now</button>
        </div>
    </header>

    <!-- Job Title Section -->
    <section class="job-title-section">
        <div class="container">
            <div class="job-title-content">
                <div>
                    <h1>Delivery Lead/ Senior Automation Test</h1>
                    <div class="job-meta">
                        <div class="meta-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Hanoi, Vietnam</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>Full-time</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-briefcase"></i>
                            <span>Code: HR09</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span>Negotiable</span>
                        </div>
                    </div>
                </div>
                <div>
                    <button class="btn light-btn">Apply</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container main-content">
        <div class="content-grid">
            <!-- Left Column - Job Details -->
            <div class="main-column">
                <!-- Job Overview -->
                <div class="card">
                    <h2>Job Overview</h2>
                    <p>
                        Tekai Vietnam is looking for a Senior Automation Test Engineer to lead our testing efforts and ensure
                        the quality of our software solutions. You will be responsible for designing, implementing, and
                        maintaining automated test frameworks, guiding junior testers, and collaborating with developers to
                        improve software reliability.
                    </p>
                    <p>
                        Participate in meetings with customers and leaders to understand the requirements.
                    </p>
                </div>

                <!-- Key Responsibilities -->
                <div class="card">
                    <h2>Key Responsibilities</h2>
                    <ul>
                        <li>
                            Design, develop, and maintain robust automation test frameworks for web, API, and mobile applications.
                        </li>
                        <li>
                            Develop and execute automated test scripts using tools like Selenium, Cypress, Playwright, or Appium.
                        </li>
                        <li>Perform end-to-end, regression, performance, and security testing to validate software quality.</li>
                        <li>Analyze test results, report defects, and work closely with developers to resolve issues.</li>
                        <li>Define and implement test strategies, plans, and documentation for automation testing.</li>
                        <li>Optimize automation test suites for better efficiency and coverage.</li>
                        <li>Mentor junior testers and improve automation best practices within the team.</li>
                        <li>
                            Participate in requirement clarification, test planning, test scenario development, and test
                            automation scripting.
                        </li>
                    </ul>
                </div>

                <!-- Job Requirements -->
                <div class="card">
                    <h2>Job Requirements</h2>

                    <h3>Technical Skills:</h3>
                    <ul>
                        <li>4+ years of experience in automation testing.</li>
                        <li>Proficient in testing methodologies and writing comprehensive test cases.</li>
                        <li>Strong proficiency in Selenium, Cypress, Playwright, or Appium.</li>
                        <li>Experience with at least one programming language (e.g., Java, Python, JavaScript, C#).</li>
                        <li>Strong understanding of API testing using Postman, RestAssured, or similar tools.</li>
                        <li>Familiarity with test management tools (Jira, TestRail, Xray, etc.).</li>
                        <li>Knowledge of performance testing tools like JMeter, k6, or Gatling is a plus.</li>
                    </ul>

                    <h3>Soft Skills:</h3>
                    <ul>
                        <li>Strong problem-solving and analytical skills.</li>
                        <li>Excellent communication skills in English (preferred TOEIC 750+ or IELTS 6.0+).</li>
                        <li>Ability to lead test automation strategies and mentor junior team members.</li>
                        <li>Proactive, self-motivated, and detail-oriented.</li>
                    </ul>
                </div>
            </div>

            <!-- Right Column - Additional Info -->
            <div class="sidebar">
                <!-- Job Details Card -->
                <div class="card">
                    <h2>Position Details</h2>

                    <div class="details-list">
                        <div class="detail-item">
                            <h3>Location</h3>
                            <p>17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội</p>
                        </div>

                        <div class="detail-item">
                            <h3>Working Hours</h3>
                            <p>
                                5 days per week (Monday to Friday, from 9:00 AM to 6:00 PM, with a 1.5-hour lunch break)
                            </p>
                        </div>

                        <div class="detail-item">
                            <h3>Employment Type</h3>
                            <div class="badge-container">
                                <span class="badge active">FULL-TIME</span>
                                <span class="badge">PART-TIME</span>
                                <span class="badge">CONTRACTOR</span>
                                <span class="badge">INTERN</span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <h3>Salary</h3>
                            <p>Negotiate (Gross)</p>
                        </div>

                        <div class="detail-item">
                            <h3>Status</h3>
                            <div class="badge-container">
                                <span class="badge green">OPEN</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Benefits Card -->
                <div class="card">
                    <h2>Benefits & Welfare</h2>

                    <ul class="benefits-list">
                        <li>
                            <i class="fas fa-chevron-right"></i>
                            <span>13th-month salary bonus, business performance-based bonus, and holiday bonuses</span>
                        </li>
                        <li>
                            <i class="fas fa-chevron-right"></i>
                            <span>Health insurance card</span>
                        </li>
                        <li>
                            <i class="fas fa-chevron-right"></i>
                            <span>Annual HR orientation program, professional skill development, and course subsidies</span>
                        </li>
                        <li>
                            <i class="fas fa-chevron-right"></i>
                            <span>Quarterly team-building activities and annual company trips</span>
                        </li>
                        <li>
                            <i class="fas fa-chevron-right"></i>
                            <span>Opportunities to travel abroad for training and experience</span>
                        </li>
                        <li>
                            <i class="fas fa-chevron-right"></i>
                            <span>Participation in an English club taught by native speakers</span>
                        </li>
                    </ul>
                </div>

                <!-- Apply Now Card -->
                <div class="card highlight-card">
                    <h2>Apply Now</h2>
                    <p>Interested in this position? Submit your application today!</p>
                    <button class="btn primary-btn full-width">Apply for this position</button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>TEKAI VIETNAM JSC</h3>
                    <p>17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội</p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="#">Home</a></li>
                        <li><a href="#">Jobs</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect With Us</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© <span id="current-year"></span> TEKAI VIETNAM JSC. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Simple script to update the current year in the footer
        document.getElementById('current-year').textContent = new Date().getFullYear();
    </script>
</body>
</html>

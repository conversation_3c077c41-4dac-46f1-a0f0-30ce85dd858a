#!/bin/bash

# Exit on error
set -e

echo "🚀 Starting build process..."

# Load environment variables
echo "📝 Loading environment variables..."
if [ -f .env.production ]; then
    source .env.production
else
    echo "❌ .env.production file not found!"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application for production
echo "🏗️ Building the application for production..."
npm run build:prod

echo "✅ Build completed successfully!"
echo "📦 Build files are ready in the 'build' directory"
echo "📋 You can now copy the contents of the 'build' directory to your server" 
cp -r build/ /var/www/job-dev.tekai.vn/
chmod -R 755 /var/www/job-dev.tekai.vn/build/images
echo "Build success!" 

server {
    server_name job.tekai.vn;
    client_max_body_size 10M;

    # SSL configuration
    # listen 443 ssl;
    # ssl_certificate /etc/nginx/ssl/job.tekai.vn.crt;
    # ssl_certificate_key /etc/nginx/ssl/job.tekai.vn.key;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Root directory for web content
    root /var/www/job.tekai.vn;
    index index.html;

    # Main location block
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Cache static assets
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }

    # Handle images directory
    location /images/ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
        add_header Access-Control-Allow-Origin *;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/job.tekai.vn/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/job.tekai.vn/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = job.tekai.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name job.tekai.vn;
    return 404; # managed by Certbot
}
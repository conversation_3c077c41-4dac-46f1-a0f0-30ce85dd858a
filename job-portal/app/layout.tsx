import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Tekai Vietnam - Job Portal',
  description: 'Explore career opportunities at Tekai Vietnam. Find detailed job descriptions, requirements, and application information for various positions.',
  generator: 'Next.js',
  keywords: ['Tekai Vietnam', 'job opportunities', 'careers', 'job descriptions', 'Vietnam jobs', 'IT jobs', 'software development', 'automation testing'],
  authors: [{ name: 'Tekai Vietnam' }],
  openGraph: {
    title: 'Tekai Vietnam - Job Portal',
    description: 'Explore career opportunities at Tekai Vietnam. Find detailed job descriptions, requirements, and application information for various positions.',
    type: 'website',
    locale: 'en_US',
    siteName: 'Tekai Vietnam',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}

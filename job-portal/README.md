# Job Portal

This repository contains the Job Portal application.

## Development Environment

### Prerequisites

- Node.js (v18 or higher recommended)
- npm or yarn package manager
- Git

### Getting Started

1. Clone the repository:
```bash
git clone <repository-url>
cd job-portal
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

The development server will start, and you can access the application at `http://localhost:3000` (or the configured port).

## Staging Environment

The staging environment is accessible at the following URLs:

- Frontend Application: https://job-dev.tekai.vn
- API Endpoint: https://api-dev.tekai.vn/v1

### API Integration

When working with the staging environment, ensure your API calls are directed to:
```
https://api-dev.tekai.vn/v1
```

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
REACT_APP_API_URL=https://api-dev.tekai.vn/v1
```

## Contributing

1. Create a new branch for your feature or fix
2. Make your changes
3. Submit a pull request

## Support

For any issues or questions, please contact the development team. 
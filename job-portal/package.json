{"name": "job-description-portal", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@react-google-maps/api": "^2.20.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "start:dev": "REACT_APP_ENV=development react-scripts start", "start:prod": "REACT_APP_ENV=production react-scripts start", "build": "react-scripts build", "build:dev": "REACT_APP_ENV=development react-scripts build", "build:prod": "REACT_APP_ENV=production react-scripts build", "test": "react-scripts test", "test:watch": "react-scripts test --watch", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "lint": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:fix": "eslint src/**/*.{js,jsx,ts,tsx} --fix", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss}\"", "analyze": "source-map-explorer 'build/static/js/*.js'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
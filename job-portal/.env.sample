# Job Description Portal Environment Variables
# Copy this file to .env.development or .env.production and update the values accordingly

# API Configuration
REACT_APP_API_URL=http://localhost:3000/api
# The base URL for API requests
# Development: http://localhost:3000/api
# Production: https://api.yourdomain.com/api

# Environment
REACT_APP_ENV=development
# The current environment (development/production)
# Development: development
# Production: production

# Debug Mode
REACT_APP_DEBUG=true
# Enable/disable debug features and logging
# Development: true
# Production: false

# Application Version
REACT_APP_VERSION=1.0.0
# The current version of the application
# Development: 1.0.0-dev
# Production: 1.0.0

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=UA-XXXXXXXXX-X
# Google Analytics tracking ID
# Development: (empty)
# Production: UA-XXXXXXXXX-X

# Error Tracking
REACT_APP_SENTRY_DSN=https://<EMAIL>/xxxxxx
# Sentry DSN for error tracking
# Development: (empty)
# Production: https://<EMAIL>/xxxxxx

# Optional: Map Configuration
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
# Google Maps API key for map features
# Development: your_development_api_key
# Production: your_production_api_key

# Optional: Authentication
REACT_APP_AUTH0_DOMAIN=your-tenant.auth0.com
REACT_APP_AUTH0_CLIENT_ID=your_client_id
REACT_APP_AUTH0_AUDIENCE=your_api_identifier
# Auth0 configuration for authentication
# Development: your_development_credentials
# Production: your_production_credentials

# Optional: Feature Flags
REACT_APP_ENABLE_EXPERIMENTAL_FEATURES=false
# Enable/disable experimental features
# Development: true/false
# Production: false 
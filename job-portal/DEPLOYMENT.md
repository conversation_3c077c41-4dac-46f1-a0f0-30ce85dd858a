# Deployment Guide for Job Description Portal

This guide explains how to deploy the Job Description Portal React application on a CentOS server with Nginx.

## Prerequisites

- CentOS 7 or later
- Node.js (v16 or later)
- Nginx
- Git
- sudo privileges

## Server Setup

1. Update system packages:
   ```bash
   sudo yum update -y
   ```

2. Install Node.js:
   ```bash
   curl -sL https://rpm.nodesource.com/setup_16.x | sudo bash -
   sudo yum install -y nodejs
   ```

3. Install Nginx:
   ```bash
   sudo yum install -y nginx
   sudo systemctl start nginx
   sudo systemctl enable nginx
   ```

4. Install Git:
   ```bash
   sudo yum install -y git
   ```

## Deployment Steps

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd job-description-portal
   ```

2. Make the deployment script executable:
   ```bash
   chmod +x deploy.sh
   ```

3. Run the deployment script:
   ```bash
   ./deploy.sh
   ```

The script will:
- Install dependencies
- Build the React application
- Set up Nginx configuration
- Deploy the static files

## Post-Deployment

### Checking Nginx Status
```bash
sudo systemctl status nginx
```

### Viewing Nginx Logs
```bash
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## Troubleshooting

1. If the application is not accessible:
   - Check Nginx status: `sudo systemctl status nginx`
   - Verify Nginx configuration: `sudo nginx -t`
   - Check file permissions in the deployment directory
   - Ensure the build directory exists and contains the correct files

2. If you need to restart Nginx:
   ```bash
   sudo systemctl restart nginx
   ```

3. If you encounter permission issues:
   ```bash
   sudo chown -R nginx:nginx /var/www/job-description-portal
   sudo chmod -R 755 /var/www/job-description-portal
   ```

## Security Considerations

1. Configure firewall:
   ```bash
   sudo firewall-cmd --permanent --add-service=http
   sudo firewall-cmd --permanent --add-service=https
   sudo firewall-cmd --reload
   ```

2. Consider setting up SSL with Let's Encrypt for HTTPS.

3. Ensure proper file permissions:
   ```bash
   sudo chown -R nginx:nginx /var/www/job-description-portal
   sudo chmod -R 755 /var/www/job-description-portal
   ```

## Updating the Application

To update the application:

1. Pull the latest changes:
   ```bash
   git pull
   ```

2. Run the deployment script again:
   ```bash
   ./deploy.sh
   ```

## Backup

Regularly backup the following:
- Application code
- Nginx configuration files
- Build directory contents

## Performance Optimization

1. Enable gzip compression in Nginx:
   ```bash
   sudo tee /etc/nginx/conf.d/gzip.conf << EOF
   gzip on;
   gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
   gzip_comp_level 6;
   gzip_min_length 1000;
   EOF
   ```

2. Restart Nginx after making changes:
   ```bash
   sudo systemctl restart nginx
   ``` 
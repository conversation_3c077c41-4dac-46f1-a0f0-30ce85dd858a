import axios from 'axios'
import { API_ENDPOINTS } from '../constants/api'

export const getAllJobs = async () => {
  try {
    const response = await axios.get(API_ENDPOINTS.JOBS)
    return response.data
  } catch (error) {
    throw error
  }
}

export const getJobById = async (id) => {
  try {
    const response = await axios.get(`${API_ENDPOINTS.JOBS}/${id}`)
    return response.data
  } catch (error) {
    throw error
  }
}
import axios from 'axios'
import { API_ENDPOINTS } from '../constants/api'

export const getAllBlogs = async (categorySlug = '') => {
  try {
    const url = `${API_ENDPOINTS.BLOGS}/by-category/${categorySlug || 'all'}`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export const getBlogById = async (id) => {
  try {
    const response = await axios.get(`${API_ENDPOINTS.BLOGS}/${id}`)
    return response.data  
  } catch (error) {
    throw error
  }
}

export const getBlogCategories = async () => {
  try {
    const response = await axios.get(`${API_ENDPOINTS.BLOGS}/categories`);
    return response.data;
  } catch (error) {
    throw error;
  }
} 
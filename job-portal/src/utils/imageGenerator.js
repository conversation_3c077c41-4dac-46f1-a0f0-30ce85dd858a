export const generateInitialImage = (title, size = 400) => {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d');

  // Background gradient
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#8a7fff');
  gradient.addColorStop(1, '#6a4fff');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, size, size);

  // Text
  const initial = title.charAt(0).toUpperCase();
  ctx.fillStyle = 'white';
  ctx.font = `bold ${size * 0.4}px Inter, sans-serif`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(initial, size / 2, size / 2);

  return canvas.toDataURL('image/png');
}; 
.blogs-page {
  min-height: 100vh;
}


/* Blogs Grid */
.blogs-listing {
  padding: 3rem 0;
}

.blogs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.blog-card {
  background: var(--color-card-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  border: 1px solid var(--color-border);
}

.blog-card:hover {
  transform: translateY(-5px);
  border-color: var(--color-accent);
}

.blog-image {
  height: 200px;
  overflow: hidden;
}

.blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blog-content {
  padding: 1.5rem;
}

.blog-header {
  margin-bottom: 1rem;
}

.blog-badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
}

.new-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--color-text);
  background: var(--color-accent-light);
}

.category-tag {
  padding: 0.3rem 0.8rem;
  background: var(--color-accent-light);
  color: var(--color-accent);
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.blog-header h2 {
  font-size: 1.25rem;
  line-height: 1.4;
  margin: 0;
  color: var(--color-text);
}

.blog-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.read-more-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.read-more-btn:hover {
  color: var(--color-accent-dark);
}

/* Loading Skeleton */
.skeleton {
  background: var(--color-border);
  background: linear-gradient(110deg, var(--color-border) 8%, var(--color-background-alt) 18%, var(--color-border) 33%);
  border-radius: 5px;
  background-size: 200% 100%;
  animation: 1.5s shine linear infinite;
}

.skeleton-blog-card {
  background: var(--color-card-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  border: 1px solid var(--color-border);
}

.skeleton-image {
  height: 200px;
  width: 100%;
}

.skeleton-content {
  padding: 1.5rem;
}

.skeleton-title {
  height: 24px;
  margin-bottom: 1rem;
}

.skeleton-description {
  height: 16px;
  margin-bottom: 0.8rem;
}

.skeleton-details {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.skeleton-detail {
  height: 16px;
  width: 100px;
}

@keyframes shine {
  to {
    background-position-x: -200%;
  }
}

/* Error Message */
.error-message {
  text-align: center;
  padding: 3rem 1rem;
}

.error-message p {
  color: var(--color-error, #dc3545);
  margin-bottom: 1rem;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Dark theme specific styles */
[data-theme='dark'] .blog-card {
  background: var(--color-card-bg);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

[data-theme='dark'] .blog-card:hover {
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.3);
}

[data-theme='dark'] .category-tag {
  background: rgba(var(--color-accent-rgb), 0.2);
}

[data-theme='dark'] .skeleton {
  background: linear-gradient(110deg, 
    rgba(255, 255, 255, 0.1) 8%, 
    rgba(255, 255, 255, 0.2) 18%, 
    rgba(255, 255, 255, 0.1) 33%
  );
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }
  
  .blogs-grid {
    grid-template-columns: 1fr;
  }
  
  .category-filters {
    overflow-x: auto;
    padding-bottom: 1rem;
    -webkit-overflow-scrolling: touch;
  }
  
  .category-btn {
    white-space: nowrap;
  }
} 
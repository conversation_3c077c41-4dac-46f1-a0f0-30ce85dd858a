/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --color-background: #ffffff;
  --color-text: #000000;
  --color-text-secondary: #555555;
  --color-accent: #8a7fff;
  --color-accent-light: #e6e3ff;
  --color-border: #eeeeee;
  --color-button: #000000;
  --color-button-text: #ffffff;
  --color-button-hover: #333333;
  --color-card-bg: #ffffff;
  --color-success: #34a853;
}

.dark-theme {
  --color-background: #121212;
  --color-text: #ffffff;
  --color-text-secondary: #aaaaaa;
  --color-accent: #8a7fff;
  --color-accent-light: #2a2544;
  --color-border: #333333;
  --color-button: #ffffff;
  --color-button-text: #000000;
  --color-button-hover: #dddddd;
  --color-card-bg: #1e1e1e;
  --color-success: #34a853;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  color: var(--color-text);
  background-color: var(--color-background);
  transition: background-color 0.3s;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4 {
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.3;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 1.5rem;
  color: var(--color-text);
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.2rem;
  color: var(--color-text);
  margin-bottom: 0.75rem;
}

p {
  margin-bottom: 1rem;
  color: var(--color-text-secondary);
}

ul {
  list-style-position: inside;
  margin-bottom: 1rem;
  padding-left: 1rem;
}

li {
  margin-bottom: 0.5rem;
  color: var(--color-text-secondary);
}

a {
  color: var(--color-text);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--color-accent);
}

.text-accent {
  color: var(--color-accent);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
}

.primary-btn {
  background-color: var(--color-button);
  color: var(--color-button-text);
}

.primary-btn:hover {
  background-color: var(--color-button-hover);
  transform: translateY(-2px);
}

.secondary-btn {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.secondary-btn:hover {
  background-color: var(--color-border);
  transform: translateY(-2px);
}

.light-btn {
  background-color: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.light-btn:hover {
  background-color: var(--color-border);
  transform: translateY(-2px);
}

.full-width {
  width: 100%;
  justify-content: center;
}

/* Main Content */
.main-content {
  padding: 40px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.card {
  background-color: var(--color-card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid var(--color-border);
  transition: background-color 0.3s, border-color 0.3s;
}

.highlight-card {
  background-color: var(--color-accent-light);
}

/* Hero Section */
.hero-section {
  padding: 100px 0 80px;
  text-align: center;
  position: relative;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-section h1 {
    font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  background: linear-gradient(
    to right,
    #000000 0%,
    #221f3f 25%,
    #443e7f 50%,
    #665dbf 75%,
    #8a7fff 100%
  );
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradient-text 10s linear infinite;
  -webkit-text-fill-color: transparent;
}

@keyframes gradient-text {
  from {
    background-position: 0% center;
  }
  to {
    background-position: -200% center;
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 30px;
  font-weight: 500;
}

/* Company Overview */
.company-overview {
  padding: 80px 0;
  background-color: var(--color-background-alt);
  position: relative;
}

.company-overview h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-align: center;
}

.company-overview p {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  line-height: 1.7;
  opacity: 0.8;
  max-width: 800px;
  margin: 0 auto 4rem;
  text-align: center;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.stat-card {
  padding: 3rem 2rem;
  border-radius: 4px;
  background: var(--color-background);
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  text-align: center;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--color-accent);
}

.stat-card h3 {
  font-size: clamp(2rem, 3vw, 2.5rem);
  color: var(--color-accent);
  margin-bottom: 1rem;
  font-weight: 700;
}

.stat-card p {
  font-size: 1.1rem;
  opacity: 0.8;
  margin: 0;
}

/* Values Section */
.values-section {
  padding: 80px 0;
  background: var(--color-background);
  position: relative;
}

.values-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 4rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-align: center;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.value-card {
  padding: 2.5rem 2rem;
  border-radius: 4px;
  background: var(--color-background-alt);
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.value-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--color-accent);
}

.value-card h3 {
  font-size: 1.75rem;
  color: var(--color-accent);
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.value-card p {
  font-size: 1.1rem;
  line-height: 1.7;
  opacity: 0.8;
  flex-grow: 1;
}

/* Team Section */
.team-section {
  padding: 80px 0;
  background: var(--color-background-alt);
  position: relative;
  overflow: hidden;
}

.team-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-align: center;
}

.section-subtitle {
  font-size: 1.25rem;
  opacity: 0.8;
  margin-bottom: 4rem;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.team-grid {
  display: flex;
  gap: 2rem;
  padding: 1rem 0;
  justify-content: center;
  flex-wrap: wrap;
  margin: 0 auto;
  max-width: 1440px;
}

.team-member {
  width: 280px;
  text-align: center;
  transition: all 0.3s ease;
  padding: 1rem;
  flex-shrink: 0;
}

/* Remove the animation and scroll-related styles */
.team-section::before,
.team-section::after {
  display: none;
}

.team-member:hover {
  transform: translateY(-8px);
}

.member-image {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  position: relative;
  overflow: hidden;
  border: 2px solid var(--color-border);
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-image.placeholder {
  background: var(--color-background);
}

.team-member:hover .member-image {
  border-color: var(--color-accent);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}

.team-member h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--color-text);
}

.member-role {
  font-size: 1.1rem;
  opacity: 1;
  color: var(--color-accent);
  position: relative;
  display: inline-block;
}

.member-role::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--color-accent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.team-member:hover .member-role::after {
  transform: scaleX(1);
}

/* Mission Section */
.mission-section {
  padding: 80px 0;
  background: var(--color-background);
  position: relative;
  overflow: hidden;
}

.mission-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 3rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background-alt);
}

.mission-content h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: var(--color-text);
}

.mission-content p {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  line-height: 1.7;
  opacity: 0.8;
}

/* About Page Responsive Styles */
@media (max-width: 1024px) {
  .values-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .member-image.placeholder {
    width: 180px;
    height: 180px;
  }

  .team-member {
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .stats-grid,
  .values-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .value-card {
    padding: 2rem;
  }

  .member-image.placeholder {
    width: 160px;
    height: 160px;
  }

  .team-member {
    min-width: 220px;
    padding: 0.75rem;
  }

  .team-grid {
    gap: 2rem;
  }

  .mission-content {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .stats-grid,
  .values-grid {
    grid-template-columns: 1fr;
  }

  .member-image.placeholder {
    width: 140px;
    height: 140px;
  }

  .team-member {
    min-width: 200px;
    padding: 0.5rem;
  }

  .team-grid {
    gap: 1.5rem;
  }

  .mission-content {
    padding: 1.5rem;
  }
}

/* Services Section */
.services-section {
  padding: 80px 0;
  background-color: var(--color-background);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

.service-card {
  padding: 2rem;
  border-radius: 12px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: white;
  position: relative;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-card .service-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.service-card .service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.service-card h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.service-card p {
  margin: 0;
  text-align: left;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 2;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.2), transparent 70%);
  z-index: 1;
}

.service-card-purple {
  background: linear-gradient(135deg, #8a7fff, #6a4fff);
}

.service-card-blue {
  background: linear-gradient(135deg, #4f9cf9, #2a7de1);
}

.service-card-green {
  background: linear-gradient(135deg, #34a853, #2d8a46);
}

.service-card-orange {
  background: linear-gradient(135deg, #ff7a59, #e25c3d);
}

/* Featured Jobs Section */
.featured-jobs {
  padding: 80px 0;
  background-color: var(--color-accent-light);
}

.jobs-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.job-card {
  background-color: var(--color-card-bg);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid var(--color-border);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.job-card-content {
  padding: 1.5rem;
  text-align: left;
}

.job-card p {
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  max-height: 4.5em;
  text-align: left;
}

.job-badges {
  display: flex;
  gap: 8px;
  align-items: center;
}

.featured-badge {
  background-color: #FFD700;
  color: #000;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.open {
  background-color: #4CAF50;
  color: white;
}

.status-badge.closed {
  background-color: #f44336;
  color: white;
}

.status-badge.draft {
  background-color: #9e9e9e;
  color: white;
}

.status-badge.pending {
  background-color: #ff9800;
  color: white;
}

.job-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--color-accent-light);
  color: var(--color-accent);
  margin-bottom: 1rem;
}

.job-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.job-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
}

.job-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--color-accent);
  color: white;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.2s, transform 0.2s;
  margin-top: auto;
  text-decoration: none;
}

.job-link:hover {
  background-color: var(--color-accent-hover);
  transform: translateY(-2px);
}

.view-all-jobs {
  text-align: center;
  margin-top: 3rem;
}

.view-all-jobs .btn {
  min-width: 200px;
}

/* CTA Section */
.cta-section {
  padding: 100px 0;
  text-align: center;
  background-color: var(--color-background);
}

.cta-content {
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
}

.cta-btn {
  min-width: 200px;
  justify-content: center;
}

.cta-btn:disabled {
  background-color: #cccccc;
  color: #666666;
  border-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cta-btn:disabled:hover {
  background-color: #cccccc;
  color: #666666;
  transform: none;
  box-shadow: none;
}

/* Loading States */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-border) 25%,
    var(--color-accent-light) 37%,
    var(--color-border) 63%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-badge {
  width: 80px;
  height: 24px;
  margin-bottom: 1rem;
}

.skeleton-title {
  height: 24px;
  margin-bottom: 1rem;
  width: 80%;
}

.skeleton-text {
  height: 16px;
  margin-bottom: 0.5rem;
  width: 100%;
}

.skeleton-meta {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
}

.skeleton-meta-item {
  height: 16px;
  width: 100px;
}

.skeleton-link {
  height: 40px;
  width: 150px;
  margin-top: 1rem;
}

/* No Jobs Message */
.no-jobs-message {
  text-align: center;
  padding: 3rem;
  background-color: var(--color-card-bg);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.no-jobs-message p {
  color: var(--color-text-secondary);
  margin-top: 1rem;
}

/* App Layout */
.app {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

@keyframes gradient-move {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.background-tk {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
  opacity: 0.05;
  pointer-events: none;
  background-image: url('../public/images/tekai-logo-black.png'), linear-gradient(45deg, 
    rgba(138, 127, 255, 0.1) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(138, 127, 255, 0.1) 100%
  );
  background-repeat: no-repeat, repeat;
  background-size: 80% auto, 200% 200%;
  background-position: center center, 0 0;
  transition: opacity 0.3s ease;
  animation: gradient-move 15s ease infinite;
}

.dark-theme .background-tk {
  background-image: url('../public/images/tekai-logo-white.png'), linear-gradient(45deg, 
    rgba(138, 127, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(138, 127, 255, 0.1) 100%
  );
}

.logo-image {
  height: 30px;
  width: auto;
  transition: filter 0.3s ease;
  filter: invert(var(--logo-invert, 0));
}

.dark-theme .logo-image {
  --logo-invert: 0;
}

.logo-image.footer-logo {
  --logo-invert: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
  }

  .hero-btn {
    width: 100%;
  }

  .service-card {
    padding: 1.5rem;
  }
  
  .service-card .service-header {
    gap: 0.75rem;
  }
  
  .service-card h3 {
    font-size: 1.1rem;
  }
  
  .service-card .service-icon {
    width: 40px;
    height: 40px;
  }
}

@media (min-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header h2 {
    font-size: 2.5rem;
  }

  h1 {
    font-size: 3rem;
  }

  .content-grid {
    grid-template-columns: 2fr 1fr;
  }
}

/* Jobs Hero Section */
.jobs-hero {
  min-height: 40vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--color-background) 0%, var(--color-background-alt) 100%);
  color: var(--color-text);
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.jobs-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(var(--color-accent-rgb), 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.jobs-hero-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.jobs-hero-content h1 {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  line-height: 1.1;
  margin-bottom: 1.5rem;
  font-weight: 800;
  letter-spacing: -0.03em;
  background: linear-gradient(135deg, var(--color-text) 0%, var(--color-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.jobs-hero-content h1 span {
  background: linear-gradient(135deg, var(--color-text) 0%, var(--color-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.static-underscore {
  color: var(--color-accent);
  opacity: 0.8;
  display: inline-block;
  background: none !important;
  -webkit-text-fill-color: var(--color-accent) !important;
  -webkit-background-clip: initial !important;
  background-clip: initial !important;
}

.blinking-underscore {
  color: var(--color-accent);
  animation: blink 1.2s ease-in-out infinite;
  opacity: 1;
  font-weight: 800;
  display: inline-block;
  transform: translateY(-2px);
  background: none !important;
  -webkit-text-fill-color: var(--color-accent) !important;
  -webkit-background-clip: initial !important;
  background-clip: initial !important;
}

.jobs-hero-content h1::after {
  content: '_';
  color: var(--color-accent);
  animation: blink 1.2s ease-in-out infinite;
  opacity: 0.8;
}

.jobs-hero-content p {
  font-size: clamp(1.1rem, 1.5vw, 1.4rem);
  line-height: 1.6;
  opacity: 0.85;
  max-width: 600px;
  margin: 0 auto;
  font-weight: 400;
}

/* Jobs Filter Section */
.jobs-filter {
  padding: 40px 0;
  background-color: var(--color-background-alt);
  border-bottom: 1px solid var(--color-border);
}

.filter-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 max(5vw, 1rem);
  text-align: center;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 0 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.search-box:hover,
.search-box:focus-within {
  border-color: var(--color-accent);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-box svg {
  color: var(--color-text-secondary);
  margin-right: 12px;
}

.search-box input {
  flex: 1;
  border: none;
  padding: 16px 0;
  background-color: transparent;
  color: var(--color-text);
  font-size: 1rem;
  outline: none;
}

.search-box input::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.8;
}

.category-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.category-btn {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-text);
}

.category-btn:hover {
  background-color: var(--color-border);
  transform: translateY(-2px);
}

.category-btn.active {
  background-color: var(--color-accent);
  color: white;
  border-color: var(--color-accent);
}

/* Jobs Listing Section */
.jobs-listing {
  padding: 80px 0;
  background-color: var(--color-background);
}

.jobs-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 max(5vw, 1rem);
  margin-bottom: 60px;
}

.job-listing-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.job-listing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--color-accent);
}

.job-listing-content {
  padding: 30px;
}

.job-listing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.job-listing-header h2 {
  font-size: 1.75rem;
  margin: 0;
  font-weight: 600;
  letter-spacing: -0.02em;
  color: var(--color-text);
}

.featured-badge {
  background-color: var(--color-accent);
  color: white;
  padding: 6px 16px;
  border-radius: 24px;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.02em;
}

.job-description {
  color: var(--color-text-secondary);
  margin-bottom: 25px;
  line-height: 1.6;
  font-size: 1.1rem;
  opacity: 0.9;
}

.job-details {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.job-detail {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--color-text-secondary);
}

.job-detail:hover {
  color: var(--color-text);
}

.job-detail svg {
  color: var(--color-text-secondary);
}

.job-listing-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding: 0 30px 30px;
}

.view-btn,
.apply-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 30px;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  min-width: 150px;
  justify-content: center;
}

.view-btn {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.view-btn:hover {
  background-color: var(--color-text);
  color: var(--color-background);
  border-color: var(--color-text);
  transform: translateY(-2px);
}

.apply-btn {
  background-color: var(--color-accent);
  color: white;
  border: 1px solid var(--color-accent);
}

[data-theme='dark'] .apply-btn {
  color: white;
}

.apply-btn:hover {
  background-color: transparent;
  color: var(--color-accent);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.apply-btn.disabled,
.apply-btn:disabled {
  background-color: #cccccc;
  color: #666666;
  border-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.apply-btn.disabled:hover,
.apply-btn:disabled:hover {
  background-color: #cccccc;
  color: #666666;
  transform: none;
  box-shadow: none;
}

/* No Jobs Found */
.no-jobs-found {
  text-align: center;
  padding: 60px 20px;
}

.no-jobs-found h3 {
  font-size: 1.8rem;
  color: var(--color-text);
  margin-bottom: 1rem;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.no-jobs-found p {
  color: var(--color-text-secondary);
  font-size: 1.1rem;
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.6;
  opacity: 0.9;
}

/* Jobs CTA Section */
.jobs-cta {
  padding: 100px 0;
  background-color: var(--color-background);
  position: relative;
  overflow: hidden;
}

.jobs-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(var(--color-accent-rgb), 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.jobs-cta-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 0 20px;
}

.jobs-cta-content h2 {
  font-size: 2.5rem;
  color: var(--color-text);
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.jobs-cta-content p {
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0.9;
}

/* Jobs Page Specific Animations */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* Jobs Page Responsive Styles */
@media (min-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .search-box {
    max-width: 500px;
    margin: 0 auto;
  }

  .job-listing-actions {
    margin-top: 0;
  }

  .view-btn,
  .apply-btn {
    min-width: 150px;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .search-box {
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .jobs-hero,
  .jobs-cta {
    padding: 60px 0;
  }

  .jobs-listing {
    padding: 40px 0;
  }

  .job-listing-content {
    padding: 20px;
  }

  .job-listing-actions {
    flex-direction: column;
  }

  .view-btn,
  .apply-btn {
    width: 100%;
  }

  .job-details {
    gap: 15px;
  }

  .job-detail {
    width: 100%;
  }

  .jobs-cta-content h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .jobs-cta-content h2 {
    font-size: 1.75rem;
  }
}

/* Job Detail Page */
.job-detail-page {
  min-height: 100vh;
  background-color: var(--color-background);
  color: var(--color-text);
}

/* Back Link */
.back-link {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  color: var(--color-text-secondary);
  margin-bottom: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 24px;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
}

.back-link:hover {
  color: var(--color-accent);
  transform: translateX(-5px);
  background-color: var(--color-background-alt);
}

/* Job Title Section */
.job-title-section {
  padding: 60px 0;
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.job-title-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 30px;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 max(5vw, 1rem);
}

.job-title-section h1 {
  font-size: clamp(2rem, 4vw, 3.5rem);
  margin-bottom: 1.5rem;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--color-text-secondary);
  font-size: 0.95rem;
  padding: 8px 16px;
  background-color: var(--color-background-alt);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.meta-item:hover {
  background-color: var(--color-accent-light);
  color: var(--color-accent);
  transform: translateY(-2px);
}

.meta-item svg {
  color: var(--color-accent);
}

/* Job Detail Specific Animations */
.arrow {
  transition: transform 0.3s ease;
}

.apply-btn:hover .arrow {
  transform: translateX(6px);
}

/* Job Detail Loading Skeleton */
.skeleton-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
}

.skeleton-title {
  height: 32px;
  width: 60%;
  border-radius: 6px;
  margin-bottom: 20px;
}

.skeleton-subtitle {
  height: 24px;
  width: 40%;
  border-radius: 4px;
  margin: 20px 0;
}

.skeleton-text {
  height: 16px;
  width: 100%;
  border-radius: 4px;
  margin-bottom: 12px;
}

.skeleton-text:last-of-type {
  width: 80%;
}

.skeleton-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 20px 0;
}

.skeleton-meta-item {
  height: 36px;
  width: 120px;
  border-radius: 20px;
}

.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
}

.skeleton-list-item {
  height: 16px;
  width: 100%;
  border-radius: 4px;
}

.skeleton-list-item:last-child {
  width: 60%;
}

.skeleton-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 20px 0;
}

.skeleton-detail {
  height: 24px;
  width: 180px;
  border-radius: 4px;
}

.skeleton-button {
  height: 48px;
  width: 180px;
  border-radius: 30px;
  margin-top: 30px;
}

/* Job Detail Responsive Styles */
@media (min-width: 768px) {
  .job-title-content {
    flex-direction: row;
    align-items: center;
  }

  .apply-btn {
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .jobs-hero-content {
    padding: 0 20px;
  }

  .job-title-section {
    padding: 40px 0;
  }

  .meta-item {
    width: 100%;
  }

  .apply-btn {
    width: 100%;
  }

  .skeleton-meta {
    gap: 10px;
  }

  .skeleton-meta-item {
    width: 100%;
  }

  .skeleton-card {
    padding: 20px;
  }

  .skeleton-title {
    width: 80%;
  }
}

/* Contact Page Styles */
.contact-page {
  width: 100%;
  background-color: var(--color-background);
  color: var(--color-text);
  min-height: 100vh;
}

/* Contact Form Section */
.contact-form-section {
  padding: 100px 0;
  background-color: var(--color-background-alt);
  position: relative;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 4rem;
  align-items: start;
}

/* Contact Information */
.contact-info {
  position: sticky;
  top: 2rem;
}

.info-card {
  background: var(--color-card-bg);
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark-theme .info-card {
  background: #1a1a1a;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.dark-theme .info-card:hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.2),
    0 4px 6px -2px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.info-card h3 {
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  color: var(--color-text);
  font-weight: 700;
  letter-spacing: -0.02em;
}

.info-card p {
  font-size: 1.1rem;
  line-height: 1.7;
  opacity: 0.9;
  margin-bottom: 2rem;
  color: var(--color-text-secondary);
}

.info-items {
  margin-top: 2rem;
}

.info-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(var(--color-accent-rgb), 0.03);
  transition: background-color 0.2s ease;
}

.dark-theme .info-item {
  background: rgba(255, 255, 255, 0.03);
}

.info-item:hover {
  background: rgba(var(--color-accent-rgb), 0.06);
}

.dark-theme .info-item:hover {
  background: rgba(255, 255, 255, 0.06);
}

.info-item strong {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text);
  font-weight: 600;
  font-size: 1.1rem;
}

.info-item p {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: 0;
  font-size: 1rem;
}

/* Map styles */
iframe {
  border-radius: 12px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
  transition: box-shadow 0.3s ease;
}

iframe:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-container {
  background: var(--color-card-bg);
  padding: 3rem;
  border-radius: 16px;
  border: none;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
}

.dark-theme .form-container {
  background: #1a1a1a;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-text);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  background: var(--color-card-bg) !important;
  color: var(--color-text);
  font-size: 1rem;
  transition: all 0.2s ease;
  outline: none;
}

.dark-theme .form-group input,
.dark-theme .form-group textarea {
  background: #1a1a1a !important;
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--color-text);
}

.form-group input:hover,
.form-group textarea:hover {
  border-color: var(--color-text-secondary);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--color-accent) !important;
  box-shadow: 0 0 0 3px rgba(var(--color-accent-rgb), 0.15);
}

.dark-theme .form-group input:focus,
.dark-theme .form-group textarea:focus {
  box-shadow: 0 0 0 3px rgba(var(--color-accent-rgb), 0.25);
}

.form-group input.error,
.form-group textarea.error {
  border-color: var(--color-error) !important;
}

.error-message {
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.submit-btn {
  position: relative;
  overflow: hidden;
  background: var(--color-accent);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 2rem;
  animation: fadeIn 0.5s ease;
}

.success-icon {
  width: 60px;
  height: 60px;
  background: var(--color-success);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
  animation: scaleIn 0.5s ease;
}

.success-message h3 {
  font-size: 1.5rem;
  color: var(--color-text);
  margin-bottom: 1rem;
}

.success-message p {
  color: var(--color-text-secondary);
  font-size: 1.1rem;
}

/* Contact Page Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0); }
  to { transform: scale(1); }
}

/* Contact Page Responsive Styles */
@media (max-width: 1024px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .info-card,
  .form-container {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-info {
    position: static;
  }

  .contact-form-section {
    padding: 60px 0;
  }
}

@media (max-width: 480px) {
  .contact-form-section {
    padding: 40px 0;
  }

  .info-card,
  .form-container {
    padding: 1.5rem;
  }

  .submit-btn {
    width: 100%;
  }

  .info-item {
    padding: 0.75rem;
  }
}

/* Map styles */
.dark-theme iframe {
  filter: invert(0.9) hue-rotate(180deg);
}

.badge-container {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  background-color: var(--color-background-alt);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.badge.active {
  background-color: var(--color-accent-light);
  color: var(--color-accent);
  border-color: var(--color-accent);
  opacity: 1;
}

.badge.green {
  background-color: rgba(52, 168, 83, 0.1);
  color: #34a853;
  border-color: #34a853;
  opacity: 1;
}
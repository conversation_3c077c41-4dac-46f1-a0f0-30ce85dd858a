"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom"
import { ArrowLeft, MapPin, Clock, Briefcase, DollarSign, ChevronRight } from "lucide-react"
import "../App.css"
import axios from 'axios'
import { getJobById } from "../services/jobService"
import ApplicationModal from "../components/ApplicationModal"

function JobDetailPage() {
  const { id } = useParams()
  const [job, setJob] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  useEffect(() => {
    const fetchJobDetail = async () => {
      try {
        const jobData = await getJobById(id);
        setJob(jobData);
      } catch (error) {
        setError('Failed to load job details');
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetail();
  }, [id]);

  if (error) return (
    <div className="error-container">
      <div className="error">{error}</div>
      <Link to="/jobs" className="back-link">
        <ArrowLeft size={16} /> Back to jobs
      </Link>
    </div>
  );

  return (
    <div className="jobs-page">
      {/* Jobs Hero */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Join Our Team<span className="blinking-underscore">_</span></h1>
            <p className="hero-subtitle">Discover exciting career opportunities at Tekai Vietnam</p>
          </div>
        </div>
      </section>

      {/* Job Title Section */}
      <section className="job-title-section">
        <div className="container">
          <div className="job-title-content">
            <div>
              <Link to="/jobs" className="back-link">
                <ArrowLeft size={16} /> Back to jobs
              </Link>
              {loading ? (
                <>
                  <div className="skeleton skeleton-title"></div>
                  <div className="skeleton-meta">
                    <div className="skeleton skeleton-meta-item"></div>
                    <div className="skeleton skeleton-meta-item"></div>
                    <div className="skeleton skeleton-meta-item"></div>
                    <div className="skeleton skeleton-meta-item"></div>
                  </div>
                </>
              ) : (
                <>
                  <h1>{job?.title}<span className="blinking-underscore">_</span></h1>
                  <div className="job-meta">
                    <div className="meta-item">
                      <MapPin size={16} />
                      <span>{job?.location}</span>
                    </div>
                    <div className="meta-item">
                      <Clock size={16} />
                      <span>{job?.type}</span>
                    </div>
                    <div className="meta-item">
                      <Briefcase size={16} />
                      <span>Code: {job?.code}</span>
                    </div>
                    <div className="meta-item">
                      <DollarSign size={16} />
                      <span>{job?.salary}</span>
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className="apply-btn-container">
              <button 
                className="btn primary-btn apply-btn" 
                onClick={handleOpenModal} 
                disabled={loading || job?.status?.toLowerCase() === 'closed'}
              >
                Apply Now <ChevronRight size={16} />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="main-content">
        <div className="container">
          <div className="content-grid">
            {/* Left Column - Job Details */}
            <div className="main-column">
              {loading ? (
                <>
                  <div className="card skeleton-card">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-text"></div>
                    <div className="skeleton skeleton-text"></div>
                    <div className="skeleton skeleton-text"></div>
                  </div>
                  <div className="card skeleton-card">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-list">
                      <div className="skeleton skeleton-list-item"></div>
                      <div className="skeleton skeleton-list-item"></div>
                      <div className="skeleton skeleton-list-item"></div>
                    </div>
                  </div>
                  <div className="card skeleton-card">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-subtitle"></div>
                    <div className="skeleton skeleton-list">
                      <div className="skeleton skeleton-list-item"></div>
                      <div className="skeleton skeleton-list-item"></div>
                      <div className="skeleton skeleton-list-item"></div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="card">
                    <h2>Job Overview</h2>
                    <p>{job?.description}</p>
                  </div>

                  <div className="card">
                    <h2>Key Responsibilities</h2>
                    <ul>
                      {job?.responsibilities?.map((responsibility, index) => (
                        <li key={index}>{responsibility}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="card">
                    <h2>Job Requirements</h2>
                    <h3>Technical Skills:</h3>
                    <ul>
                      {job?.technicalSkills?.map((skill, index) => (
                        <li key={index}>{skill}</li>
                      ))}
                    </ul>
                    {job?.softSkills && job.softSkills.length > 0 && job.softSkills[0].trim() !== '' && (
                      <>
                        <h3>Soft Skills:</h3>
                        <ul>
                          {job.softSkills.map((skill, index) => (
                            <li key={index}>{skill}</li>
                          ))}
                        </ul>
                      </>
                    )}
                  </div>
                </>
              )}
            </div>

            {/* Right Column - Additional Info */}
            <div className="sidebar">
              {loading ? (
                <>
                  <div className="card skeleton-card">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton-details">
                      <div className="skeleton skeleton-detail"></div>
                      <div className="skeleton skeleton-detail"></div>
                      <div className="skeleton skeleton-detail"></div>
                      <div className="skeleton skeleton-detail"></div>
                    </div>
                  </div>
                  <div className="card skeleton-card">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-list">
                      <div className="skeleton skeleton-list-item"></div>
                      <div className="skeleton skeleton-list-item"></div>
                      <div className="skeleton skeleton-list-item"></div>
                    </div>
                  </div>
                  <div className="card skeleton-card highlight-card">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-text"></div>
                    <div className="skeleton skeleton-button"></div>
                  </div>
                </>
              ) : (
                <>
                  <div className="card">
                    <h2>Position Details</h2>
                    <div className="details-list">
                      <div className="detail-item">
                        <h3>Location</h3>
                        <p>{job?.fullAddress}</p>
                      </div>
                      <div className="detail-item">
                        <h3>Working Hours</h3>
                        <p>{job?.workingHours}</p>
                      </div>
                      <div className="detail-item">
                        <h3>Employment Type</h3>
                        <div className="badge-container">
                          {["Full-time", "Part-time", "Contractor", "Intern", "Remote"].map((type) => (
                            <span 
                              key={type}
                              className={`badge ${job?.type === type ? "active" : ""}`}
                            >
                              {type}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="detail-item">
                        <h3>Salary</h3>
                        <p>{job?.salary}</p>
                      </div>
                      <div className="detail-item">
                        <h3>Status</h3>
                        <div className="badge-container">
                          <span className={`status-badge ${job?.status?.toLowerCase()}`}>
                            {job?.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {job?.benefits && job.benefits.length > 0 && job.benefits[0].trim() !== '' && (
                    <div className="card">
                      <h2>Benefits & Welfare</h2>
                      <ul className="benefits-list">
                        {job.benefits.map((benefit, index) => (
                          <li key={index}>
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="card highlight-card">
                    <h2>Apply Now</h2>
                    <p>Interested in this position? Submit your application today!</p>
                    <button 
                      className="btn primary-btn full-width apply-btn" 
                      onClick={handleOpenModal}
                      disabled={loading || job?.status?.toLowerCase() === 'closed'}
                    >
                      Apply Now <ChevronRight size={16} />
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Jobs CTA Section */}
      <section className="jobs-cta">
        <div className="container">
          <div className="jobs-cta-content">
            <h2>Don't see a position that fits your skills?</h2>
            <p>
              We're always looking for talented individuals to join our team. Send us your resume and we'll contact you
              when a suitable position opens up.
            </p>
            <button 
              className="btn primary-btn cta-btn" 
              onClick={() => handleOpenModal()}
              disabled={loading || job?.status?.toLowerCase() === 'closed'}
            >
              Submit your resume <ChevronRight size={16} />
            </button>
          </div>
        </div>
      </section>
      
      <ApplicationModal 
        isOpen={isModalOpen} 
        onClose={handleCloseModal} 
        job={{
          id: job?.id,
          title: job?.title || 'General Application'
        }} 
      />
    </div>
  )
}

export default JobDetailPage

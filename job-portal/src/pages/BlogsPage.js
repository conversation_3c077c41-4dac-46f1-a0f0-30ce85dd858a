"use client"

import { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { ArrowRight, Search, Calendar, User, Tag } from "lucide-react"
import "../styles/BlogsPage.css"
import { getAllBlogs, getBlogCategories } from "../services/blogService"
import { generateInitialImage } from "../utils/imageGenerator"

export const BLOG_CATEGORIES = ['Technology', 'Software Development',  'Company News', 'Industry Insights', 'Career Tips', 'Events'];

const isNewBlog = (createdAt) => {
  const blogDate = new Date(createdAt);
  const now = new Date();
  const diffTime = Math.abs(now - blogDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 10;
};

function BlogsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState({ name: "All", slug: "all" })
  const [blogs, setBlogs] = useState([])
  const [categories, setCategories] = useState([{ name: "All", slug: "all" }])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load categories only once on initial render
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await getBlogCategories();
        // remove categories that name is 'Blogs' 
        const filteredCategories = categoriesData.filter((category) => category.name !== 'Blogs');
        setCategories([{ name: "All", slug: "all" }, ...filteredCategories]);
      } catch (error) {
        setError('Failed to load categories. Please try again later.');
      }
    };
    loadCategories();
  }, []);

  // Load blogs when category changes
  useEffect(() => {
    const loadBlogs = async () => {
      try {
        setLoading(true);
        setError(null);
        const blogsData = await getAllBlogs(filterCategory.slug === "all" ? "" : filterCategory.slug);
        setBlogs(blogsData);
      } catch (error) {
        setError('Failed to load blogs. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    loadBlogs();
  }, [filterCategory]);

  const filteredBlogs = blogs.filter((blog) => {
    const matchesSearch =
      blog.title.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  if (loading) {
    return (
      <div className="blogs-page">
        {/* Blogs Hero */}
      <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              <h1>Our Blogs<span className="blinking-underscore">_</span></h1>
              <p>Insights, news, and updates from Tekai Vietnam</p>
            </div>
          </div>
        </section>
        {/* Blogs Filter */}
        <section className="blogs-filter">
          <div className="container">
            <div className="filter-container">
              <div className="search-box">
                <Search size={20} />
                <input
                  type="text"
                  placeholder="Search for blogs..."
                  disabled
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Loading Skeleton */}
        <section className="blogs-listing">
          <div className="container">
            <div className="blogs-grid">
              {[1, 2, 3].map((index) => (
                <div className="skeleton-blog-card" key={index}>
                  <div className="skeleton skeleton-image"></div>
                  <div className="skeleton-content">
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-description"></div>
                    <div className="skeleton skeleton-description"></div>
                    <div className="skeleton-details">
                      <div className="skeleton skeleton-detail"></div>
                      <div className="skeleton skeleton-detail"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    );
  }

  if (error) {
    return (
      <div className="blogs-page">
        <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              <h1>Our Blogs<span className="blinking-underscore">_</span></h1>
              <p>Insights, news, and updates from Tekai Vietnam</p>
            </div>
          </div>
        </section>
        <div className="error-message">
          <p>{error}</p>
          <button onClick={() => window.location.reload()} className="btn primary-btn">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="blogs-page">
      {/* Blogs Hero */}
      <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              <h1>Our Blogs<span className="blinking-underscore">_</span></h1>
              <p>Insights, news, and updates from Tekai Vietnam</p>
            </div>
          </div>
        </section>

      {/* Blogs Filter */}
      <section className="blogs-filter">
        <div className="container">
          <div className="filter-container">
            <div className="search-box">
              <Search size={20} />
              <input
                type="text"
                placeholder="Search for blogs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="category-filters">
              {categories.map((category) => (
                <button
                  key={category.slug}
                  className={`category-btn ${filterCategory.slug === category.slug ? "active" : ""}`}
                  onClick={() => setFilterCategory(category)}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blogs Listing */}
      <section className="blogs-listing">
        <div className="container">
          <div className="blogs-grid">
            {filteredBlogs.length > 0 ? (
              filteredBlogs.map((blog) => (
                <div className="blog-card" key={blog.id}>
                  <div className="blog-image">
                    <img 
                      src={blog.imageUrl || generateInitialImage(blog.title)} 
                      alt={blog.title} 
                    />
                  </div>
                  <div className="blog-content">
                    <div className="blog-header">
                      <div className="blog-badges">
                        {isNewBlog(blog.createdAt) && <span className="new-badge" style={{backgroundColor: '#980928', color: 'white'}}>New</span>}
                        <span className="category-tag">{blog.category.name}</span>
                      </div>
                      <h2>{blog.title}</h2>
                    </div>
                    <div className="blog-details">
                      <div>
                        <span style={{ fontStyle: 'italic' }}>{new Date(blog.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}</span>
                      </div>
                      <Link to={`/blogs/${blog.slug}`} className="read-more-btn">
                        Read More <ArrowRight size={14} />
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="no-blogs-found">
                <h3>No blogs found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}

export default BlogsPage 
"use client"

import { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { ArrowRight, Users, Award, Globe, Code, Briefcase, Smartphone, CheckCircle } from "lucide-react"
import "../App.css"
import { getAllJobs } from "../services/jobService"

function HomePage({ openModal }) {
  const [featuredJobs, setFeaturedJobs] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const loadFeaturedJobs = async () => {
      try {
        const jobs = await getAllJobs()
        // Filter featured jobs and limit to 3
        const featured = jobs.filter(job => job.featured).slice(0, 3)
        setFeaturedJobs(featured)
      } catch (error) {
        setError('Failed to load featured jobs')
      } finally {
        setLoading(false)
      }
    }

    loadFeaturedJobs()
  }, [])

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Lasting Software, Lasting Partnerships<span className="blinking-underscore">_</span></h1>
            <p className="hero-subtitle">
              Tekai Vietnam delivers high-quality software solutions with a focus on long-term partnerships and
              sustainable growth.
            </p>
            <div className="hero-buttons">
              <Link to="/contact" className="btn primary-btn hero-btn">
                Let's talk <ArrowRight size={16} />
              </Link>
              <Link to="/jobs" className="btn secondary-btn hero-btn">
                View open positions <ArrowRight size={16} />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="company-overview">
        <div className="container">
          <div className="section-header">
            <h2>About Tekai Vietnam<span className="text-accent">_</span></h2>
            <p>
              Established in 2024, Tekai Vietnam is a dynamic software development company specializing in delivering
              high-quality solutions for businesses worldwide.
            </p>
          </div>

          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <Users />
              </div>
              <h3>50+</h3>
              <p>Talented Professionals</p>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Award />
              </div>
              <h3>1+</h3>
              <p>Years of Excellence</p>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Globe />
              </div>
              <h3>10+</h3>
              <p>Global Clients</p>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Code />
              </div>
              <h3>25+</h3>
              <p>Successful Projects</p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="company-overview">
        <div className="container">
          <div className="section-header">
            <h2>
              Our Services<span className="text-accent">_</span>
            </h2>
            <p>We provide comprehensive software development services tailored to your business needs.</p>
          </div>

          <div className="services-grid">
            <div className="service-card service-card-purple">
              <div className="service-header">
                <div className="service-icon">
                  <Code size={24} />
                </div>
                <h3>Custom Software Development</h3>
              </div>
              <p>
                Tailored solutions designed specifically for your business requirements, ensuring optimal performance
                and scalability.
              </p>
            </div>
            <div className="service-card service-card-blue">
              <div className="service-header">
                <div className="service-icon">
                  <Globe size={24} />
                </div>
                <h3>Web Application Development</h3>
              </div>
              <p>
                Modern, responsive web applications built with the latest technologies to provide exceptional user
                experiences.
              </p>
            </div>
            <div className="service-card service-card-green">
              <div className="service-header">
                <div className="service-icon">
                  <Smartphone size={24} />
                </div>
                <h3>Mobile App Development</h3>
              </div>
              <p>Native and cross-platform mobile applications that deliver seamless experiences across all devices.</p>
            </div>
            <div className="service-card service-card-orange">
              <div className="service-header">
                <div className="service-icon">
                  <CheckCircle size={24} />
                </div>
                <h3>Quality Assurance & Testing</h3>
              </div>
              <p>
                Comprehensive testing services to ensure your software meets the highest quality standards and performs
                flawlessly.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Jobs Section */}
      <section className="company-overview">
        <div className="container">
          <div className="section-header">
            <h2>Join Our Team<span className="text-accent">_</span></h2>
            <p>Explore exciting career opportunities at Tekai Vietnam and be part of our growing team.</p>
          </div>

          <div className="jobs-preview">
            {loading ? (
              // Loading skeleton
              Array(3).fill(null).map((_, index) => (
                <div key={index} className="job-card">
                  <div className="job-card-content">
                    <div className="skeleton skeleton-badge"></div>
                    <div className="skeleton skeleton-title"></div>
                    <div className="skeleton skeleton-text"></div>
                    <div className="skeleton skeleton-meta">
                      <div className="skeleton skeleton-meta-item"></div>
                      <div className="skeleton skeleton-meta-item"></div>
                    </div>
                  </div>
                  <div className="skeleton skeleton-link"></div>
                </div>
              ))
            ) : featuredJobs.length > 0 ? (
              featuredJobs.map(job => (
                <div key={job.id} className="job-card">
                  <div className="job-card-content">
                    {job.featured && <span className="job-badge">Featured</span>}
                    <h3>{job.title}</h3>
                    <p>{job.description}</p>
                    <div className="job-meta">
                      <span>
                        <Briefcase size={16} /> {job.type}
                      </span>
                      <span>{job.location}</span>
                    </div>
                  </div>
                  <Link to={`/jobs/${job.id}`} className="job-link">
                    View Details <ArrowRight size={16} />
                  </Link>
                </div>
              ))
            ) : (
              <div className="no-jobs-message">
                <p>No featured jobs available at the moment.</p>
              </div>
            )}
          </div>

          <div className="view-all-jobs">
            <Link to="/jobs" className="btn primary-btn">
              View all positions <ArrowRight size={16} />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to start your career with us?</h2>
            <p>Join our team of talented professionals and work on exciting projects in a collaborative environment.</p>
              <Link to="/contact" className="btn primary-btn cta-btn">
                Get in touch <ArrowRight size={16} />
              </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default HomePage

.blog-detail-page {
  padding: 2rem 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.blog-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 1rem;
}

.blog-detail-back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  text-decoration: none;
  margin-bottom: 2rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.blog-detail-back-link:hover {
  background-color: #eee;
}

.blog-detail-article {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.blog-detail-header {
  margin-bottom: 2rem;
}

.blog-detail-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.blog-detail-meta-top {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: #666;
}

.blog-detail-category, .blog-detail-date, .blog-detail-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-detail-category-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background-color: #e9ecef;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

.blog-detail-image {
  margin: 2rem 0;
  border-radius: 8px;
  overflow: hidden;
}

.blog-detail-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.blog-detail-content {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #444;
  margin-bottom: 2rem;
}

/* Quill Editor Content Styles */
.blog-detail-content .ql-editor {
  padding: 0;
}

/* Typography */
.blog-detail-content h1,
.blog-detail-content h2,
.blog-detail-content h3,
.blog-detail-content h4,
.blog-detail-content h5,
.blog-detail-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.3;
  color: #333;
}

.blog-detail-content h1 { font-size: 2.5rem; }
.blog-detail-content h2 { font-size: 2rem; }
.blog-detail-content h3 { font-size: 1.75rem; }
.blog-detail-content h4 { font-size: 1.5rem; }
.blog-detail-content h5 { font-size: 1.25rem; }
.blog-detail-content h6 { font-size: 1.1rem; }

/* Paragraphs and Text */
.blog-detail-content p {
  margin-bottom: 1.5em;
}

.blog-detail-content strong {
  font-weight: 600;
  color: #333;
}

.blog-detail-content em {
  font-style: italic;
}

/* Lists */
.blog-detail-content ul,
.blog-detail-content ol {
  margin: 1em 0;
  padding-left: 2em;
}

.blog-detail-content li {
  margin-bottom: 0.5em;
}

.blog-detail-content ul {
  list-style-type: disc;
}

.blog-detail-content ol {
  list-style-type: decimal;
}

/* Links */
.blog-detail-content a {
  color: #2563eb;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.blog-detail-content a:hover {
  color: #1d4ed8;
  border-bottom-color: currentColor;
}

/* Blockquotes */
.blog-detail-content blockquote {
  margin: 1.5em 0;
  padding: 1em 1.5em;
  border-left: 4px solid #e2e8f0;
  background-color: #f8fafc;
  color: #475569;
  font-style: italic;
}

/* Code Blocks */
.blog-detail-content pre {
  margin: 1.5em 0;
  padding: 1em;
  background-color: #1e293b;
  color: #e2e8f0;
  border-radius: 0.375rem;
  overflow-x: auto;
}

.blog-detail-content code {
  font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', monospace;
  font-size: 0.9em;
}

/* Images */
.blog-detail-content img {
  max-width: 100%;
  height: auto;
  margin: 1.5em 0;
  border-radius: 0.375rem;
}

/* Tables */
.blog-detail-content table {
  width: 100%;
  margin: 1.5em 0;
  border-collapse: collapse;
}

.blog-detail-content th,
.blog-detail-content td {
  padding: 0.75em;
  border: 1px solid #e2e8f0;
}

.blog-detail-content th {
  background-color: #f8fafc;
  font-weight: 600;
  text-align: left;
}

/* Alignments */
.blog-detail-content .ql-align-center {
  text-align: center;
}

.blog-detail-content .ql-align-right {
  text-align: right;
}

.blog-detail-content .ql-align-justify {
  text-align: justify;
}

/* Indentation */
.blog-detail-content .ql-indent-1 { margin-left: 3em; }
.blog-detail-content .ql-indent-2 { margin-left: 6em; }
.blog-detail-content .ql-indent-3 { margin-left: 9em; }
.blog-detail-content .ql-indent-4 { margin-left: 12em; }
.blog-detail-content .ql-indent-5 { margin-left: 15em; }
.blog-detail-content .ql-indent-6 { margin-left: 18em; }
.blog-detail-content .ql-indent-7 { margin-left: 21em; }
.blog-detail-content .ql-indent-8 { margin-left: 24em; }

/* Responsive Adjustments */
@media (max-width: 768px) {
  .blog-detail-content {
    font-size: 1rem;
  }

  .blog-detail-content h1 { font-size: 2rem; }
  .blog-detail-content h2 { font-size: 1.75rem; }
  .blog-detail-content h3 { font-size: 1.5rem; }
  .blog-detail-content h4 { font-size: 1.25rem; }
  .blog-detail-content h5 { font-size: 1.1rem; }
  .blog-detail-content h6 { font-size: 1rem; }

  .blog-detail-content pre {
    font-size: 0.875rem;
  }
}

.blog-detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.blog-detail-tags-container {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.blog-detail-tag-badge {
  background-color: #f1f3f5;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  color: #495057;
}

.blog-detail-share-buttons {
  display: flex;
  gap: 0.5rem;
}

.blog-detail-share-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  font-weight: 500;
}

.blog-detail-share-btn.facebook {
  background-color: #1877f2;
  color: white;
}

.blog-detail-share-btn.facebook:hover {
  background-color: #0d6efd;
}

.blog-detail-share-btn.linkedin {
  background-color: #0a66c2;
  color: white;
}

.blog-detail-share-btn.copy {
  background-color: #6c757d;
  color: white;
  position: relative;
}

.blog-detail-share-btn.copy:hover {
  background-color: #5a6268;
}

.blog-detail-share-btn.share {
  background-color: #28a745;
  color: white;
}

.blog-detail-share-btn.share:hover {
  background-color: #218838;
}

.blog-detail-share-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #000;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  margin-bottom: 8px;
  animation: fadeIn 0.2s ease-in-out;
}

.blog-detail-share-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 4px;
  border-style: solid;
  border-color: #000 transparent transparent transparent;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* Skeleton loading styles */
.blog-detail-skeleton {
  background: #e9ecef;
  background: linear-gradient(
    110deg,
    #ececec 8%,
    #f5f5f5 18%,
    #ececec 33%
  );
  border-radius: 5px;
  background-size: 200% 100%;
  animation: 1.5s blog-detail-shine linear infinite;
}

.blog-detail-skeleton-wrapper {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.blog-detail-skeleton-image {
  width: 100%;
  height: 400px;
  margin-bottom: 2rem;
}

.blog-detail-skeleton-title {
  height: 40px;
  margin-bottom: 1rem;
  width: 80%;
}

.blog-detail-skeleton-meta {
  height: 20px;
  margin-bottom: 2rem;
  width: 40%;
}

.blog-detail-skeleton-content {
  height: 20px;
  margin-bottom: 1rem;
  width: 100%;
}

@keyframes blog-detail-shine {
  to {
    background-position-x: -200%;
  }
}

/* Error container styles */
.blog-detail-error-container {
  text-align: center;
  padding: 2rem;
}

.blog-detail-error {
  color: #dc3545;
  font-size: 1.2rem;
  margin-bottom: 1rem;
} 
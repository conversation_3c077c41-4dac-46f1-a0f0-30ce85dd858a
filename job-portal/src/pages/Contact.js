"use client"

import { useState } from "react"
import { ArrowRight } from "lucide-react"
import { API_ENDPOINTS } from "../constants/api"
import "../App.css"

export default function Contact() {
  const [formData, setFormData] = useState({
    fullname: "",
    phoneNumber: "",
    email: "",
    content: ""
  })

  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [error, setError] = useState(null)

  const mapContainerStyle = {
    width: '100%',
    height: '300px',
    borderRadius: '8px',
    marginTop: '20px'
  }

  const center = {
    lat: 21.067364714806928,
    lng: 105.81019213854333
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.fullname || formData.fullname.length < 2) {
      newErrors.fullname = "Full name must be at least 2 characters"
    }

    if (!formData.phoneNumber || formData.phoneNumber.length < 10) {
      newErrors.phoneNumber = "Phone number must be at least 10 digits"
    }

    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!formData.content || formData.content.length < 10) {
      newErrors.content = "Message must be at least 10 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }))
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch(API_ENDPOINTS.CONTACTS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      setSubmitSuccess(true)
      setFormData({
        fullname: "",
        phoneNumber: "",
        email: "",
        content: ""
      })
      setTimeout(() => setSubmitSuccess(false), 3000)
    } catch (error) {
      setError('Failed to submit form. Please try again.');
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="jobs-page">
      {/* Contact Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>Get in Touch<span className="blinking-underscore">_</span></h1>
            {error && <div className="error-message">{error}</div>}
            <p className="hero-subtitle">
              Have questions or want to discuss your project? We'd love to hear from you.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="contact-form-section">
        <div className="container">
          <div className="contact-grid">
            {/* Contact Information */}
            <div className="contact-info">
              <div className="info-card">
                <h3>Contact Information</h3>
                <p>Feel free to reach out to us through any of these channels:</p>
                <div className="info-items">
                  <div className="info-item">
                    <strong>Address:</strong>
                    <p>17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội</p>
                  </div>
                  <div className="info-item">
                    <strong>Email:</strong>
                    <p><EMAIL></p>
                  </div>
                  <div className="info-item">
                    <strong>Working Hours:</strong>
                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                  </div>
                </div>
                <iframe 
                  title="Office Location"
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3723.137632685997!2d105.8076386755689!3d21.06716448059183!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab002a43a30b%3A0xb62a69ef9e2385e3!2sTekai%20Vi%E1%BB%87t%20Nam!5e0!3m2!1sen!2sus!4v1745202395104!5m2!1sen!2sus"
                  style={{
                    width: '100%',
                    height: '300px',
                    border: 0,
                    borderRadius: '8px'
                  }}
                  allowFullScreen=""
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                />
              </div>
            </div>

            {/* Contact Form */}
            <div className="form-container">
              {submitSuccess ? (
                <div className="success-message">
                  <div className="success-icon">✓</div>
                  <h3>Message Sent Successfully!</h3>
                  <p>Thank you for contacting us. We'll get back to you soon.</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="contact-form">
                  <div className="form-group">
                    <label htmlFor="fullname">Full Name *</label>
                    <input
                      type="text"
                      id="fullname"
                      name="fullname"
                      value={formData.fullname}
                      onChange={handleChange}
                      className={errors.fullname ? 'error' : ''}
                    />
                    {errors.fullname && <span className="error-message">{errors.fullname}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="phoneNumber">Phone Number *</label>
                    <input
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleChange}
                      className={errors.phoneNumber ? 'error' : ''}
                    />
                    {errors.phoneNumber && <span className="error-message">{errors.phoneNumber}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="email">Email Address *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className={errors.email ? 'error' : ''}
                    />
                    {errors.email && <span className="error-message">{errors.email}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="content">Message *</label>
                    <textarea
                      id="content"
                      name="content"
                      rows="4"
                      value={formData.content}
                      onChange={handleChange}
                      className={`${errors.content ? 'error' : ''}`}
                    ></textarea>
                    {errors.content && <span className="error-message">{errors.content}</span>}
                  </div>

                  <button 
                    type="submit" 
                    className={`btn primary-btn submit-btn ${isSubmitting ? 'submitting' : ''}`}
                    disabled={isSubmitting}
                    style={{ marginLeft: 'auto', display: 'block' }}
                  >
                    {isSubmitting ? (
                      "Sending..."
                    ) : (
                      <>
                        Send Message <ArrowRight size={16} />
                      </>
                    )}
                  </button>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
} 
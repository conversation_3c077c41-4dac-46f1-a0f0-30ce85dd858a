"use client"

import React from "react"
import "../App.css"

function About() {
  return (
    <div className="about-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1>About Us<span className="blinking-underscore">_</span></h1>
            <p className="hero-subtitle">
              Connecting talent with opportunity, building careers that matter.
            </p>
          </div>
        </div>
      </section>

      {/* Company Overview Section */}
      <section className="company-overview">
        <div className="container">
          <h2>Our Story</h2>
          <p>
            Founded with a vision to revolutionize the job search experience, we've grown into a
            trusted platform connecting talented professionals with innovative companies. Our mission
            is to make the job search process more transparent, efficient, and human-centric.
          </p>
          <div className="stats-grid">
            <div className="stat-card">
              <h3>10+</h3>
              <p>Job Placements</p>
            </div>
            <div className="stat-card">
              <h3>20+</h3>
              <p>Partner Companies</p>
            </div>
            <div className="stat-card">
              <h3>95%</h3>
              <p>Success Rate</p>
            </div>
            <div className="stat-card">
              <h3>24/7</h3>
              <p>Support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="values-section">
        <div className="container">
          <h2>Our Values</h2>
          <div className="values-grid">
            <div className="value-card">
              <h3>Innovation</h3>
              <p>
                We constantly evolve and adapt to bring you the latest in job search technology
                and career development tools.
              </p>
            </div>
            <div className="value-card">
              <h3>Transparency</h3>
              <p>
                We believe in clear, honest communication and providing accurate, up-to-date
                information about job opportunities.
              </p>
            </div>
            <div className="value-card">
              <h3>Excellence</h3>
              <p>
                We strive for excellence in everything we do, from our platform's user experience
                to our customer support.
              </p>
            </div>
            <div className="value-card">
              <h3>Community</h3>
              <p>
                We foster a supportive community where job seekers and employers can connect and
                grow together.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="team-section">
        <div className="container">
          <h2>Our Team</h2>
          <p className="section-subtitle">
            Meet the passionate individuals driving our mission forward
          </p>
          <div className="team-grid">
            <div className="team-member">
              <div className="member-image">
                <img src="/images/trung-icon.png" alt="Trung Nguyen" />
              </div>
              <h3>Trung Nguyen</h3>
              <p className="member-role">CEO at Tekai & Chairman</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/jarvis-icon.png" alt="Jarvis Luong" />
              </div>
              <h3>Jarvis Luong</h3>
              <p className="member-role">CIO at Tekai & Co-Founder</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/ky-icon.png" alt="Lucas Pham" />
              </div>
              <h3>Lucas Pham</h3>
              <p className="member-role">CEO at Tekai VN & Co-Founder</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/hieu-ng-icon.png" alt="Hieu Nguyen" />
              </div>
              <h3>Hieu Nguyen</h3>
              <p className="member-role">CTO at Tekai</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/dong-pham.png" alt="Dong Pham" />
              </div>
              <h3>Dong Pham</h3>
              <p className="member-role">Senior Test Automation Engineer</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/tuan.png" alt="Mark Phung" />
              </div>
              <h3>Mark Phung</h3>
              <p className="member-role">Full Stack Developer</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/manh.png" alt="Dom Nguyen" />
              </div>
              <h3>Dom Nguyen</h3>
              <p className="member-role">Frontend Developer</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/linh.png" alt="Terry Nguyen" />
              </div>
              <h3>Terry Nguyen</h3>
              <p className="member-role">Python Developer</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/hieu-icon.png" alt="Johny Dang" />
              </div>
              <h3>Johny Dang</h3>
              <p className="member-role">Mobile Developer</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/duy.png" alt="Elon Pham" />
              </div>
              <h3>Elon Pham</h3>
              <p className="member-role">.Net Developer</p>
            </div>
            <div className="team-member">
              <div className="member-image">
                <img src="/images/ninh.png" alt="Lily Luu" />
              </div>
              <h3>Lily Luu</h3>
              <p className="member-role">HR administrative specialist</p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="mission-section">
        <div className="container">
          <div className="mission-content">
            <h2>Our Mission</h2>
            <p>
              To transform the way people find and secure their dream jobs by providing a
              transparent, efficient, and user-friendly platform that puts the human experience
              first.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About 
"use client"

import { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { ArrowRight, Search, Briefcase, MapPin, Clock, DollarSign } from "lucide-react"
import "../App.css"
import { getAllJobs } from "../services/jobService"
import ApplicationModal from "../components/ApplicationModal"

export const JOB_CATEGORIES = ['Management', 'Engineering', 'Quality Assurance', 'Human Resources', 'Design', 'Marketing'];

function JobsPage() { // Remove openModal prop
  const [searchTerm, setSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState("All")
  const [jobs, setJobs] = useState([])
  const [loading, setLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedJob, setSelectedJob] = useState(null)
  const [error, setError] = useState(null)

  const handleOpenModal = (job) => {
    setSelectedJob(job)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedJob(null)
  }

  useEffect(() => {
    const loadJobs = async () => {
      try {
        const jobsData = await getAllJobs();
        //sort by createdAt descending and move closed jobs to the end
        const sortedJobs = jobsData.sort((a, b) => {
          if (a.status === 'Closed') return 1;
          if (b.status === 'Closed') return -1;
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
        setJobs(sortedJobs);
      } catch (error) {
        setError('Failed to load jobs. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    loadJobs();
  }, []);

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = filterCategory === "All" || job.category === filterCategory

    return matchesSearch && matchesCategory
  })

  const categories = ["All", ...JOB_CATEGORIES]

  if (loading) {
    return (
      <div className="jobs-page">
        {/* Jobs Hero */}
        <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              <h1>Join Our Team<span className="blinking-underscore">_</span></h1>
              <p className="hero-subtitle">Discover exciting career opportunities at Tekai Vietnam</p>
            </div>
          </div>
        </section>

        {/* Jobs Filter */}
        <section className="jobs-filter">
          <div className="container">
            <div className="filter-container">
              <div className="search-box">
                <Search size={20} />
                <input
                  type="text"
                  placeholder="Search for jobs..."
                  disabled
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="category-filters">
                <button className="category-btn active">All</button>
                <button className="category-btn">Engineering</button>
                <button className="category-btn">Design</button>
                <button className="category-btn">Marketing</button>
              </div>
            </div>
          </div>
        </section>

        {/* Loading Skeleton */}
        <section className="jobs-listing">
          <div className="container">
            <div className="jobs-grid">
              {[1, 2, 3].map((index) => (
                <div className="skeleton-job-card" key={index}>
                  <div className="skeleton skeleton-title"></div>
                  <div className="skeleton skeleton-description"></div>
                  <div className="skeleton skeleton-description"></div>
                  <div className="skeleton-details">
                    <div className="skeleton skeleton-detail"></div>
                    <div className="skeleton skeleton-detail"></div>
                    <div className="skeleton skeleton-detail"></div>
                    <div className="skeleton skeleton-detail"></div>
                  </div>
                  <div className="skeleton-actions">
                    <div className="skeleton skeleton-button"></div>
                    <div className="skeleton skeleton-button"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="jobs-cta">
          <div className="container">
            <div className="jobs-cta-content">
              <h2>Don't see a position that fits your skills?</h2>
              <p>
                We're always looking for talented individuals to join our team. Send us your resume and we'll contact you
                when a suitable position opens up.
              </p>
              <button className="btn primary-btn cta-btn" disabled>
                Submit your resume <ArrowRight size={16} />
              </button>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="jobs-page">
      {/* Jobs Hero */}
      <section className="hero-section">
          <div className="container">
            <div className="hero-content">
              <h1>Join Our Team<span className="blinking-underscore">_</span></h1>
              <p>Discover exciting career opportunities at Tekai Vietnam</p>
            </div>
          </div>
        </section>

      {/* Jobs Filter */}
      <section className="jobs-filter">
        <div className="container">
          <div className="filter-container">
            <div className="search-box">
              <Search size={20} />
              <input
                type="text"
                placeholder="Search for jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="category-filters">
              {categories.map((category) => (
                <button
                  key={category}
                  className={`category-btn ${filterCategory === category ? "active" : ""}`}
                  onClick={() => setFilterCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Jobs Listing */}
      <section className="jobs-listing">
        <div className="container">
          <div className="jobs-grid">
            {filteredJobs.length > 0 ? (
              filteredJobs.map((job) => (
                <div className="job-listing-card" key={job.id}>
                  <div className="job-listing-content">
                    <div className="job-listing-header">
                      <h2>{job.title}</h2>
                      <div className="job-badges">
                        {job.featured && <span className="featured-badge">Featured</span>}
                        <span className={`status-badge ${job.status.toLowerCase()}`}>
                          {job.status}
                        </span>
                      </div>
                    </div>
                    <p className="job-description">{job.description}</p>
                    <div className="job-details">
                      <div className="job-detail">
                        <Briefcase size={16} />
                        <span>{job.type}</span>
                      </div>
                      <div className="job-detail">
                        <MapPin size={16} />
                        <span>{job.location}</span>
                      </div>
                      <div className="job-detail">
                        <Clock size={16} />
                        <span>
                          {job.status.toLowerCase() === 'closed' ? 'Closed' : 
                            job.timeAlive ? 
                              `Closes in ${Math.max(0, Math.ceil(job.timeAlive - (new Date() - new Date(job.createdAt)) / (1000 * 60 * 60 * 24)))} days` :
                              'Open until filled'
                          }
                        </span>
                      </div>
                      <div className="job-detail">
                        <DollarSign size={16} />
                        <span>{job.salary}</span>
                      </div>
                    </div>
                  </div>
                  <div className="job-listing-actions">
                    <Link to={`/jobs/${job.slug}`} className="btn secondary-btn view-btn">
                      View Details <ArrowRight size={16} />
                    </Link>
                    {/* Update your Apply Now buttons to use handleOpenModal */}
                    <button 
                      className={`btn primary-btn apply-btn ${job.status.toLowerCase() === 'closed' ? 'disabled' : ''}`} 
                      onClick={() => handleOpenModal({
                        id: job.id,
                        title: job.title
                      })}
                      disabled={job.status.toLowerCase() === 'closed'}
                    >
                      Apply Now <ArrowRight size={16} />
                    </button>
                    
                    {/* Add the ApplicationModal at the bottom of your component */}
                    <ApplicationModal 
                      isOpen={isModalOpen}
                      onClose={handleCloseModal}
                      job={selectedJob}
                    />
                  </div>
                </div>
              ))
            ) : (
              <div className="no-jobs-found">
                <h3>No jobs found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="jobs-cta">
        <div className="container">
          <div className="jobs-cta-content">
            <h2>Don't see a position that fits your skills?</h2>
            <p>
              We're always looking for talented individuals to join our team. Send us your resume and we'll contact you
              when a suitable position opens up.
            </p>
            <button className="btn primary-btn cta-btn" onClick={() => handleOpenModal({
              id: 'general',
              title: 'General Application'
            })}>
              Submit your resume <ArrowRight size={16} />
            </button>
          </div>
        </div>
      </section>

      {/* Move ApplicationModal outside of the job listing loop to the end of the component */}
      <ApplicationModal 
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        job={selectedJob}
      />
    </div>
  )
}

export default JobsPage

"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom"
import { ArrowLeft, Calendar, User, Tag, Facebook, Linkedin, Copy, Share2 } from "lucide-react"
import "../App.css"
import "./BlogPage.css"
import { getBlogById } from "../services/blogService"

function BlogDetailPage() {
  const { id } = useParams()
  const [blog, setBlog] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showShareTooltip, setShowShareTooltip] = useState(false)

  useEffect(() => {
    const fetchBlogDetail = async () => {
      try {
        const blogData = await getBlogById(id);
        setBlog(blogData);
      } catch (error) {
        setError('Failed to load blog post');
      } finally {
        setLoading(false);
      }
    };

    fetchBlogDetail();
  }, [id]);

  const generateShareContent = () => {
    if (!blog) return {};

    // Tạo đoạn mô tả ngắn từ nội dung (loại bỏ HTML tags và giới hạn độ dài)
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = blog.content;
    const plainText = tempDiv.textContent || tempDiv.innerText;
    const description = plainText.substring(0, 200) + (plainText.length > 200 ? '...' : '');

    // Tạo hashtags từ category và tags
    const hashtags = [];
    if (blog.category) {
      hashtags.push(blog.category.name.replace(/\s+/g, ''));
    }
    if (blog.tags) {
      hashtags.push(...blog.tags.map(tag => tag.replace(/\s+/g, '')));
    }

    return {
      title: blog.title,
      description: description,
      hashtags: hashtags.map(tag => '#' + tag).join(' '),
      author: blog.author || '',
      url: window.location.href
    };
  };

  const handleShare = async (platform) => {
    const content = generateShareContent();
    
    switch (platform) {
      case 'facebook': {
        // Facebook chỉ cần URL, họ sẽ tự động lấy metadata từ trang web
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(content.url)}`;
        openShareWindow(shareUrl);
        break;
      }
      case 'linkedin': {
        // LinkedIn cũng chủ yếu dựa vào metadata của trang
        const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(content.url)}`;
        openShareWindow(shareUrl);
        break;
      }
      case 'copy': {
        // Copy tất cả thông tin vào clipboard
        const shareText = `${content.title}\n\n${content.description}\n\n${content.author ? `By ${content.author}\n\n` : ''}${content.hashtags}\n\n${content.url}`;
        
        try {
          await navigator.clipboard.writeText(shareText);
          setShowShareTooltip(true);
          setTimeout(() => setShowShareTooltip(false), 2000);
        } catch (err) {
          setShowShareTooltip(false);
        }
        break;
      }
      case 'native': {
        // Sử dụng Web Share API nếu có hỗ trợ
        if (navigator.share) {
          try {
            await navigator.share({
              title: content.title,
              text: `${content.description}\n\n${content.author ? `By ${content.author}\n\n` : ''}${content.hashtags}`,
              url: content.url
            });
          } catch (err) {
            setError('Failed to share content');
          }
        } else {
          // Fallback to copy link if Web Share API is not supported
          handleShare('copy');
        }
        break;
      }
    }
  };

  const openShareWindow = (url) => {
    const width = 600;
    const height = 400;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;
    
    window.open(
      url,
      '_blank',
      `toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`
    );
  };

  if (loading) {
    return (
      <div className="blog-detail-page">
        <div className="blog-detail-container">
          <div className="blog-detail-skeleton-wrapper">
            <div className="blog-detail-skeleton blog-detail-skeleton-image"></div>
            <div className="blog-detail-skeleton-content">
              <div className="blog-detail-skeleton blog-detail-skeleton-title"></div>
              <div className="blog-detail-skeleton blog-detail-skeleton-meta"></div>
              <div className="blog-detail-skeleton blog-detail-skeleton-content"></div>
              <div className="blog-detail-skeleton blog-detail-skeleton-content"></div>
              <div className="blog-detail-skeleton blog-detail-skeleton-content"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="blog-detail-error-container">
        <div className="blog-detail-error">{error}</div>
        <Link to="/blogs" className="blog-detail-back-link">
          <ArrowLeft size={16} /> Back to blogs
        </Link>
      </div>
    );
  }

  if (!blog) {
    return null;
  }

  return (
    <div className="blog-detail-page">
      <div className="blog-detail-container">
        <Link to="/blogs" className="blog-detail-back-link">
          <ArrowLeft size={16} /> Back to blogs
        </Link>
        
        <article className="blog-detail-article">
          <div className="blog-detail-header">
            <h1>{blog.title}</h1>
            <div className="blog-detail-meta-top">
              <div className="blog-detail-category">
                {blog.category && (
                  <span className="blog-detail-category-badge">
                    <Tag size={14} />
                    {blog.category.name}
                  </span>
                )}
              </div>
              <div className="blog-detail-date">
                <Calendar size={14} />
                <span>{new Date(blog.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</span>
              </div>
            </div>
            {blog.author && (
              <div className="blog-detail-author">
                <User size={16} />
                <span>{blog.author}</span>
              </div>
            )}
          </div>

          {blog.imageUrl && (
            <div className="blog-detail-image">
              <img 
                src={blog.imageUrl} 
                alt={blog.title} 
              />
            </div>
          )}

          <div className="blog-detail-content">
            <div dangerouslySetInnerHTML={{ __html: blog.content }} />
          </div>

          <div className="blog-detail-footer">
            <div className="blog-detail-tags-container">
              {blog.tags && blog.tags.map((tag, index) => (
                <span key={index} className="blog-detail-tag-badge">{tag}</span>
              ))}
            </div>
            <div className="blog-detail-share-buttons">
              <button 
                className="blog-detail-share-btn facebook"
                onClick={() => handleShare('facebook')}
                aria-label="Share on Facebook"
              >
                <Facebook size={16} />
                Facebook
              </button>
              <button 
                className="blog-detail-share-btn linkedin"
                onClick={() => handleShare('linkedin')}
                aria-label="Share on LinkedIn"
              >
                <Linkedin size={16} />
                LinkedIn
              </button>
              <button 
                className="blog-detail-share-btn copy"
                onClick={() => handleShare('copy')}
                aria-label="Copy link and content"
              >
                <Copy size={16} />
                Copy
                {showShareTooltip && (
                  <span className="blog-detail-share-tooltip">Copied!</span>
                )}
              </button>
              <button 
                className="blog-detail-share-btn share"
                onClick={() => handleShare('native')}
                aria-label="Share using native share"
              >
                <Share2 size={16} />
                Share
              </button>
            </div>
          </div>
        </article>
      </div>
    </div>
  );
}

export default BlogDetailPage; 
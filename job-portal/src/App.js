"use client"

import { Routes, Route } from "react-router-dom"
import "./App.css"
import Header from "./components/Header"
import Footer from "./components/Footer"
import HomePage from "./pages/HomePage"
import JobsPage from "./pages/JobsPage"
import JobDetailPage from "./pages/JobDetailPage"
import Contact from "./pages/Contact"
import About from "./pages/About"
import BlogsPage from "./pages/BlogsPage"
import BlogDetailPage from "./pages/BlogDetailPage"

function App() {
  return (
    <div className="app">
      <div className="background-tk">
        {/* <svg viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M100 50H50V450H100V50Z" fill="var(--color-accent)" fillOpacity="0.2" />
          <path d="M250 50H150V200H50V450H100V250H150V450H200V50Z" fill="var(--color-accent)" fillOpacity="0.2" />
        </svg> */}
      </div>
      <Header />
      <main style={{ flex: 1 }}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/jobs" element={<JobsPage />} />
          <Route path="/jobs/:id" element={<JobDetailPage />} />
          <Route path="/about" element={<About />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/blogs" element={<BlogsPage />} />
          <Route path="/blogs/:id" element={<BlogDetailPage />} />
        </Routes>
      </main>
      <Footer />
    </div>
  )
}

export default App

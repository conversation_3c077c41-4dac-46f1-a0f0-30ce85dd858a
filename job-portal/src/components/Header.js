"use client"

import { useState, useEffect } from "react"
import { Link, NavLink, useLocation } from "react-router-dom"
import "./Header.css"
import Logo from "./Logo"
import ThemeToggle from "./ThemeToggle"
import { Menu, X, Home, Briefcase, Info, Mail, ExternalLink, Book } from "lucide-react"

function Header({ openModal }) {
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem("theme")
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
    return savedTheme === "dark" || (!savedTheme && prefersDark) ? "dark" : "light"
  })

  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()

  useEffect(() => {
    const handleThemeChange = () => {
      setTheme(document.documentElement.classList.contains("dark-theme") ? "dark" : "light")
    }

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          handleThemeChange()
        }
      })
    })

    observer.observe(document.documentElement, { attributes: true })

    // Close menu when clicking outside
    const handleClickOutside = (event) => {
      const nav = document.querySelector('.main-nav')
      const menuButton = document.querySelector('.mobile-menu-button')
      if (isMenuOpen && nav && !nav.contains(event.target) && !menuButton.contains(event.target)) {
        setIsMenuOpen(false)
      }
    }

    // Close menu when resizing to desktop
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMenuOpen(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    window.addEventListener('resize', handleResize)

    return () => {
      observer.disconnect()
      document.removeEventListener('click', handleClickOutside)
      window.removeEventListener('resize', handleResize)
    }
  }, [isMenuOpen])

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false)
  }, [location])

  const handleMenuClick = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const handleLinkClick = () => {
    setIsMenuOpen(false)
  }

  return (
    <header className="header">
      <div className="container header-container">
        <Link to="/" className="logo-link" onClick={handleLinkClick}>
          <Logo type={'header'}/>
        </Link>

        <button 
          className="mobile-menu-button" 
          onClick={handleMenuClick}
          aria-label="Toggle menu"
          aria-expanded={isMenuOpen}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        <nav className={`main-nav ${isMenuOpen ? 'show' : ''}`}>
          <NavLink 
            to="/" 
            className={({ isActive }) => (isActive ? "active" : "")}
            onClick={handleLinkClick}
          >
            <Home size={20} />
            Home
          </NavLink>
          <NavLink 
            to="/jobs" 
            className={({ isActive }) => (isActive ? "active" : "")}
            onClick={handleLinkClick}
          >
            <Briefcase size={20} />
            Jobs
          </NavLink>
          <NavLink 
            to="/blogs" 
            className={({ isActive }) => (isActive ? "active" : "")}
            onClick={handleLinkClick}
          >
            <Book size={20} />
            Blogs
          </NavLink>
          <NavLink 
            to="/about" 
            className={({ isActive }) => (isActive ? "active" : "")}
            onClick={handleLinkClick}
          >
            <Info size={20} />
            About
          </NavLink>
          
          <NavLink 
            to="/contact" 
            className={({ isActive }) => (isActive ? "active" : "")}
            onClick={handleLinkClick}
          >
            <Mail size={20} />
            Contact
          </NavLink>
          <a 
            href="https://tekai.fi" 
            target="_blank" 
            rel="noopener noreferrer"
            className="external-link"
            onClick={handleLinkClick}
          >
            <ExternalLink size={20} />
            Tekai.fi
          </a>
        </nav>
        
        <div className="header-actions">
          <ThemeToggle />         
        </div>
      </div>
    </header>
  )
}

export default Header

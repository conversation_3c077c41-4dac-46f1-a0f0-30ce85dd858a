import "./Footer.css"
import { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faFacebookF, faTwitter, faInstagram, faLinkedinIn } from "@fortawesome/free-brands-svg-icons"
import { faMapMarkerAlt } from "@fortawesome/free-solid-svg-icons"
import LogoFooter from "./LogoFooter"

function Footer() {
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem("theme")
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
    return savedTheme === "dark" || (!savedTheme && prefersDark) ? "dark" : "light"
  })

  useEffect(() => {
    const handleThemeChange = () => {
      setTheme(document.documentElement.classList.contains("dark-theme") ? "dark" : "light")
    }

    // Create a MutationObserver to watch for class changes on documentElement
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          handleThemeChange()
        }
      })
    })

    observer.observe(document.documentElement, { attributes: true })

    return () => observer.disconnect()
  }, [])

  const currentYear = new Date().getFullYear()

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-grid">
          <div className="footer-column">
            <div className="footer-logo">
              <LogoFooter />
            </div>
            <p>
              Connecting talented professionals with innovative companies. 
              Our mission is to make the job search process more transparent, 
              efficient, and human-centric.
            </p>
            <p className="address">
              <FontAwesomeIcon icon={faMapMarkerAlt} className="address-icon" />
              17A, Ngõ 603 Lạc Long Quân, Xuân La, Tây Hồ, Hà Nội
            </p>
          </div>
          <div className="footer-column">
            <h3>Quick Links</h3>
            <ul className="footer-links">
              <li>
                <Link to="/">Home</Link>
              </li>
              <li>
                <Link to="/jobs">Jobs</Link>
              </li>
              <li>
                <Link to="/about">About Us</Link>
              </li>
              <li>
                <Link to="/contact">Contact</Link>
              </li>
            </ul>
          </div>
          <div className="footer-column">
            <h3>Connect With Us</h3>
            <div className="social-icons">
              <a href="https://www.facebook.com/tekai.vn" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <FontAwesomeIcon icon={faFacebookF} />
              </a>
             
              <a href="https://www.linkedin.com/company/tekai" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                <FontAwesomeIcon icon={faLinkedinIn} />
              </a>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <p>© {currentYear} Tekai Vietnam JSC. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer

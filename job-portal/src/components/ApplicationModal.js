"use client"

import { useState } from "react"
import "./ApplicationModal.css"
import { submitJobApplication } from '../services/jobApplicationService'

function ApplicationModal({ isOpen, onClose, job = { id: 'general', title: "Senior Automation Test Engineer" } }) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phoneNumber: "",
    cv: null,
  })
  const [fileName, setFileName] = useState("No file chosen")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [error, setError] = useState(null)
  const [fileError, setFileError] = useState(null)

  if (!isOpen) return null

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }

  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      availableDate: date,
    })
  }

  const handleFileChange = (e) => {
    setFileError(null)
    const file = e.target.files[0]
    
    if (file) {
      // Check file size (5MB = 5 * 1024 * 1024 bytes)
      if (file.size > 5 * 1024 * 1024) {
        setFileError('File size must be less than 5MB')
        setFileName("No file chosen")
        setFormData({
          ...formData,
          cv: null,
        })
        e.target.value = null // Reset file input
        return
      }
      
      setFormData({
        ...formData,
        cv: file,
      })
      setFileName(file.name)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    const formDataToSend = new FormData()
    formDataToSend.append('name', formData.name)
    formDataToSend.append('email', formData.email)
    formDataToSend.append('phoneNumber', formData.phoneNumber)
    formDataToSend.append('cv', formData.cv)
    formDataToSend.append('jobId', job.id)
    formDataToSend.append('jobTitle', job.title)
    formDataToSend.append('timestamp', new Date().toISOString())

    try {
      await submitJobApplication(formDataToSend)
      setIsSubmitting(false)
      setSubmitSuccess(true)

      setTimeout(() => {
        setSubmitSuccess(false)
        onClose()
        setFormData({
          name: "",
          email: "",
          phone: "",
          cv: null,
        })
        setFileName("No file chosen")
      }, 2000)
    } catch (error) {
      setError('Failed to submit application. Please try again.')
      setIsSubmitting(false)
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Apply for {job.title}</h2>
          <button className="close-button" onClick={onClose}>
            &times;
          </button>
        </div>

        {submitSuccess ? (
          <div className="success-message">
            <div className="success-icon">✓</div>
            <h3>Application Submitted!</h3>
            <p>Thank you for your interest. We'll be in touch soon.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="application-form">
            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder="Enter your full name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email Address *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder="Enter your email address"
              />
            </div>

            <div className="form-group">
              <label htmlFor="phoneNumber">Phone Number *</label>
              <input
                type="tel"
                id="phoneNumber"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                required
                placeholder="Enter your phone number"
              />
            </div>

            <div className="form-group">
              <label htmlFor="cv">Upload CV (PDF, DOC, DOCX) *</label>
              <div className="file-input-container">
                <div className="file-input-button">Choose File</div>
                <span className="file-name">{fileName}</span>
                <input
                  type="file"
                  id="cv"
                  name="cv"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  required
                  className="file-input"
                />
              </div>
              {fileError && <div className="error-message" style={{ color: 'red', fontSize: '0.875rem', marginTop: '0.25rem' }}>{fileError}</div>}
            </div>

            <div className="form-actions">
              <button type="button" className="btn cancel-btn" onClick={onClose}>
                Cancel
              </button>
              <button type="submit" className="btn primary-btn submit-btn" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Submit Application"}{" "}
                {!isSubmitting && <span className="arrow">→</span>}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}

export default ApplicationModal

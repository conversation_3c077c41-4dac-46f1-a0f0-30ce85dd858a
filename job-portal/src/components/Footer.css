.footer {
  background-color: var(--color-background-alt);
  color: var(--color-text);
  padding: 80px 0 40px;
  border-top: 1px solid var(--color-border);
}

.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 5vw;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-column h3 {
  color: var(--color-text);
  margin-bottom: 24px;
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.footer-column p {
  color: var(--color-text);
  opacity: 0.8;
  line-height: 1.6;
  font-size: 1rem;
}

.footer-logo {
  margin-bottom: 24px;
}

.footer-links {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-links li {
  margin: 0;
}

.footer-links a {
  color: var(--color-text);
  opacity: 0.8;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
  padding: 4px 0;
}

.footer-links a::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--color-accent);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.footer-links a:hover {
  color: var(--color-accent);
  opacity: 1;
}

.footer-links a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.social-icons {
  display: flex;
  gap: 16px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--color-background);
  color: var(--color-text);
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
}

.social-icons a:hover {
  background-color: var(--color-accent);
  color: #FFFFFF;
  border-color: var(--color-accent);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 32px;
  margin-top: 32px;
  border-top: 1px solid var(--color-border);
  color: var(--color-text);
  opacity: 0.7;
  font-size: 0.9rem;
}

[data-theme='dark'] .footer {
  background-color: var(--color-background);
  border-top-color: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .social-icons a {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .social-icons a:hover {
  background-color: var(--color-accent);
  border-color: var(--color-accent);
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 60px;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 60px 0 32px;
  }

  .container {
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 40px 0 24px;
  }

  .footer-grid {
    gap: 32px;
  }

  .social-icons {
    justify-content: center;
  }
}

.address {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 16px;
}

.address-icon {
  color: var(--color-accent);
  margin-top: 4px;
}

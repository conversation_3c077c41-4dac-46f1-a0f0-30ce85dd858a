.header {
  background-color: var(--color-background);
  padding: 20px 0;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.3s, border-color 0.3s;
  position: relative;
  z-index: 1000;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 5vw;
  position: relative;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--color-text);
}

.logo-link {
  text-decoration: none;
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: none;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--color-text);
  cursor: pointer;
  padding: 8px;
  z-index: 1001;
  transition: color 0.3s ease;
  position: relative;
}

.mobile-menu-button:hover {
  color: var(--color-accent);
}

/* Mobile Menu Overlay */
.menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-overlay.show {
  display: block;
  opacity: 1;
}

/* Navigation */
.main-nav {
  display: flex;
  align-items: center;
  z-index: 1000;
}

@media (max-width: 767px) {
  .mobile-menu-button {
    display: flex;
  }

  .main-nav {
    display: none;
    position: fixed;
    top: 80px;
    left: 20px;
    right: 20px;
    background-color: var(--color-background);
    padding: 8px 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    flex-direction: column;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    z-index: 1000;
    margin-top: 8px;
  }

  .main-nav.show {
    display: flex;
  }

  .main-nav a {
    color: var(--color-text);
    font-weight: 500;
    text-decoration: none;
    padding: 12px 20px;
    transition: all 0.3s ease;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    border: none;
  }

  .main-nav a:hover {
    color: var(--color-accent);
    opacity: 1;
    background-color: var(--color-accent-light);
  }

  .main-nav a.active {
    color: var(--color-accent);
    opacity: 1;
    background-color: var(--color-accent-light);
  }

  .main-nav a svg {
    width: 18px;
    height: 18px;
  }
}

@media (min-width: 768px) {
  .main-nav {
    margin: 0 20px;
  }

  .main-nav a {
    margin-left: 30px;
    padding: 8px 12px;
    position: relative;
    color: var(--color-text);
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
    opacity: 0.9;
  }

  .main-nav a:first-child {
    margin-left: 0;
  }

  .main-nav a:hover {
    color: var(--color-accent);
    opacity: 1;
  }

  .main-nav a.active {
    color: var(--color-accent);
    opacity: 1;
  }

  .main-nav a::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-accent);
    transition: width 0.3s ease;
  }

  .main-nav a:hover::after,
  .main-nav a.active::after {
    width: 100%;
  }

  .main-nav a svg {
    display: none;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

/* Primary Button Style */
.talk-btn {
  background-color: var(--color-accent);
  color: #FFFFFF;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.talk-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.talk-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background-color: var(--color-accent-dark);
  color: #FFFFFF;
}

.talk-btn:hover::before {
  transform: translateX(100%);
}

.arrow {
  transition: transform 0.3s ease;
}

.talk-btn:hover .arrow {
  transform: translateX(4px);
}

/* View Position Button Style */
.view-position-btn {
  background-color: transparent;
  color: var(--color-text);
  border: 2px solid var(--color-border);
  border-radius: 4px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.view-position-btn:hover {
  border-color: var(--color-accent);
  color: var(--color-accent);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background-color: rgba(var(--color-accent-rgb), 0.1);
}

[data-theme='dark'] .view-position-btn {
  color: #FFFFFF;
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme='dark'] .view-position-btn:hover {
  border-color: var(--color-accent);
  color: var(--color-accent);
  background-color: rgba(var(--color-accent-rgb), 0.15);
}

[data-theme='dark'] .main-nav a {
  color: rgba(255, 255, 255, 0.9);
}

[data-theme='dark'] .main-nav a:hover,
[data-theme='dark'] .main-nav a.active {
  color: var(--color-accent);
}

[data-theme='dark'] .menu-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}

/* Responsive styles */
@media (max-width: 768px) {
  .header-container {
    padding: 0 20px;
  }

  .header-actions {
    gap: 16px;
  }

  .talk-btn,
  .view-position-btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header-actions {
    gap: 12px;
  }

  .talk-btn,
  .view-position-btn {
    padding: 8px 16px;
  }
}

.external-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  color: var(--color-text);
  text-decoration: none;
  transition: all 0.3s ease;
  background-color: var(--color-accent);
  color: white;
  border-radius: 30px;
  font-weight: 500;
  margin-left: 1rem;
}

.external-link:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: var(--color-accent-dark);
}

@media (max-width: 768px) {
  .external-link {
    margin: 0.5rem 1rem;
    justify-content: center;
    border-bottom: none;
  }
}

[data-theme='dark'] .external-link {
  color: white;
}

[data-theme='dark'] .external-link:hover {
  color: white;
  background-color: var(--color-accent-dark);
}


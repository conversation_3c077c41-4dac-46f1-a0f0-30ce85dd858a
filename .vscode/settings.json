{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.tsdk": "node_modules/typescript/lib", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": false, "**/dist": false}, "search.exclude": {"**/node_modules": true, "**/dist": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"]}
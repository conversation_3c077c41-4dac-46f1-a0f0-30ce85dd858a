{"version": "2.0.0", "tasks": [{"label": "Build", "type": "shell", "command": "npm run build", "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}, {"label": "Start Development", "type": "shell", "command": "npm run start:dev", "group": "none", "problemMatcher": []}, {"label": "Start Debug", "type": "shell", "command": "npm run start:debug", "group": "none", "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "npm run test", "group": {"kind": "test", "isDefault": true}, "problemMatcher": []}, {"label": "Run E2E Tests", "type": "shell", "command": "npm run test:e2e", "group": "test", "problemMatcher": []}]}
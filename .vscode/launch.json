{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS Application", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start:debug"], "console": "integratedTerminal", "restart": true, "sourceMaps": true, "skipFiles": ["<node_internals>/**"], "outFiles": ["${workspaceFolder}/job-backend/dist/**/*.js"], "cwd": "${workspaceFolder}/job-backend"}, {"type": "node", "request": "launch", "name": "Debug E2E Tests", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:e2e:debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "cwd": "${workspaceFolder}/job-backend"}, {"type": "node", "request": "launch", "name": "Debug Unit Tests", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "cwd": "${workspaceFolder}/job-backend"}]}